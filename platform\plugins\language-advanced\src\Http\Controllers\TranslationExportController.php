<?php

namespace Bo<PERSON>ble\LanguageAdvanced\Http\Controllers;

use Bo<PERSON>ble\DataSynchronize\Exporter\Exporter;
use Bo<PERSON>ble\DataSynchronize\Http\Controllers\ExportController;
use Bo<PERSON>ble\LanguageAdvanced\Exporters\TranslationExporterManager;

class TranslationExportController extends ExportController
{
    protected TranslationExporterManager $exporterManager;

    protected ?string $type;

    public function __construct(TranslationExporterManager $exporterManager)
    {
        $this->exporterManager = $exporterManager;
        $this->type = request()->route('type');
    }

    protected function getExporter(): Exporter
    {
        return $this->exporterManager->getExporter($this->type);
    }
}
