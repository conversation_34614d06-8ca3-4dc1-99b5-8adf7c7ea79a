<?php

return [
    'settings' => [
        'title' => 'Sitemap',
        'description' => 'Manage sitemap configuration',
        'enable_sitemap' => 'Enable sitemap?',
        'enable_sitemap_help' => 'When enabled, a sitemap.xml file will be automatically generated and accessible at :url to help search engines better index your site.',
        'enable_stores_sitemap' => 'Enable stores sitemap?',
        'enable_stores_sitemap_help' => 'Include store pages in the sitemap to improve their visibility in search engines.',
        'enable_blog_tags_sitemap' => 'Enable blog tags sitemap?',
        'enable_blog_tags_sitemap_help' => 'Include blog tag pages in the sitemap to help search engines discover your blog content.',
        'sitemap_items_per_page' => 'Sitemap items per page',
        'sitemap_items_per_page_help' => 'The number of items to include in each sitemap page. Larger values may improve sitemap generation performance but could cause issues with very large sites. Default: 1000',
        'indexnow_enabled' => 'Enable IndexNow?',
        'indexnow_enabled_help' => 'Automatically notify search engines (Bing, Yandex, Seznam, Naver) when your content is updated using the modern IndexNow protocol for instant indexing.',
        'indexnow_api_key' => 'IndexNow API Key',
        'indexnow_api_key_help' => 'A unique UUID that identifies your site to search engines. Generate one automatically or use your own.',
        'indexnow_no_key' => 'No API Key Generated',
        'indexnow_no_key_help' => 'Generate an API key to start using IndexNow for instant search engine indexing.',
        'indexnow_ready' => 'IndexNow is Ready!',
        'indexnow_ready_help' => 'Your API key and key file are properly configured. IndexNow will automatically notify search engines when your content is updated.',
        'key_file_invalid' => 'API Key File Invalid',
        'key_file_invalid_help' => 'The key file exists but contains incorrect content. Click "Regenerate Key" to fix this issue.',
        'key_file_missing' => 'API Key File Missing',
        'key_file_missing_help' => 'The API key file was not found in your public directory. Click "Create Key File" to generate it automatically.',
        'key_file_name' => 'File Name',
        'key_file_content' => 'File Content',
        'test_key_file' => 'Test Key File',
        'setup_instructions' => 'Setup Instructions',
        'hide_instructions' => 'Hide Instructions',
        'setup_steps' => 'IndexNow Setup Steps',
        'generate_key_command' => 'Or generate via command line: ',
        'generate_api_key' => 'Generate API Key',
        'generating' => 'Generating...',
        'regenerate_api_key' => 'Regenerate Key',
        'regenerating' => 'Regenerating...',
        'create_key_file' => 'Create Key File',
        'creating_key_file' => 'Creating...',
        'regenerate_confirm' => 'Are you sure you want to regenerate the API key? This will create a new key file and the old one will be removed.',
        'api_key_generated' => 'API key generated successfully!',
        'key_file_created' => 'Key file created automatically.',
        'key_file_created_invalid' => 'Key file created but content validation failed.',
        'key_file_creation_failed' => 'Warning: Key file creation failed. You may need to create it manually.',
        'generate_key_error' => 'Failed to generate API key. Please try again.',
        'no_api_key_to_create_file' => 'No API key found. Please generate an API key first.',
        'key_file_created_successfully' => 'Key file created successfully!',
        'key_file_created_but_invalid' => 'Key file created but content validation failed.',
        'key_file_creation_failed_detailed' => 'Failed to create key file. Please check file permissions.',
        'key_file_creation_error' => 'Failed to create key file. Please try again.',
        'indexnow_info' => 'IndexNow Information & Setup Guide',
        'automatic_setup' => 'Automatic Setup',
        'automatic_setup_desc' => 'Everything is handled automatically - no manual file creation needed',
        'step_generate' => 'Generate Key',
        'step_create_file' => 'Create File',
        'step_ready' => 'Ready to Use',
        'auto_step_1' => 'API key is generated automatically when you click the button',
        'auto_step_2' => 'Key file ":filename" is created automatically in your public directory',
        'auto_step_3' => 'File is accessible at :url for search engine verification',
        'supported_engines' => 'Supported Search Engines',
        'supported_engines_desc' => 'IndexNow works with multiple search engines for better coverage',
        'fully_supported' => 'Fully supported',
        'indexnow_benefits' => 'Key Benefits',
        'indexnow_benefits_desc' => 'Why IndexNow is better than traditional sitemap pinging',
        'benefit_instant' => 'Instant indexing notifications',
        'benefit_automatic' => 'Automatic content updates',
        'benefit_multiple' => 'Multiple search engines',
        'benefit_reliable' => 'Modern, reliable protocol',
        'about_google' => 'About Google',
        'google_note' => 'Google deprecated their sitemap ping service in June 2023 and now relies on natural crawling and lastmod tags.',
        'sitemap_info_title' => 'How Sitemap Works',
        'sitemap_info_description' => 'Your sitemap is automatically generated and updated whenever content changes. It helps search engines discover and index your website content more efficiently.',
        'sitemap_url' => 'Sitemap URL',
        'view_sitemap' => 'View Sitemap',
        'automatic_generation' => 'Automatic Generation',
        'automatic_generation_desc' => 'Updates automatically when content changes',
        'automatic_update_note' => 'The sitemap updates automatically whenever you create, edit, or delete content on your website.',
    ],
];
