.swatches-container {
    .header {
        display: flex;
        flex-direction: row;
        font-weight: 700;
        background-color: var(--bb-body-bg);
        color: var(--bb-body-color);
        border-bottom: var(--bb-border-width) var(--bb-border-style) var(--bb-border-color);

        > * {
            float: left;
            padding: 10px;
            text-align: center;
        }
    }

    .swatches-list {
        float: left;
        list-style: none;
        margin: 0 0 15px;
        padding: 0;
        width: 100%;
        position: relative;

        li {
            display: flex;
            flex-direction: row;
            align-items: center;
            position: relative;

            + li {
                margin-top: 1px;
            }

            &:nth-child(odd) {
                background-color: var(--bb-body-bg);
            }

            > * {
                float: left;
                padding: 10px;
                text-align: center;
            }

            .image-box-container {
                width: 34px;
                margin: 0 auto;
                font-size: 0;

                img {
                    border: 1px solid #cccccc;
                    width: 34px;
                    height: 34px;
                }
            }
        }

        #loading-update-currencies {
            .currency-loading-backdrop {
                position: absolute;
                background-color: gray;
                opacity: 0.3;
                z-index: 9998;
                width: 100%;
                height: 100%;
            }

            .currency-loading-loader {
                position: absolute;
                border: 5px solid #f3f3f3;
                border-top: 5px solid #3498db;
                border-radius: 50%;
                right: 50%;
                top: 40%;
                z-index: 9999;
                width: 30px;
                height: 30px;
                animation: spin 2s linear infinite;
            }
        }
    }

    .swatch-item {
        flex: 1;
        max-width: 90px;
    }

    .swatch-is-default {
        width: 120px;
    }

    .remove-item {
        width: 80px;
    }

    .swatch-exchange-rate,
    .swatch-is-prefix-symbol,
    .swatch-decimals {
        max-width: 180px;
    }
}

// Mobile responsive styles
@media (max-width: 768px) {
    .swatches-container {
        .header {
            display: none;
        }

        .swatches-list {
            li {
                display: block;
                background-color: var(--bb-body-bg) !important;
                border: var(--bb-border-width) var(--bb-border-style) var(--bb-border-color);
                border-radius: 8px;
                margin-bottom: 15px;
                padding: 15px;

                + li {
                    margin-top: 15px;
                }

                > * {
                    float: none;
                    padding: 8px 0;
                    text-align: left;
                    width: 100% !important;
                    max-width: none !important;
                    display: block;
                }

                .swatch-item,
                .swatch-is-default,
                .remove-item {
                    position: relative;
                    margin-bottom: 12px;

                    &::before {
                        content: attr(data-label);
                        display: block;
                        font-weight: 600;
                        margin-bottom: 5px;
                        color: var(--bb-body-color);
                        font-size: 14px;
                    }

                    .form-control,
                    .form-select {
                        width: 100%;
                    }
                }



                .remove-item {
                    text-align: center;
                    margin-top: 15px;
                    padding-top: 15px;
                    border-top: var(--bb-border-width) var(--bb-border-style) var(--bb-border-color);

                    &::before {
                        display: none;
                    }
                }
            }
        }
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}
