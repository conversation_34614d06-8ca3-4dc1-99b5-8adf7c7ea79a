<?php

namespace Bo<PERSON><PERSON>\HyperPay\Tests;

use Botble\HyperPay\Services\Gateways\HyperPayPaymentService;
use <PERSON><PERSON>ble\HyperPay\Supports\HyperPayHelper;
use Tests\TestCase;

class HyperPayIntegrationTest extends TestCase
{
    protected HyperPayPaymentService $hyperPayService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->hyperPayService = new HyperPayPaymentService();
    }

    public function testHyperPayServiceInstantiation()
    {
        $this->assertInstanceOf(HyperPayPaymentService::class, $this->hyperPayService);
    }

    public function testSupportedCurrencies()
    {
        $currencies = $this->hyperPayService->supportedCurrencyCodes();
        
        $this->assertIsArray($currencies);
        $this->assertContains('SAR', $currencies);
        $this->assertContains('AED', $currencies);
        $this->assertContains('USD', $currencies);
        $this->assertContains('EUR', $currencies);
    }

    public function testHelperFunctions()
    {
        // Test amount formatting
        $formattedAmount = HyperPayHelper::formatAmount(123.456);
        $this->assertEquals('123.46', $formattedAmount);

        // Test supported currencies
        $currencies = HyperPayHelper::getSupportedCurrencies();
        $this->assertIsArray($currencies);
        $this->assertArrayHasKey('SAR', $currencies);

        // Test payment type brands
        $brands = HyperPayHelper::getPaymentTypeBrands();
        $this->assertIsArray($brands);
        $this->assertArrayHasKey('visa', $brands);
        $this->assertArrayHasKey('mada', $brands);
    }

    public function testResultCodeValidation()
    {
        // Test successful payment code
        $successResult = HyperPayHelper::validateResultCode('000.000.100');
        $this->assertTrue($successResult['is_success']);
        $this->assertEquals('success', $successResult['status']);

        // Test pending payment code
        $pendingResult = HyperPayHelper::validateResultCode('000.200.000');
        $this->assertTrue($pendingResult['is_pending']);
        $this->assertEquals('pending', $pendingResult['status']);

        // Test failed payment code
        $failedResult = HyperPayHelper::validateResultCode('800.400.100');
        $this->assertFalse($failedResult['is_success']);
        $this->assertFalse($failedResult['is_pending']);
        $this->assertEquals('failed', $failedResult['status']);
    }

    public function testConfigurationValidation()
    {
        // Test configuration status
        $status = HyperPayHelper::getConfigurationStatus();
        $this->assertIsArray($status);
        $this->assertArrayHasKey('configured', $status);
        $this->assertArrayHasKey('missing', $status);
        $this->assertArrayHasKey('warnings', $status);
    }

    public function testCustomerDataSanitization()
    {
        $testData = [
            'name' => 'John Doe!@#',
            'email' => '<EMAIL>',
            'address' => '123 Main St. #456',
            'city' => 'Riyadh@#$',
            'country' => 'SA',
            'postcode' => '12345-6789'
        ];

        $sanitized = HyperPayHelper::sanitizeCustomerData($testData);

        $this->assertEquals('John Doe', $sanitized['name']);
        $this->assertEquals('<EMAIL>', $sanitized['email']);
        $this->assertEquals('123 Main St. 456', $sanitized['address']);
        $this->assertEquals('Riyadh', $sanitized['city']);
        $this->assertEquals('SA', $sanitized['country']);
        $this->assertEquals('12345-6789', $sanitized['postcode']);
    }

    public function testPaymentServiceMethods()
    {
        // Test availability check
        $isAvailable = $this->hyperPayService->isAvailable();
        $this->assertIsBool($isAvailable);

        // Test supported payment types
        $paymentTypes = $this->hyperPayService->getSupportedPaymentTypes();
        $this->assertIsArray($paymentTypes);
        $this->assertArrayHasKey('visa', $paymentTypes);
        $this->assertArrayHasKey('mada', $paymentTypes);
        $this->assertArrayHasKey('amex', $paymentTypes);
        $this->assertArrayHasKey('applepay', $paymentTypes);

        // Test API URL generation
        $apiUrl = $this->hyperPayService->getApiUrl();
        $this->assertStringContains('oppwa.com', $apiUrl);
    }

    public function testEntityIdRetrieval()
    {
        // Test entity ID retrieval for different payment types
        $visaEntityId = HyperPayHelper::getEntityIdForPaymentType('visa');
        $madaEntityId = HyperPayHelper::getEntityIdForPaymentType('mada');
        $amexEntityId = HyperPayHelper::getEntityIdForPaymentType('amex');
        $applePayEntityId = HyperPayHelper::getEntityIdForPaymentType('applepay');

        // These might be null if not configured, which is expected in test environment
        $this->assertTrue(is_null($visaEntityId) || is_string($visaEntityId));
        $this->assertTrue(is_null($madaEntityId) || is_string($madaEntityId));
        $this->assertTrue(is_null($amexEntityId) || is_string($amexEntityId));
        $this->assertTrue(is_null($applePayEntityId) || is_string($applePayEntityId));
    }

    public function testMerchantTransactionIdGeneration()
    {
        $transactionId1 = HyperPayHelper::generateMerchantTransactionId();
        $transactionId2 = HyperPayHelper::generateMerchantTransactionId('test');

        $this->assertStringStartsWith('order_', $transactionId1);
        $this->assertStringStartsWith('test_', $transactionId2);
        $this->assertNotEquals($transactionId1, $transactionId2);
    }

    public function testWebhookSignatureValidation()
    {
        $payload = '{"test": "data"}';
        $secret = 'test_secret';
        $validSignature = hash_hmac('sha256', $payload, $secret);
        $invalidSignature = 'invalid_signature';

        $this->assertTrue(HyperPayHelper::validateWebhookSignature($payload, $validSignature, $secret));
        $this->assertFalse(HyperPayHelper::validateWebhookSignature($payload, $invalidSignature, $secret));
    }

    public function testEnvironmentConfiguration()
    {
        // Test sandbox mode detection
        $isSandbox = HyperPayHelper::isSandboxMode();
        $this->assertIsBool($isSandbox);

        // Test 3DS configuration
        $is3DSEnabled = HyperPayHelper::is3DSEnabled();
        $this->assertIsBool($is3DSEnabled);

        // Test API base URL
        $apiBaseUrl = HyperPayHelper::getApiBaseUrl();
        $this->assertStringContains('oppwa.com', $apiBaseUrl);

        // Test widget script URL
        $scriptUrl = HyperPayHelper::getWidgetScriptUrl();
        $this->assertStringContains('paymentWidgets.js', $scriptUrl);
    }
}
