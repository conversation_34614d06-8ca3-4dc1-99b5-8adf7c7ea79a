<?php

namespace Bo<PERSON>ble\Tabby;

use Bo<PERSON><PERSON>\PluginManagement\Abstracts\PluginOperationAbstract;
use <PERSON><PERSON><PERSON>\Setting\Facades\Setting;
use Bo<PERSON><PERSON>\Tabby\Services\TabbyWebhookService;

class Plugin extends PluginOperationAbstract
{
    public static function activate(): void
    {
        // Auto setup webhook if in production environment
        if (get_payment_setting('environment', TABBY_PAYMENT_METHOD_NAME) === 'live') {
            $webhookService = new TabbyWebhookService();
            $webhookService->setupWebhook();
        }
    }

    public static function remove(): void
    {
        // Remove webhook before deleting settings
        try {
            $webhookService = new TabbyWebhookService();
            $webhookService->removeWebhook();
        } catch (\Exception $e) {
            // Ignore webhook removal errors during plugin removal
        }

        Setting::delete([
            'payment_tabby_status',
            'payment_tabby_public_key',
            'payment_tabby_secret_key',
            'payment_tabby_merchant_code',
            'payment_tabby_environment',
            'payment_tabby_webhook_id',
            'payment_tabby_webhook_secret',
        ]);
    }
}
