<?php

return [
    'name' => 'HyperPay',
    'description' => 'Secure payment processing with HyperPay gateway supporting Visa, Mastercard, Mada, American Express, and Apple Pay',
    
    // Configuration
    'access_token' => 'Access Token',
    'access_token_helper' => 'Your HyperPay access token (Bearer token) from the merchant portal',
    'visa_entity_id' => 'Visa/Mastercard Entity ID',
    'visa_entity_id_helper' => 'Entity ID for Visa and Mastercard payments',
    'mada_entity_id' => 'Mada Entity ID',
    'mada_entity_id_helper' => 'Entity ID for Mada payments (Saudi local payment method)',
    'amex_entity_id' => 'American Express Entity ID',
    'amex_entity_id_helper' => 'Entity ID for American Express payments',
    'applepay_entity_id' => 'Apple Pay Entity ID',
    'applepay_entity_id_helper' => 'Entity ID for Apple Pay payments',
    'sandbox_mode' => 'Sandbox Mode',
    'sandbox_mode_helper' => 'Enable for testing. Disable for live transactions.',
    '3ds_enabled' => '3D Secure Enabled',
    '3ds_enabled_helper' => 'Enable 3D Secure authentication for enhanced security',
    'currency' => 'Default Currency',
    'currency_helper' => 'Default currency for HyperPay transactions',
    'webhook_secret' => 'Webhook Secret',
    'webhook_secret_helper' => 'Secret key for webhook signature verification (optional but recommended)',
    
    // Instructions
    'instructions_title' => 'HyperPay Setup Instructions',
    'configuration_steps' => 'Configuration Steps',
    'step_1' => 'Sign up for a HyperPay merchant account at hyperpay.com',
    'step_2' => 'Obtain your access token from the HyperPay merchant portal',
    'step_3' => 'Get entity IDs for each payment method you want to support',
    'step_4' => 'Configure webhook URLs in your HyperPay account (optional)',
    'step_5' => 'Test in sandbox mode before going live',
    
    'supported_features' => 'Supported Features',
    'feature_visa_mastercard' => 'Visa & Mastercard payments',
    'feature_mada' => 'Mada (Saudi local payment)',
    'feature_amex' => 'American Express',
    'feature_applepay' => 'Apple Pay',
    'feature_3ds' => '3D Secure authentication',
    'feature_webhooks' => 'Webhook notifications',
    
    'sandbox_warning' => 'Remember to disable sandbox mode for live transactions!',
    'documentation_link' => 'For detailed setup instructions, visit the',
    'documentation' => 'HyperPay Documentation',
    
    // Payment Interface
    'select_payment_type' => 'Select Payment Method',
    'payment_info' => 'Your payment information is secure and encrypted.',
    'secure_payment' => 'Secure Payment',
    'secure_payment_description' => 'Complete your payment securely with HyperPay',
    'powered_by' => 'Powered by',
    'processing' => 'Processing',
    'go_back' => 'Go Back',
    
    // Checkout
    'checkout_title' => 'HyperPay Checkout',
    'checkout_error' => 'Checkout Error',
    'checkout_error_description' => 'Unable to initialize payment. Please try again or contact support.',
    
    // Payment Details
    'payment_details' => 'Payment Details',
    'transaction_id' => 'Transaction ID',
    'amount' => 'Amount',
    'payment_type' => 'Payment Type',
    'result_code' => 'Result Code',
    'result_description' => 'Result Description',
    'card_details' => 'Card Details',
    'card_bin' => 'Card BIN',
    'card_last4' => 'Last 4 Digits',
    'card_holder' => 'Card Holder',
    'card_expiry' => 'Expiry Date',
    'processed_at' => 'Processed At',
    'merchant_transaction_id' => 'Merchant Transaction ID',
    'no_payment_details' => 'No payment details available',
    
    // Messages
    'payment_success' => 'Payment completed successfully!',
    'payment_failed' => 'Payment failed. Please try again.',
    'payment_error' => 'An error occurred while processing your payment.',
    'payment_pending' => 'Your payment is being processed. You will be notified once completed.',
    'configuration_incomplete' => 'HyperPay configuration is incomplete. Please check your settings.',
    'invalid_currency' => 'Currency not supported by HyperPay.',
    'invalid_amount' => 'Invalid payment amount.',
    
    // Error Messages
    'error_access_token_missing' => 'HyperPay access token is not configured',
    'error_entity_id_missing' => 'Entity ID is not configured for the selected payment method',
    'error_checkout_creation_failed' => 'Failed to create checkout session',
    'error_payment_processing_failed' => 'Payment processing failed',
    'error_invalid_signature' => 'Invalid webhook signature',
    'error_missing_parameters' => 'Missing required parameters',
    'error_api_communication' => 'Communication error with HyperPay API',
    
    // Status Messages
    'status_success' => 'Success',
    'status_pending' => 'Pending',
    'status_failed' => 'Failed',
    'status_cancelled' => 'Cancelled',
    'status_refunded' => 'Refunded',
    
    // Validation Messages
    'validation_access_token_required' => 'Access token is required',
    'validation_entity_id_required' => 'At least one entity ID must be configured',
    'validation_currency_invalid' => 'Selected currency is not supported',
    'validation_amount_invalid' => 'Amount must be greater than zero',
    
    // Admin Interface
    'settings_title' => 'HyperPay Settings',
    'settings_description' => 'Configure HyperPay payment gateway settings',
    'test_connection' => 'Test Connection',
    'connection_successful' => 'Connection to HyperPay API successful',
    'connection_failed' => 'Failed to connect to HyperPay API',
    
    // Webhook Messages
    'webhook_received' => 'Webhook notification received',
    'webhook_processed' => 'Webhook processed successfully',
    'webhook_failed' => 'Webhook processing failed',
    'webhook_invalid_signature' => 'Invalid webhook signature',
    
    // Payment Types
    'payment_type_visa' => 'Visa/Mastercard',
    'payment_type_mada' => 'Mada',
    'payment_type_amex' => 'American Express',
    'payment_type_applepay' => 'Apple Pay',
];
