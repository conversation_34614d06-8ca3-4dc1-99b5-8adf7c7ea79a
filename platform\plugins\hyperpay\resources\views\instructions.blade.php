<div class="hyperpay-instructions">
    <h5>{{ trans('plugins/hyperpay::hyperpay.instructions_title') }}</h5>
    
    <div class="row">
        <div class="col-md-6">
            <h6>{{ trans('plugins/hyperpay::hyperpay.configuration_steps') }}</h6>
            <ol>
                <li>{{ trans('plugins/hyperpay::hyperpay.step_1') }}</li>
                <li>{{ trans('plugins/hyperpay::hyperpay.step_2') }}</li>
                <li>{{ trans('plugins/hyperpay::hyperpay.step_3') }}</li>
                <li>{{ trans('plugins/hyperpay::hyperpay.step_4') }}</li>
                <li>{{ trans('plugins/hyperpay::hyperpay.step_5') }}</li>
            </ol>
        </div>
        
        <div class="col-md-6">
            <h6>{{ trans('plugins/hyperpay::hyperpay.supported_features') }}</h6>
            <ul>
                <li><i class="fas fa-check text-success"></i> {{ trans('plugins/hyperpay::hyperpay.feature_visa_mastercard') }}</li>
                <li><i class="fas fa-check text-success"></i> {{ trans('plugins/hyperpay::hyperpay.feature_mada') }}</li>
                <li><i class="fas fa-check text-success"></i> {{ trans('plugins/hyperpay::hyperpay.feature_amex') }}</li>
                <li><i class="fas fa-check text-success"></i> {{ trans('plugins/hyperpay::hyperpay.feature_applepay') }}</li>
                <li><i class="fas fa-check text-success"></i> {{ trans('plugins/hyperpay::hyperpay.feature_3ds') }}</li>
                <li><i class="fas fa-check text-success"></i> {{ trans('plugins/hyperpay::hyperpay.feature_webhooks') }}</li>
            </ul>
        </div>
    </div>
    
    <div class="alert alert-warning mt-3">
        <i class="fas fa-exclamation-triangle"></i>
        {{ trans('plugins/hyperpay::hyperpay.sandbox_warning') }}
    </div>
    
    <div class="alert alert-info mt-2">
        <i class="fas fa-info-circle"></i>
        {{ trans('plugins/hyperpay::hyperpay.documentation_link') }}
        <a href="https://hyperpay.docs.oppwa.com/" target="_blank">{{ trans('plugins/hyperpay::hyperpay.documentation') }}</a>
    </div>
</div>
