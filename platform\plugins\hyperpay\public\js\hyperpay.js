/**
 * HyperPay Payment Gateway JavaScript
 */

class HyperPayPayment {
    constructor() {
        this.checkoutId = null;
        this.scriptUrl = null;
        this.shopperResultUrl = null;
        this.paymentType = 'visa';
        this.isProcessing = false;
        
        this.init();
    }

    init() {
        this.bindEvents();
        this.setupPaymentTypeSelection();
    }

    bindEvents() {
        // Payment type selection
        document.addEventListener('change', (e) => {
            if (e.target.name === 'hyperpay_payment_type') {
                this.handlePaymentTypeChange(e.target.value);
            }
        });

        // Form submission handling
        document.addEventListener('submit', (e) => {
            if (e.target.classList.contains('paymentWidgets')) {
                this.handlePaymentSubmission(e);
            }
        });

        // Checkout initialization
        if (window.hyperPayCheckoutData) {
            this.initializeCheckout(window.hyperPayCheckoutData);
        }
    }

    setupPaymentTypeSelection() {
        const paymentTypeInputs = document.querySelectorAll('input[name="hyperpay_payment_type"]');
        
        paymentTypeInputs.forEach(input => {
            input.addEventListener('change', () => {
                this.updatePaymentTypeDisplay(input.value);
            });
        });

        // Set initial payment type
        const checkedInput = document.querySelector('input[name="hyperpay_payment_type"]:checked');
        if (checkedInput) {
            this.paymentType = checkedInput.value;
        }
    }

    handlePaymentTypeChange(paymentType) {
        this.paymentType = paymentType;
        console.log('Payment type changed to:', paymentType);
        
        // Update UI if needed
        this.updatePaymentTypeDisplay(paymentType);
        
        // Trigger custom event
        document.dispatchEvent(new CustomEvent('hyperPayTypeChanged', {
            detail: { paymentType }
        }));
    }

    updatePaymentTypeDisplay(paymentType) {
        // Update any visual indicators based on payment type
        const container = document.querySelector('.hyperpay-payment-form');
        if (container) {
            container.setAttribute('data-payment-type', paymentType);
        }
    }

    initializeCheckout(checkoutData) {
        this.checkoutId = checkoutData.checkoutId;
        this.scriptUrl = checkoutData.scriptUrl;
        this.shopperResultUrl = checkoutData.shopperResultUrl;
        
        if (this.checkoutId && this.scriptUrl) {
            this.loadPaymentWidget();
        }
    }

    loadPaymentWidget() {
        const container = document.getElementById('hyperpay-checkout-container');
        if (!container) {
            console.error('HyperPay checkout container not found');
            return;
        }

        // Show loading state
        this.showLoading(container);

        // Load HyperPay script
        const script = document.createElement('script');
        script.src = this.scriptUrl;
        script.onload = () => {
            this.renderPaymentForm(container);
        };
        script.onerror = () => {
            this.showError(container, 'Failed to load payment widget');
        };
        
        document.head.appendChild(script);
    }

    renderPaymentForm(container) {
        // Clear loading state
        container.innerHTML = '';
        
        // Create payment form
        const form = document.createElement('form');
        form.action = this.shopperResultUrl;
        form.className = 'paymentWidgets';
        form.setAttribute('data-brands', this.getPaymentBrands());
        
        container.appendChild(form);
        container.style.display = 'block';
        
        // Add security indicator
        this.addSecurityIndicator(container);
    }

    getPaymentBrands() {
        const brandMap = {
            'visa': 'VISA MASTER',
            'mada': 'MADA',
            'amex': 'AMEX',
            'applepay': 'APPLEPAY'
        };
        
        return brandMap[this.paymentType] || 'VISA MASTER';
    }

    handlePaymentSubmission(event) {
        if (this.isProcessing) {
            event.preventDefault();
            return;
        }

        this.isProcessing = true;
        
        // Show processing state
        this.showProcessingState(event.target);
        
        // Add timeout to reset processing state
        setTimeout(() => {
            this.isProcessing = false;
        }, 30000); // 30 seconds timeout
    }

    showProcessingState(form) {
        const submitButton = form.querySelector('button[type="submit"]');
        if (submitButton) {
            submitButton.disabled = true;
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
        }
        
        // Add processing class to form
        form.classList.add('processing');
    }

    showLoading(container) {
        container.innerHTML = `
            <div class="hyperpay-loading">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">Loading...</span>
                </div>
                <p class="mt-3">Loading secure payment form...</p>
            </div>
        `;
        container.style.display = 'block';
    }

    showError(container, message) {
        container.innerHTML = `
            <div class="hyperpay-error">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>Error:</strong> ${message}
            </div>
        `;
        container.style.display = 'block';
    }

    addSecurityIndicator(container) {
        const indicator = document.createElement('div');
        indicator.className = 'security-indicator';
        indicator.innerHTML = `
            <i class="fas fa-lock"></i>
            <span>Your payment is secured with SSL encryption</span>
        `;
        container.appendChild(indicator);
    }

    // Public methods for external use
    static getInstance() {
        if (!window.hyperPayInstance) {
            window.hyperPayInstance = new HyperPayPayment();
        }
        return window.hyperPayInstance;
    }

    static initializeWithData(checkoutData) {
        const instance = HyperPayPayment.getInstance();
        instance.initializeCheckout(checkoutData);
        return instance;
    }

    static setPaymentType(paymentType) {
        const instance = HyperPayPayment.getInstance();
        instance.handlePaymentTypeChange(paymentType);
    }
}

// Auto-initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    HyperPayPayment.getInstance();
});

// Expose to global scope
window.HyperPayPayment = HyperPayPayment;

// Utility functions
window.HyperPayUtils = {
    formatAmount: (amount) => {
        return parseFloat(amount).toFixed(2);
    },
    
    validatePaymentData: (data) => {
        const required = ['amount', 'currency'];
        return required.every(field => data[field] && data[field].toString().trim() !== '');
    },
    
    showNotification: (message, type = 'info') => {
        // Simple notification system
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} hyperpay-notification`;
        notification.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'}-circle"></i>
            ${message}
        `;
        
        document.body.appendChild(notification);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 5000);
    }
};
