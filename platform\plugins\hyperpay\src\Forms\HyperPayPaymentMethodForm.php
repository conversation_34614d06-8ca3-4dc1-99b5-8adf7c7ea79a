<?php

namespace Bo<PERSON><PERSON>\HyperPay\Forms;

use Bo<PERSON>ble\Base\Facades\BaseHelper;
use Bo<PERSON>ble\Base\Forms\FieldOptions\CheckboxFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\SelectFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\TextFieldOption;
use Botble\Base\Forms\Fields\CheckboxField;
use Botble\Base\Forms\Fields\SelectField;
use Botble\Base\Forms\Fields\TextField;
use Botble\Payment\Concerns\Forms\HasAvailableCountriesField;
use Botble\Payment\Forms\PaymentMethodForm;

class HyperPayPaymentMethodForm extends PaymentMethodForm
{
    use HasAvailableCountriesField;

    public function setup(): void
    {
        parent::setup();

        $this
            ->paymentId(HYPERPAY_PAYMENT_METHOD_NAME)
            ->paymentName('HyperPay')
            ->paymentDescription(trans('plugins/hyperpay::hyperpay.description'))
            ->paymentLogo(url('vendor/core/plugins/hyperpay/images/hyperpay.png'))
            ->paymentFeeField(HYPERPAY_PAYMENT_METHOD_NAME)
            ->paymentUrl('https://www.hyperpay.com')
            ->paymentInstructions(view('plugins/hyperpay::instructions')->render())
            ->add(
                'payment_hyperpay_access_token',
                'password',
                TextFieldOption::make()
                    ->label(trans('plugins/hyperpay::hyperpay.access_token'))
                    ->value(BaseHelper::hasDemoModeEnabled() ? '*******************************' : get_payment_setting('access_token', HYPERPAY_PAYMENT_METHOD_NAME))
                    ->placeholder('OGFjOWE0Yzc2N2ExNWMwYjAxNjdkYmYzZDRhYjZmYmJ8ekpycmNNSEY2dw==')
                    ->attributes(['data-counter' => 400])
                    ->helperText(trans('plugins/hyperpay::hyperpay.access_token_helper'))
            )
            ->add(
                'payment_hyperpay_visa_entity_id',
                TextField::class,
                TextFieldOption::make()
                    ->label(trans('plugins/hyperpay::hyperpay.visa_entity_id'))
                    ->value(BaseHelper::hasDemoModeEnabled() ? '*******************************' : get_payment_setting('visa_entity_id', HYPERPAY_PAYMENT_METHOD_NAME))
                    ->placeholder('8ac7a4c767a15c0b0167dbf4171c6fbf')
                    ->helperText(trans('plugins/hyperpay::hyperpay.visa_entity_id_helper'))
            )
            ->add(
                'payment_hyperpay_mada_entity_id',
                TextField::class,
                TextFieldOption::make()
                    ->label(trans('plugins/hyperpay::hyperpay.mada_entity_id'))
                    ->value(BaseHelper::hasDemoModeEnabled() ? '*******************************' : get_payment_setting('mada_entity_id', HYPERPAY_PAYMENT_METHOD_NAME))
                    ->placeholder('8ac7a4c767a15c0b0167dbf4171c6fbf')
                    ->helperText(trans('plugins/hyperpay::hyperpay.mada_entity_id_helper'))
            )
            ->add(
                'payment_hyperpay_amex_entity_id',
                TextField::class,
                TextFieldOption::make()
                    ->label(trans('plugins/hyperpay::hyperpay.amex_entity_id'))
                    ->value(BaseHelper::hasDemoModeEnabled() ? '*******************************' : get_payment_setting('amex_entity_id', HYPERPAY_PAYMENT_METHOD_NAME))
                    ->placeholder('8ac7a4c767a15c0b0167dbf4171c6fbf')
                    ->helperText(trans('plugins/hyperpay::hyperpay.amex_entity_id_helper'))
            )
            ->add(
                'payment_hyperpay_applepay_entity_id',
                TextField::class,
                TextFieldOption::make()
                    ->label(trans('plugins/hyperpay::hyperpay.applepay_entity_id'))
                    ->value(BaseHelper::hasDemoModeEnabled() ? '*******************************' : get_payment_setting('applepay_entity_id', HYPERPAY_PAYMENT_METHOD_NAME))
                    ->placeholder('8ac7a4c767a15c0b0167dbf4171c6fbf')
                    ->helperText(trans('plugins/hyperpay::hyperpay.applepay_entity_id_helper'))
            )
            ->add(
                'payment_hyperpay_sandbox_mode',
                CheckboxField::class,
                CheckboxFieldOption::make()
                    ->label(trans('plugins/hyperpay::hyperpay.sandbox_mode'))
                    ->value(1)
                    ->checked(get_payment_setting('sandbox_mode', HYPERPAY_PAYMENT_METHOD_NAME, false))
                    ->helperText(trans('plugins/hyperpay::hyperpay.sandbox_mode_helper'))
            )
            ->add(
                'payment_hyperpay_3ds_enabled',
                CheckboxField::class,
                CheckboxFieldOption::make()
                    ->label(trans('plugins/hyperpay::hyperpay.3ds_enabled'))
                    ->value(1)
                    ->checked(get_payment_setting('3ds_enabled', HYPERPAY_PAYMENT_METHOD_NAME, true))
                    ->helperText(trans('plugins/hyperpay::hyperpay.3ds_enabled_helper'))
            )
            ->add(
                'payment_hyperpay_currency',
                SelectField::class,
                SelectFieldOption::make()
                    ->label(trans('plugins/hyperpay::hyperpay.currency'))
                    ->choices([
                        'SAR' => 'Saudi Riyal (SAR)',
                        'AED' => 'UAE Dirham (AED)',
                        'USD' => 'US Dollar (USD)',
                        'EUR' => 'Euro (EUR)',
                    ])
                    ->selected(get_payment_setting('currency', HYPERPAY_PAYMENT_METHOD_NAME, 'SAR'))
                    ->helperText(trans('plugins/hyperpay::hyperpay.currency_helper'))
            )
            ->add(
                'payment_hyperpay_webhook_secret',
                'password',
                TextFieldOption::make()
                    ->label(trans('plugins/hyperpay::hyperpay.webhook_secret'))
                    ->value(BaseHelper::hasDemoModeEnabled() ? '*******************************' : get_payment_setting('webhook_secret', HYPERPAY_PAYMENT_METHOD_NAME))
                    ->placeholder('whsec_*************')
                    ->helperText(trans('plugins/hyperpay::hyperpay.webhook_secret_helper'))
            )
            ->addAvailableCountriesField(HYPERPAY_PAYMENT_METHOD_NAME);
    }
}
