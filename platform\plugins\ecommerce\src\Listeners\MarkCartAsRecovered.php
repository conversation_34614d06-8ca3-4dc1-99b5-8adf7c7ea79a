<?php

namespace Bo<PERSON>ble\Ecommerce\Listeners;

use Bo<PERSON>ble\Ecommerce\Events\OrderPlacedEvent;
use Botble\Ecommerce\Services\AbandonedCartService;

class MarkCartAsRecovered
{
    public function __construct(
        protected AbandonedCartService $abandonedCartService
    ) {
    }

    public function handle(OrderPlacedEvent $event): void
    {
        $this->abandonedCartService->markCartAsRecovered($event->order);
    }
}
