<?php

namespace Shaqi\ERPNext\Forms;

use Botble\Base\Forms\FieldOptions\HtmlFieldOption;
use Botble\Base\Forms\FieldOptions\SelectFieldOption;
use Botble\Base\Forms\Fields\HtmlField;
use Bo<PERSON>ble\Base\Forms\Fields\SelectField;
use Botble\Base\Forms\FormAbstract;

class ProductImportForm extends FormAbstract
{
    public function setup(): void
    {
        $this
            ->setUrl(route('erpnext.product-import.import'))
            ->setMethod('POST')
            ->add(
                'instructions',
                HtmlField::class,
                HtmlFieldOption::make()
                    ->content($this->getInstructionsHtml())
            )
            ->add(
                'item_groups_container',
                HtmlField::class,
                HtmlFieldOption::make()
                    ->content($this->getItemGroupsContainerHtml())
            )
            ->add(
                'item_groups',
                SelectField::class,
                SelectFieldOption::make()
                    ->label('Select Item Groups to Import')
                    ->choices([])
                    ->multiple()
                    ->searchable()
                    ->attributes([
                        'id' => 'item_groups_select',
                        'class' => 'form-control select-full-width',
                        'data-placeholder' => 'Loading Item Groups...'
                    ])
                    ->helperText('Select one or more Item Groups from ERPNext to import products from.')
            )
            ->add(
                'preview_container',
                HtmlField::class,
                HtmlFieldOption::make()
                    ->content($this->getPreviewContainerHtml())
            )
            ->add(
                'import_actions',
                HtmlField::class,
                HtmlFieldOption::make()
                    ->content($this->getImportActionsHtml())
            )
            ->addMetaBoxes([
                'import_results' => [
                    'title' => 'Import Results',
                    'content' => $this->getImportResultsHtml(),
                    'priority' => 1,
                ],
            ]);
    }

    protected function getInstructionsHtml(): string
    {
        return '
            <div class="alert alert-info">
                <h5><i class="ti ti-info-circle"></i> Product Import Instructions</h5>
                <ul class="mb-0">
                    <li>Select one or more Item Groups from ERPNext to import products from</li>
                    <li>Click "Preview Products" to see what products will be imported</li>
                    <li>Products with existing SKUs will be skipped to prevent duplicates</li>
                    <li>New product categories will be created automatically based on Item Groups</li>
                    <li>Click "Import Products" to start the import process</li>
                </ul>
            </div>
        ';
    }

    protected function getItemGroupsContainerHtml(): string
    {
        return '
            <div id="item-groups-loading" class="text-center py-3">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading Item Groups...</span>
                </div>
                <p class="mt-2">Loading Item Groups from ERPNext...</p>
            </div>
            <div id="item-groups-error" class="alert alert-danger" style="display: none;">
                <strong>Error:</strong> <span id="item-groups-error-message"></span>
            </div>
        ';
    }

    protected function getPreviewContainerHtml(): string
    {
        return '
            <div id="preview-section" style="display: none;">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="ti ti-eye"></i> Products Preview
                            <span id="preview-count" class="badge bg-primary ms-2"></span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="preview-loading" class="text-center py-3" style="display: none;">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading products...</span>
                            </div>
                            <p class="mt-2">Loading products preview...</p>
                        </div>
                        <div id="preview-content"></div>
                    </div>
                </div>
            </div>
        ';
    }

    protected function getImportActionsHtml(): string
    {
        return '
            <div class="form-group">
                <button type="button" id="preview-btn" class="btn btn-info me-2" disabled>
                    <i class="ti ti-eye"></i> Preview Products
                </button>
                <button type="submit" id="import-btn" class="btn btn-success" disabled>
                    <i class="ti ti-download"></i> Import Products
                </button>
                <div id="import-loading" class="d-inline-block ms-3" style="display: none;">
                    <div class="spinner-border spinner-border-sm text-success" role="status">
                        <span class="visually-hidden">Importing...</span>
                    </div>
                    <span class="ms-2">Importing products...</span>
                </div>
            </div>
        ';
    }

    protected function getImportResultsHtml(): string
    {
        return '
            <div id="import-results" style="display: none;">
                <div id="import-summary" class="alert alert-success">
                    <h6><i class="ti ti-check-circle"></i> Import Summary</h6>
                    <div id="import-stats"></div>
                </div>
                <div id="import-details">
                    <h6>Import Details</h6>
                    <div id="import-details-content"></div>
                </div>
            </div>
        ';
    }

    public function getView(): string
    {
        return 'plugins/erpnext::product-import.index';
    }
}
