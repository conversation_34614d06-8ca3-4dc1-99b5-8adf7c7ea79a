'use strict';

class TabbyCardManager {
    constructor() {
        this.tabbyCardInstance = null;
        this.isInitialized = false;
        this.retryCount = 0;
        this.maxRetries = 3;
    }

    init() {
        if (this.isInitialized) return;
        
        this.isInitialized = true;
        this.bindEvents();
        this.initializeCard();
    }

    bindEvents() {
        // Initialize on DOM ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.initializeCard());
        } else {
            this.initializeCard();
        }

        // Re-initialize when payment method changes
        document.addEventListener('change', (e) => {
            if (e.target.name === 'payment_method') {
                setTimeout(() => this.initializeCard(), 100);
            }
        });

        // Re-initialize when payment methods are reloaded
        document.addEventListener('payment-form-reloaded', () => {
            setTimeout(() => this.initializeCard(), 100);
        });

        // Listen for checkout updates that might change the total amount
        this.observeCheckoutChanges();
    }

    observeCheckoutChanges() {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    const paymentMethodsArea = document.querySelector('[data-bb-toggle="checkout-payment-methods-area"]');
                    if (paymentMethodsArea && mutation.target.contains && mutation.target.contains(paymentMethodsArea)) {
                        setTimeout(() => this.initializeCard(), 100);
                    }
                }
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    initializeCard() {
        try {
            const tabbyCardContainer = document.getElementById('tabbyCard');

            // Get payment method name from global data or fallback
            const paymentMethodName = window.TabbyCheckoutData?.paymentMethodName || 'tabby';
            const tabbyPaymentMethod = document.querySelector(`input[name="payment_method"][value="${paymentMethodName}"]`);

            if (!tabbyCardContainer || !tabbyPaymentMethod) {
                return;
            }

            // Only initialize if Tabby payment method is selected
            if (!tabbyPaymentMethod.checked) {
                this.destroyCard();
                return;
            }

            // Get dynamic data
            const cardData = this.getCardData();

            if (!cardData.amount || parseFloat(cardData.amount) <= 0) {
                console.warn('Tabby Card: Invalid amount detected');
                return;
            }

            // Destroy existing instance
            this.destroyCard();

            // Clear container
            tabbyCardContainer.innerHTML = '';

            // Wait for TabbyCard to be available
            this.waitForTabbyCard(() => {
                this.createCard(cardData);
            });

        } catch (error) {
            console.error('Error initializing Tabby card:', error);
            this.handleError(error);
        }
    }

    waitForTabbyCard(callback) {
        if (typeof TabbyCard !== 'undefined') {
            callback();
        } else if (this.retryCount < this.maxRetries) {
            this.retryCount++;
            setTimeout(() => this.waitForTabbyCard(callback), 500);
        } else {
            console.error('TabbyCard library not loaded after maximum retries');
        }
    }

    createCard(cardData) {
        try {
            this.tabbyCardInstance = new TabbyCard({
                selector: '#tabbyCard',
                currency: cardData.currency,
                lang: cardData.locale,
                price: cardData.amount,
                size: 'wide',
                theme: 'default',
                header: false
            });
            
            this.retryCount = 0; // Reset retry count on success
            console.log('Tabby card initialized successfully');
            
        } catch (error) {
            console.error('Error creating Tabby card:', error);
            this.handleError(error);
        }
    }

    destroyCard() {
        if (this.tabbyCardInstance) {
            try {
                if (typeof this.tabbyCardInstance.destroy === 'function') {
                    this.tabbyCardInstance.destroy();
                }
            } catch (error) {
                console.log('Error destroying Tabby card instance:', error);
            }
            this.tabbyCardInstance = null;
        }
    }

    getCardData() {
        // Prefer global data from server-side, fallback to DOM elements
        let amount = '0';
        let currency = 'SAR';
        let locale = 'en';

        // Use global data if available
        if (window.TabbyCheckoutData) {
            amount = window.TabbyCheckoutData.amount || '0';
            currency = window.TabbyCheckoutData.currency || 'SAR';
            locale = window.TabbyCheckoutData.locale || 'en';
        } else {
            // Fallback to DOM elements
            const amountInput = document.querySelector('input[name="amount"]');
            if (amountInput) {
                amount = amountInput.value.replace(/[^0-9.]/g, '');
            }

            const currencyInput = document.querySelector('input[name="currency"]');
            if (currencyInput) {
                currency = currencyInput.value.toUpperCase();
            }

            const htmlLang = document.documentElement.lang;
            if (htmlLang && htmlLang.startsWith('ar')) {
                locale = 'ar';
            }
        }

        return {
            amount: amount,
            currency: currency,
            locale: locale
        };
    }

    handleError(error) {
        // Log error for debugging
        console.error('Tabby Card Error:', error);
        
        // Optionally show user-friendly message
        const tabbyCardContainer = document.getElementById('tabbyCard');
        if (tabbyCardContainer) {
            tabbyCardContainer.innerHTML = '<div class="text-muted small">Unable to load payment preview</div>';
        }
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('tabbyCard')) {
        const tabbyManager = new TabbyCardManager();
        tabbyManager.init();
        
        // Make it globally accessible for debugging
        window.TabbyCardManager = tabbyManager;
    }
});
