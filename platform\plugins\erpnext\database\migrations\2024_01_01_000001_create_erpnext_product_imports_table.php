<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('erpnext_product_imports', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('product_id')->nullable();
            $table->string('erpnext_item_code')->index();
            $table->string('erpnext_item_name');
            $table->string('erpnext_item_group');
            $table->decimal('erpnext_standard_rate', 15, 2)->nullable();
            $table->text('erpnext_description')->nullable();
            $table->string('import_status')->default('imported'); // imported, updated, failed
            $table->json('import_data')->nullable(); // Store original ERPNext data
            $table->timestamp('imported_at');
            $table->unsignedBigInteger('imported_by')->nullable();
            $table->timestamps();

            $table->foreign('product_id')->references('id')->on('ec_products')->onDelete('set null');
            $table->foreign('imported_by')->references('id')->on('users')->onDelete('set null');
            
            $table->unique(['erpnext_item_code'], 'erpnext_imports_item_code_unique');
            $table->index(['import_status', 'imported_at']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('erpnext_product_imports');
    }
};
