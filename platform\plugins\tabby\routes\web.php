<?php

use <PERSON><PERSON><PERSON>\Tabby\Http\Controllers\TabbyController;
use Illuminate\Support\Facades\Route;

Route::middleware(['core'])
    ->prefix('payment/tabby')->name('payments.tabby.')
    ->group(function (): void {
        Route::get('callback/{token}', [TabbyController::class, 'callback'])->name('callback');

        Route::post('webhook', [TabbyController::class, 'webhook'])
            ->name('webhook');

        // Webhook management routes (admin only)
        Route::middleware(['web', 'auth'])->group(function (): void {
            Route::post('webhook/setup', [TabbyController::class, 'setupWebhook'])->name('webhook.setup');
            Route::delete('webhook/remove', [TabbyController::class, 'removeWebhook'])->name('webhook.remove');
            Route::get('webhook/status', [TabbyController::class, 'getWebhookStatus'])->name('webhook.status');
        });
    });
