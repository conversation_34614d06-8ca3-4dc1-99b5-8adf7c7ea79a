@extends('frontend.layouts.app_wide')

@section('content')

@section('pageTitle', __('messages.visamaster'))

  <div></div>
  
    <form  action="{{route('checkout.hyperpaynew', ['id'=>$checkoutId, 'order_id'=>$order_id])}}" class="paymentWidgets" data-brands="VISA MASTER STC_PAY"></form>
<script src="https://eu-prod.oppwa.com/v1/paymentWidgets.js?checkoutId={{$checkoutId}}"
    integrity="{{$integrity}}"
                    crossorigin="anonymous">
                </script>


 

   

   @php
        $order = \App\Models\Orders::where('id', $order_id)->with('getOrdersIteams')->latest()->first();
        $order_price = $order->total_price;

        $products_data = $order->getOrdersIteams->map(function ($product) {
            return [
                'item_id' => $product->product_sku,
                'item_name' => $product->product_name,
                'quantity' => $product->product_qty,
                'price' => $product->product_final_price,
            ];
        });

        $dataLayer1 = json_encode([
            'transaction_id' => $order->id,
            'currency' => 'SAR',
            'value' => $order->total_price,
            'payement'=>'VISA MASTER',
            'items' => $products_data->toArray(),
        ]);
    @endphp

    <script>
        gtag("event", "begin_checkout", {!! $dataLayer1 !!});
    </script>

@endsection
