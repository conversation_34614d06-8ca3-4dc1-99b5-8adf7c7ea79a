{"__meta": {"id": "01K2Q4EPB00CCYXJ05ABM8E1TG", "datetime": "2025-08-15 15:09:28", "utime": **********.289188, "method": "POST", "uri": "/admin/ecommerce/orders?", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 144, "start": 1755270565.681699, "end": **********.289219, "duration": 2.6075198650360107, "duration_str": "2.61s", "measures": [{"label": "Booting", "start": 1755270565.681699, "relative_start": 0, "end": **********.489376, "relative_end": **********.489376, "duration": 0.****************, "duration_str": "808ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.489392, "relative_start": 0.****************, "end": **********.289223, "relative_end": 4.0531158447265625e-06, "duration": 1.****************, "duration_str": "1.8s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.509051, "relative_start": 0.****************, "end": **********.517852, "relative_end": **********.517852, "duration": 0.008800983428955078, "duration_str": "8.8ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: core/table::bulk-changes", "start": **********.563859, "relative_start": 1.***************, "end": **********.563859, "relative_end": **********.563859, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::dropdown.item", "start": **********.567296, "relative_start": 1.****************, "end": **********.567296, "relative_end": **********.567296, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::53dbbce7846c20f5734d935076bf04ca", "start": **********.579232, "relative_start": 1.8975329399108887, "end": **********.579232, "relative_end": **********.579232, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::dropdown.item", "start": **********.581203, "relative_start": 1.8995039463043213, "end": **********.581203, "relative_end": **********.581203, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::dropdown.item", "start": **********.581824, "relative_start": 1.9001250267028809, "end": **********.581824, "relative_end": **********.581824, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.create", "start": **********.72919, "relative_start": 2.0474910736083984, "end": **********.72919, "relative_end": **********.72919, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::baaf02f6c56c328f3c307011a60b6b9f", "start": **********.730235, "relative_start": 2.0485360622406006, "end": **********.730235, "relative_end": **********.730235, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::table-info", "start": **********.753339, "relative_start": 2.0716400146484375, "end": **********.753339, "relative_end": **********.753339, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c33cfbd01dd1d76718fcd68287a40728", "start": **********.764346, "relative_start": 2.0826468467712402, "end": **********.764346, "relative_end": **********.764346, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::badge", "start": **********.765183, "relative_start": 2.083483934402466, "end": **********.765183, "relative_end": **********.765183, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": **********.959287, "relative_start": 2.277587890625, "end": **********.959287, "relative_end": **********.959287, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": **********.96005, "relative_start": 2.278351068496704, "end": **********.96005, "relative_end": **********.96005, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": **********.960716, "relative_start": 2.2790169715881348, "end": **********.960716, "relative_end": **********.960716, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": **********.961483, "relative_start": 2.2797839641571045, "end": **********.961483, "relative_end": **********.961483, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ef76c90211a97992d58871a56c0e5be6", "start": **********.970236, "relative_start": 2.28853702545166, "end": **********.970236, "relative_end": **********.970236, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": **********.970893, "relative_start": 2.289193868637085, "end": **********.970893, "relative_end": **********.970893, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": **********.971232, "relative_start": 2.2895328998565674, "end": **********.971232, "relative_end": **********.971232, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": **********.97215, "relative_start": 2.2904510498046875, "end": **********.97215, "relative_end": **********.97215, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::56d32ad600231903bf8d5f8c5f304993", "start": **********.981394, "relative_start": 2.2996950149536133, "end": **********.981394, "relative_end": **********.981394, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::components.badge", "start": **********.993476, "relative_start": 2.311776876449585, "end": **********.993476, "relative_end": **********.993476, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": **********.99771, "relative_start": 2.3160109519958496, "end": **********.99771, "relative_end": **********.99771, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::components.badge", "start": **********.999296, "relative_start": 2.317596912384033, "end": **********.999296, "relative_end": **********.999296, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::********************************", "start": **********.009007, "relative_start": 2.327307939529419, "end": **********.009007, "relative_end": **********.009007, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": **********.066706, "relative_start": 2.385006904602051, "end": **********.066706, "relative_end": **********.066706, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": **********.067256, "relative_start": 2.385556936264038, "end": **********.067256, "relative_end": **********.067256, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": **********.067545, "relative_start": 2.385845899581909, "end": **********.067545, "relative_end": **********.067545, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": **********.068055, "relative_start": 2.3863558769226074, "end": **********.068055, "relative_end": **********.068055, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ef76c90211a97992d58871a56c0e5be6", "start": **********.068524, "relative_start": 2.3868248462677, "end": **********.068524, "relative_end": **********.068524, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": **********.068777, "relative_start": 2.387078046798706, "end": **********.068777, "relative_end": **********.068777, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": **********.069021, "relative_start": 2.387321949005127, "end": **********.069021, "relative_end": **********.069021, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": **********.069501, "relative_start": 2.3878018856048584, "end": **********.069501, "relative_end": **********.069501, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::56d32ad600231903bf8d5f8c5f304993", "start": **********.069943, "relative_start": 2.3882439136505127, "end": **********.069943, "relative_end": **********.069943, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::components.badge", "start": **********.07523, "relative_start": 2.39353084564209, "end": **********.07523, "relative_end": **********.07523, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": **********.078953, "relative_start": 2.39725399017334, "end": **********.078953, "relative_end": **********.078953, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::components.badge", "start": **********.080428, "relative_start": 2.398728847503662, "end": **********.080428, "relative_end": **********.080428, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::********************************", "start": **********.080989, "relative_start": 2.399289846420288, "end": **********.080989, "relative_end": **********.080989, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": **********.091466, "relative_start": 2.409766912460327, "end": **********.091466, "relative_end": **********.091466, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": **********.092034, "relative_start": 2.410335063934326, "end": **********.092034, "relative_end": **********.092034, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": **********.09233, "relative_start": 2.410630941390991, "end": **********.09233, "relative_end": **********.09233, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": **********.092851, "relative_start": 2.411151885986328, "end": **********.092851, "relative_end": **********.092851, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ef76c90211a97992d58871a56c0e5be6", "start": **********.093332, "relative_start": 2.411633014678955, "end": **********.093332, "relative_end": **********.093332, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": **********.093657, "relative_start": 2.4119579792022705, "end": **********.093657, "relative_end": **********.093657, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": **********.093916, "relative_start": 2.412216901779175, "end": **********.093916, "relative_end": **********.093916, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": **********.094469, "relative_start": 2.4127700328826904, "end": **********.094469, "relative_end": **********.094469, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::56d32ad600231903bf8d5f8c5f304993", "start": **********.095018, "relative_start": 2.4133188724517822, "end": **********.095018, "relative_end": **********.095018, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::components.badge", "start": **********.099354, "relative_start": 2.4176549911499023, "end": **********.099354, "relative_end": **********.099354, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": **********.102249, "relative_start": 2.4205498695373535, "end": **********.102249, "relative_end": **********.102249, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::components.badge", "start": **********.103439, "relative_start": 2.4217400550842285, "end": **********.103439, "relative_end": **********.103439, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::********************************", "start": **********.103947, "relative_start": 2.422247886657715, "end": **********.103947, "relative_end": **********.103947, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": **********.116961, "relative_start": 2.4352619647979736, "end": **********.116961, "relative_end": **********.116961, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": **********.117522, "relative_start": 2.4358229637145996, "end": **********.117522, "relative_end": **********.117522, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": **********.11781, "relative_start": 2.4361109733581543, "end": **********.11781, "relative_end": **********.11781, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": **********.118321, "relative_start": 2.436621904373169, "end": **********.118321, "relative_end": **********.118321, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ef76c90211a97992d58871a56c0e5be6", "start": **********.118805, "relative_start": 2.437105894088745, "end": **********.118805, "relative_end": **********.118805, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": **********.119075, "relative_start": 2.437376022338867, "end": **********.119075, "relative_end": **********.119075, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": **********.119336, "relative_start": 2.4376368522644043, "end": **********.119336, "relative_end": **********.119336, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": **********.119852, "relative_start": 2.438153028488159, "end": **********.119852, "relative_end": **********.119852, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::56d32ad600231903bf8d5f8c5f304993", "start": **********.12029, "relative_start": 2.4385910034179688, "end": **********.12029, "relative_end": **********.12029, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::components.badge", "start": **********.127938, "relative_start": 2.4462389945983887, "end": **********.127938, "relative_end": **********.127938, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": **********.131423, "relative_start": 2.449723958969116, "end": **********.131423, "relative_end": **********.131423, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::components.badge", "start": **********.132756, "relative_start": 2.451056957244873, "end": **********.132756, "relative_end": **********.132756, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::********************************", "start": **********.13332, "relative_start": 2.4516210556030273, "end": **********.13332, "relative_end": **********.13332, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": **********.144929, "relative_start": 2.4632298946380615, "end": **********.144929, "relative_end": **********.144929, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": **********.145509, "relative_start": 2.4638099670410156, "end": **********.145509, "relative_end": **********.145509, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": **********.145809, "relative_start": 2.4641098976135254, "end": **********.145809, "relative_end": **********.145809, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": **********.146351, "relative_start": 2.4646520614624023, "end": **********.146351, "relative_end": **********.146351, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ef76c90211a97992d58871a56c0e5be6", "start": **********.146839, "relative_start": 2.465139865875244, "end": **********.146839, "relative_end": **********.146839, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": **********.147095, "relative_start": 2.465395927429199, "end": **********.147095, "relative_end": **********.147095, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": **********.147342, "relative_start": 2.4656429290771484, "end": **********.147342, "relative_end": **********.147342, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": **********.147834, "relative_start": 2.466135025024414, "end": **********.147834, "relative_end": **********.147834, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::56d32ad600231903bf8d5f8c5f304993", "start": **********.148261, "relative_start": 2.466562032699585, "end": **********.148261, "relative_end": **********.148261, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::components.badge", "start": **********.152575, "relative_start": 2.4708759784698486, "end": **********.152575, "relative_end": **********.152575, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": **********.156132, "relative_start": 2.474432945251465, "end": **********.156132, "relative_end": **********.156132, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::components.badge", "start": **********.158141, "relative_start": 2.4764418601989746, "end": **********.158141, "relative_end": **********.158141, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::********************************", "start": **********.158819, "relative_start": 2.4771199226379395, "end": **********.158819, "relative_end": **********.158819, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": **********.168542, "relative_start": 2.4868428707122803, "end": **********.168542, "relative_end": **********.168542, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": **********.169163, "relative_start": 2.48746395111084, "end": **********.169163, "relative_end": **********.169163, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": **********.169471, "relative_start": 2.487771987915039, "end": **********.169471, "relative_end": **********.169471, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": **********.170017, "relative_start": 2.4883179664611816, "end": **********.170017, "relative_end": **********.170017, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ef76c90211a97992d58871a56c0e5be6", "start": **********.170504, "relative_start": 2.488805055618286, "end": **********.170504, "relative_end": **********.170504, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": **********.170803, "relative_start": 2.4891040325164795, "end": **********.170803, "relative_end": **********.170803, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": **********.171076, "relative_start": 2.489377021789551, "end": **********.171076, "relative_end": **********.171076, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": **********.171607, "relative_start": 2.48990797996521, "end": **********.171607, "relative_end": **********.171607, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::56d32ad600231903bf8d5f8c5f304993", "start": **********.172223, "relative_start": 2.4905240535736084, "end": **********.172223, "relative_end": **********.172223, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::components.badge", "start": **********.178551, "relative_start": 2.496851921081543, "end": **********.178551, "relative_end": **********.178551, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": **********.181874, "relative_start": 2.5001749992370605, "end": **********.181874, "relative_end": **********.181874, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::components.badge", "start": **********.183305, "relative_start": 2.501605987548828, "end": **********.183305, "relative_end": **********.183305, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::********************************", "start": **********.183835, "relative_start": 2.502135992050171, "end": **********.183835, "relative_end": **********.183835, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": **********.19588, "relative_start": 2.514180898666382, "end": **********.19588, "relative_end": **********.19588, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": **********.196482, "relative_start": 2.5147829055786133, "end": **********.196482, "relative_end": **********.196482, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": **********.196813, "relative_start": 2.5151140689849854, "end": **********.196813, "relative_end": **********.196813, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": **********.197373, "relative_start": 2.515673875808716, "end": **********.197373, "relative_end": **********.197373, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ef76c90211a97992d58871a56c0e5be6", "start": **********.19789, "relative_start": 2.516191005706787, "end": **********.19789, "relative_end": **********.19789, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": **********.19817, "relative_start": 2.5164709091186523, "end": **********.19817, "relative_end": **********.19817, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": **********.198417, "relative_start": 2.5167179107666016, "end": **********.198417, "relative_end": **********.198417, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": **********.198937, "relative_start": 2.517237901687622, "end": **********.198937, "relative_end": **********.198937, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::56d32ad600231903bf8d5f8c5f304993", "start": **********.199369, "relative_start": 2.517669916152954, "end": **********.199369, "relative_end": **********.199369, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::components.badge", "start": **********.203747, "relative_start": 2.522047996520996, "end": **********.203747, "relative_end": **********.203747, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": **********.207184, "relative_start": 2.525485038757324, "end": **********.207184, "relative_end": **********.207184, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::components.badge", "start": **********.208913, "relative_start": 2.5272140502929688, "end": **********.208913, "relative_end": **********.208913, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::********************************", "start": **********.209575, "relative_start": 2.5278759002685547, "end": **********.209575, "relative_end": **********.209575, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": **********.219756, "relative_start": 2.5380568504333496, "end": **********.219756, "relative_end": **********.219756, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": **********.220285, "relative_start": 2.538585901260376, "end": **********.220285, "relative_end": **********.220285, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": **********.220578, "relative_start": 2.538878917694092, "end": **********.220578, "relative_end": **********.220578, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": **********.221162, "relative_start": 2.5394630432128906, "end": **********.221162, "relative_end": **********.221162, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ef76c90211a97992d58871a56c0e5be6", "start": **********.221686, "relative_start": 2.5399868488311768, "end": **********.221686, "relative_end": **********.221686, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": **********.221969, "relative_start": 2.5402698516845703, "end": **********.221969, "relative_end": **********.221969, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": **********.222337, "relative_start": 2.540637969970703, "end": **********.222337, "relative_end": **********.222337, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": **********.22312, "relative_start": 2.5414209365844727, "end": **********.22312, "relative_end": **********.22312, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::56d32ad600231903bf8d5f8c5f304993", "start": **********.223701, "relative_start": 2.542001962661743, "end": **********.223701, "relative_end": **********.223701, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::components.badge", "start": **********.229306, "relative_start": 2.547606945037842, "end": **********.229306, "relative_end": **********.229306, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": **********.232662, "relative_start": 2.5509629249572754, "end": **********.232662, "relative_end": **********.232662, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::components.badge", "start": **********.234089, "relative_start": 2.5523898601531982, "end": **********.234089, "relative_end": **********.234089, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::********************************", "start": **********.234765, "relative_start": 2.5530660152435303, "end": **********.234765, "relative_end": **********.234765, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": **********.246027, "relative_start": 2.5643279552459717, "end": **********.246027, "relative_end": **********.246027, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": **********.246608, "relative_start": 2.564908981323242, "end": **********.246608, "relative_end": **********.246608, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": **********.246908, "relative_start": 2.565208911895752, "end": **********.246908, "relative_end": **********.246908, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": **********.247452, "relative_start": 2.5657529830932617, "end": **********.247452, "relative_end": **********.247452, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ef76c90211a97992d58871a56c0e5be6", "start": **********.24796, "relative_start": 2.566261053085327, "end": **********.24796, "relative_end": **********.24796, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": **********.248215, "relative_start": 2.5665159225463867, "end": **********.248215, "relative_end": **********.248215, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": **********.248457, "relative_start": 2.566757917404175, "end": **********.248457, "relative_end": **********.248457, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": **********.248965, "relative_start": 2.5672659873962402, "end": **********.248965, "relative_end": **********.248965, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::56d32ad600231903bf8d5f8c5f304993", "start": **********.249387, "relative_start": 2.56768798828125, "end": **********.249387, "relative_end": **********.249387, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::components.badge", "start": **********.253906, "relative_start": 2.572206974029541, "end": **********.253906, "relative_end": **********.253906, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": **********.257536, "relative_start": 2.5758368968963623, "end": **********.257536, "relative_end": **********.257536, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::components.badge", "start": **********.258999, "relative_start": 2.5773000717163086, "end": **********.258999, "relative_end": **********.258999, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::********************************", "start": **********.259594, "relative_start": 2.577894926071167, "end": **********.259594, "relative_end": **********.259594, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": **********.269588, "relative_start": 2.5878889560699463, "end": **********.269588, "relative_end": **********.269588, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": **********.27009, "relative_start": 2.588391065597534, "end": **********.27009, "relative_end": **********.27009, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": **********.270351, "relative_start": 2.5886518955230713, "end": **********.270351, "relative_end": **********.270351, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": **********.270888, "relative_start": 2.589189052581787, "end": **********.270888, "relative_end": **********.270888, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ef76c90211a97992d58871a56c0e5be6", "start": **********.271373, "relative_start": 2.5896739959716797, "end": **********.271373, "relative_end": **********.271373, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": **********.271648, "relative_start": 2.589948892593384, "end": **********.271648, "relative_end": **********.271648, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": **********.271932, "relative_start": 2.5902328491210938, "end": **********.271932, "relative_end": **********.271932, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": **********.27253, "relative_start": 2.5908310413360596, "end": **********.27253, "relative_end": **********.27253, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::56d32ad600231903bf8d5f8c5f304993", "start": **********.273067, "relative_start": 2.5913679599761963, "end": **********.273067, "relative_end": **********.273067, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::components.badge", "start": **********.278218, "relative_start": 2.5965189933776855, "end": **********.278218, "relative_end": **********.278218, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": **********.281581, "relative_start": 2.599881887435913, "end": **********.281581, "relative_end": **********.281581, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::components.badge", "start": **********.28285, "relative_start": 2.6011509895324707, "end": **********.28285, "relative_end": **********.28285, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::********************************", "start": **********.283344, "relative_start": 2.601644992828369, "end": **********.283344, "relative_end": **********.283344, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.28563, "relative_start": 2.603930950164795, "end": **********.286107, "relative_end": **********.286107, "duration": 0.00047707557678222656, "duration_str": "477μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 55847816, "peak_usage_str": "53MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.21.0", "PHP Version": "8.3.17", "Environment": "local", "Debug Mode": "Enabled", "URL": "martfury.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 140, "nb_templates": 140, "templates": [{"name": "1x core/table::bulk-changes", "param_count": null, "params": [], "start": **********.563835, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/table/resources/views/bulk-changes.blade.phpcore/table::bulk-changes", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Fbulk-changes.blade.php&line=1", "ajax": false, "filename": "bulk-changes.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/table::bulk-changes"}, {"name": "3x 8def1252668913628243c4d363bee1ef::dropdown.item", "param_count": null, "params": [], "start": **********.567277, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/dropdown/item.blade.php8def1252668913628243c4d363bee1ef::dropdown.item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fdropdown%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 3, "name_original": "8def1252668913628243c4d363bee1ef::dropdown.item"}, {"name": "1x __components::53dbbce7846c20f5734d935076bf04ca", "param_count": null, "params": [], "start": **********.57921, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/53dbbce7846c20f5734d935076bf04ca.blade.php__components::53dbbce7846c20f5734d935076bf04ca", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F53dbbce7846c20f5734d935076bf04ca.blade.php&line=1", "ajax": false, "filename": "53dbbce7846c20f5734d935076bf04ca.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::53dbbce7846c20f5734d935076bf04ca"}, {"name": "1x core/table::partials.create", "param_count": null, "params": [], "start": **********.729165, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/table/resources/views/partials/create.blade.phpcore/table::partials.create", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Fpartials%2Fcreate.blade.php&line=1", "ajax": false, "filename": "create.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/table::partials.create"}, {"name": "1x __components::baaf02f6c56c328f3c307011a60b6b9f", "param_count": null, "params": [], "start": **********.730216, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/baaf02f6c56c328f3c307011a60b6b9f.blade.php__components::baaf02f6c56c328f3c307011a60b6b9f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fbaaf02f6c56c328f3c307011a60b6b9f.blade.php&line=1", "ajax": false, "filename": "baaf02f6c56c328f3c307011a60b6b9f.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::baaf02f6c56c328f3c307011a60b6b9f"}, {"name": "1x core/table::table-info", "param_count": null, "params": [], "start": **********.753311, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/table/resources/views/table-info.blade.phpcore/table::table-info", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Ftable-info.blade.php&line=1", "ajax": false, "filename": "table-info.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/table::table-info"}, {"name": "1x __components::c33cfbd01dd1d76718fcd68287a40728", "param_count": null, "params": [], "start": **********.764323, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/c33cfbd01dd1d76718fcd68287a40728.blade.php__components::c33cfbd01dd1d76718fcd68287a40728", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fc33cfbd01dd1d76718fcd68287a40728.blade.php&line=1", "ajax": false, "filename": "c33cfbd01dd1d76718fcd68287a40728.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::c33cfbd01dd1d76718fcd68287a40728"}, {"name": "1x 8def1252668913628243c4d363bee1ef::badge", "param_count": null, "params": [], "start": **********.765164, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/badge.blade.php8def1252668913628243c4d363bee1ef::badge", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fbadge.blade.php&line=1", "ajax": false, "filename": "badge.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::badge"}, {"name": "10x core/table::row-actions", "param_count": null, "params": [], "start": **********.959266, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/table/resources/views/row-actions.blade.phpcore/table::row-actions", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Frow-actions.blade.php&line=1", "ajax": false, "filename": "row-actions.blade.php", "line": "?"}, "render_count": 10, "name_original": "core/table::row-actions"}, {"name": "20x core/table::actions.action", "param_count": null, "params": [], "start": **********.960031, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/table/resources/views/actions/action.blade.phpcore/table::actions.action", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Factions%2Faction.blade.php&line=1", "ajax": false, "filename": "action.blade.php", "line": "?"}, "render_count": 20, "name_original": "core/table::actions.action"}, {"name": "20x core/table::actions.includes.action-attributes", "param_count": null, "params": [], "start": **********.960696, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/table/resources/views/actions/includes/action-attributes.blade.phpcore/table::actions.includes.action-attributes", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Factions%2Fincludes%2Faction-attributes.blade.php&line=1", "ajax": false, "filename": "action-attributes.blade.php", "line": "?"}, "render_count": 20, "name_original": "core/table::actions.includes.action-attributes"}, {"name": "20x core/table::actions.includes.action-icon", "param_count": null, "params": [], "start": **********.961465, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/table/resources/views/actions/includes/action-icon.blade.phpcore/table::actions.includes.action-icon", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Factions%2Fincludes%2Faction-icon.blade.php&line=1", "ajax": false, "filename": "action-icon.blade.php", "line": "?"}, "render_count": 20, "name_original": "core/table::actions.includes.action-icon"}, {"name": "10x __components::ef76c90211a97992d58871a56c0e5be6", "param_count": null, "params": [], "start": **********.970208, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/ef76c90211a97992d58871a56c0e5be6.blade.php__components::ef76c90211a97992d58871a56c0e5be6", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fef76c90211a97992d58871a56c0e5be6.blade.php&line=1", "ajax": false, "filename": "ef76c90211a97992d58871a56c0e5be6.blade.php", "line": "?"}, "render_count": 10, "name_original": "__components::ef76c90211a97992d58871a56c0e5be6"}, {"name": "10x __components::56d32ad600231903bf8d5f8c5f304993", "param_count": null, "params": [], "start": **********.981366, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/56d32ad600231903bf8d5f8c5f304993.blade.php__components::56d32ad600231903bf8d5f8c5f304993", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F56d32ad600231903bf8d5f8c5f304993.blade.php&line=1", "ajax": false, "filename": "56d32ad600231903bf8d5f8c5f304993.blade.php", "line": "?"}, "render_count": 10, "name_original": "__components::56d32ad600231903bf8d5f8c5f304993"}, {"name": "20x core/base::components.badge", "param_count": null, "params": [], "start": **********.993445, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/badge.blade.phpcore/base::components.badge", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fbadge.blade.php&line=1", "ajax": false, "filename": "badge.blade.php", "line": "?"}, "render_count": 20, "name_original": "core/base::components.badge"}, {"name": "10x core/table::partials.checkbox", "param_count": null, "params": [], "start": **********.997691, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/table/resources/views/partials/checkbox.blade.phpcore/table::partials.checkbox", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Fpartials%2Fcheckbox.blade.php&line=1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}, "render_count": 10, "name_original": "core/table::partials.checkbox"}, {"name": "10x __components::********************************", "param_count": null, "params": [], "start": **********.008981, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/********************************.blade.php__components::********************************", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F********************************.blade.php&line=1", "ajax": false, "filename": "********************************.blade.php", "line": "?"}, "render_count": 10, "name_original": "__components::********************************"}]}, "queries": {"count": 24, "nb_statements": 24, "nb_visible_statements": 24, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.014770000000000002, "accumulated_duration_str": "14.77ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}], "start": **********.540206, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 0, "width_percent": 3.25}, {"sql": "select exists(select * from `activations` where `activations`.`user_id` = 1 and `activations`.`user_id` is not null and `completed` = 1) as `exists`", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/botble/platform/acl/src/Models/User.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\acl\\src\\Models\\User.php", "line": 122}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\acl\\src\\Http\\Middleware\\Authenticate.php", "line": 22}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 87}], "start": **********.5459769, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "User.php:122", "source": {"index": 14, "namespace": null, "name": "vendor/botble/platform/acl/src/Models/User.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\acl\\src\\Models\\User.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Facl%2Fsrc%2FModels%2FUser.php&line=122", "ajax": false, "filename": "User.php", "line": "122"}, "connection": "martfury", "explain": null, "start_percent": 3.25, "width_percent": 2.911}, {"sql": "select * from `user_meta` where `user_meta`.`user_id` = 1 and `user_meta`.`user_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/acl/src/Concerns/HasPreferences.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\acl\\src\\Concerns\\HasPreferences.php", "line": 62}, {"index": 18, "namespace": null, "name": "vendor/botble/platform/acl/src/Concerns/HasPreferences.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\acl\\src\\Concerns\\HasPreferences.php", "line": 32}, {"index": 19, "namespace": null, "name": "vendor/botble/platform/base/src/Supports/AdminAppearance.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Supports\\AdminAppearance.php", "line": 99}, {"index": 20, "namespace": null, "name": "vendor/botble/platform/base/src/Supports/AdminAppearance.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Supports\\AdminAppearance.php", "line": 39}], "start": **********.56302, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 6.161, "width_percent": 3.318}, {"sql": "select `lang_locale`, `lang_code`, `lang_name`, `lang_flag`, `lang_is_rtl` from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\language\\src\\Providers\\HookServiceProvider.php", "line": 118}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "vendor/botble/platform/base/src/Http/Middleware/AdminLocaleMiddleware.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php", "line": 28}], "start": **********.568473, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 9.479, "width_percent": 3.047}, {"sql": "select count(*) as aggregate from `ec_orders` where `is_finished` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\laragon\\www\\martfury\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 174}, {"index": 17, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/DataTableAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\yajra\\laravel-datatables-oracle\\src\\DataTableAbstract.php", "line": 709}, {"index": 18, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\laragon\\www\\martfury\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 157}, {"index": 19, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\laragon\\www\\martfury\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 129}, {"index": 20, "namespace": null, "name": "vendor/botble/platform/table/src/Abstracts/TableAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\table\\src\\Abstracts\\TableAbstract.php", "line": 920}], "start": **********.85016, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "QueryDataTable.php:174", "source": {"index": 16, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\laragon\\www\\martfury\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 174}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fyajra%2Flaravel-datatables-oracle%2Fsrc%2FQueryDataTable.php&line=174", "ajax": false, "filename": "QueryDataTable.php", "line": "174"}, "connection": "martfury", "explain": null, "start_percent": 12.525, "width_percent": 7.515}, {"sql": "select `id`, `status`, `user_id`, `created_at`, `amount`, `tax_amount`, `shipping_amount`, `payment_id`, `ec_orders`.`store_id` from `ec_orders` where `is_finished` = 1 order by `id` desc limit 10 offset 0", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\laragon\\www\\martfury\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 146}, {"index": 17, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\laragon\\www\\martfury\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/botble/platform/table/src/Abstracts/TableAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\table\\src\\Abstracts\\TableAbstract.php", "line": 920}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Tables/OrderTable.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Tables\\OrderTable.php", "line": 85}], "start": **********.853465, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 20.041, "width_percent": 5.687}, {"sql": "select * from `ec_customers` where `ec_customers`.`id` in (0, 2, 11)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\laragon\\www\\martfury\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 146}, {"index": 23, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\laragon\\www\\martfury\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/botble/platform/table/src/Abstracts/TableAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\table\\src\\Abstracts\\TableAbstract.php", "line": 920}], "start": **********.857559, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 25.728, "width_percent": 3.859}, {"sql": "select * from `ec_shipments` where `ec_shipments`.`order_id` in (36, 37, 38, 39, 40, 41, 42, 43, 46, 48)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\laragon\\www\\martfury\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 146}, {"index": 23, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\laragon\\www\\martfury\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/botble/platform/table/src/Abstracts/TableAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\table\\src\\Abstracts\\TableAbstract.php", "line": 920}], "start": **********.8652809, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 29.587, "width_percent": 7.38}, {"sql": "select * from `ec_order_addresses` where `type` = 'shipping_address' and `ec_order_addresses`.`order_id` in (36, 37, 38, 39, 40, 41, 42, 43, 46, 48)", "type": "query", "params": [], "bindings": ["shipping_address"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\laragon\\www\\martfury\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 146}, {"index": 23, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\laragon\\www\\martfury\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/botble/platform/table/src/Abstracts/TableAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\table\\src\\Abstracts\\TableAbstract.php", "line": 920}], "start": **********.8734589, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 36.967, "width_percent": 7.718}, {"sql": "select * from `payments` where `payments`.`id` in (68, 69, 73, 74, 78, 79, 80, 82, 84, 85)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\laragon\\www\\martfury\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 146}, {"index": 23, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\laragon\\www\\martfury\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/botble/platform/table/src/Abstracts/TableAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\table\\src\\Abstracts\\TableAbstract.php", "line": 920}], "start": **********.877868, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 44.685, "width_percent": 6.906}, {"sql": "select * from `mp_stores` where `mp_stores`.`id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\laragon\\www\\martfury\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 146}, {"index": 23, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\laragon\\www\\martfury\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/botble/platform/table/src/Abstracts/TableAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\table\\src\\Abstracts\\TableAbstract.php", "line": 920}], "start": **********.881964, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 51.591, "width_percent": 2.776}, {"sql": "select * from `ec_currencies` order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/CurrencySupport.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\CurrencySupport.php", "line": 107}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/CurrencySupport.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\CurrencySupport.php", "line": 41}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/helpers/currencies.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\helpers\\currencies.php", "line": 141}, {"index": 21, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/Processors/DataProcessor.php", "file": "D:\\laragon\\www\\martfury\\vendor\\yajra\\laravel-datatables-oracle\\src\\Processors\\DataProcessor.php", "line": 102}], "start": **********.953908, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 54.367, "width_percent": 3.588}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_type` = 'Botble\\\\Marketplace\\\\Models\\\\Store' and `slugs`.`reference_id` = 1 and `slugs`.`reference_id` is not null limit 1", "type": "query", "params": [], "bindings": ["Botble\\Marketplace\\Models\\Store", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "vendor/botble/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 118}, {"index": 29, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 25}, {"index": 30, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\HookServiceProvider.php", "line": 767}], "start": **********.9842741, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 57.955, "width_percent": 3.114}, {"sql": "select exists(select * from `ec_shipments`) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Tables/OrderTable.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Tables\\OrderTable.php", "line": 73}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 84}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.995522, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "OrderTable.php:73", "source": {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Tables/OrderTable.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Tables\\OrderTable.php", "line": 73}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FTables%2FOrderTable.php&line=73", "ajax": false, "filename": "OrderTable.php", "line": "73"}, "connection": "martfury", "explain": null, "start_percent": 61.07, "width_percent": 6.026}, {"sql": "select exists(select * from `ec_shipments`) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Tables/OrderTable.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Tables\\OrderTable.php", "line": 73}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 84}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.0771148, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "OrderTable.php:73", "source": {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Tables/OrderTable.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Tables\\OrderTable.php", "line": 73}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FTables%2FOrderTable.php&line=73", "ajax": false, "filename": "OrderTable.php", "line": "73"}, "connection": "martfury", "explain": null, "start_percent": 67.095, "width_percent": 3.927}, {"sql": "select exists(select * from `ec_shipments`) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Tables/OrderTable.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Tables\\OrderTable.php", "line": 73}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 84}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.1008708, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "OrderTable.php:73", "source": {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Tables/OrderTable.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Tables\\OrderTable.php", "line": 73}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FTables%2FOrderTable.php&line=73", "ajax": false, "filename": "OrderTable.php", "line": "73"}, "connection": "martfury", "explain": null, "start_percent": 71.022, "width_percent": 2.776}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_type` = 'Botble\\\\Marketplace\\\\Models\\\\Store' and `slugs`.`reference_id` = 2 and `slugs`.`reference_id` is not null limit 1", "type": "query", "params": [], "bindings": ["Botble\\Marketplace\\Models\\Store", 2], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "vendor/botble/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 118}, {"index": 29, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 25}, {"index": 30, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\HookServiceProvider.php", "line": 767}], "start": **********.121727, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 73.798, "width_percent": 4.942}, {"sql": "select exists(select * from `ec_shipments`) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Tables/OrderTable.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Tables\\OrderTable.php", "line": 73}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 84}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.129997, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "OrderTable.php:73", "source": {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Tables/OrderTable.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Tables\\OrderTable.php", "line": 73}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FTables%2FOrderTable.php&line=73", "ajax": false, "filename": "OrderTable.php", "line": "73"}, "connection": "martfury", "explain": null, "start_percent": 78.741, "width_percent": 2.708}, {"sql": "select exists(select * from `ec_shipments`) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Tables/OrderTable.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Tables\\OrderTable.php", "line": 73}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 84}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.154236, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "OrderTable.php:73", "source": {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Tables/OrderTable.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Tables\\OrderTable.php", "line": 73}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FTables%2FOrderTable.php&line=73", "ajax": false, "filename": "OrderTable.php", "line": "73"}, "connection": "martfury", "explain": null, "start_percent": 81.449, "width_percent": 3.047}, {"sql": "select exists(select * from `ec_shipments`) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Tables/OrderTable.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Tables\\OrderTable.php", "line": 73}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 84}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.180425, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "OrderTable.php:73", "source": {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Tables/OrderTable.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Tables\\OrderTable.php", "line": 73}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FTables%2FOrderTable.php&line=73", "ajax": false, "filename": "OrderTable.php", "line": "73"}, "connection": "martfury", "explain": null, "start_percent": 84.496, "width_percent": 2.708}, {"sql": "select exists(select * from `ec_shipments`) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Tables/OrderTable.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Tables\\OrderTable.php", "line": 73}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 84}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.205491, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "OrderTable.php:73", "source": {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Tables/OrderTable.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Tables\\OrderTable.php", "line": 73}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FTables%2FOrderTable.php&line=73", "ajax": false, "filename": "OrderTable.php", "line": "73"}, "connection": "martfury", "explain": null, "start_percent": 87.204, "width_percent": 3.25}, {"sql": "select exists(select * from `ec_shipments`) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Tables/OrderTable.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Tables\\OrderTable.php", "line": 73}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 84}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.231124, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "OrderTable.php:73", "source": {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Tables/OrderTable.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Tables\\OrderTable.php", "line": 73}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FTables%2FOrderTable.php&line=73", "ajax": false, "filename": "OrderTable.php", "line": "73"}, "connection": "martfury", "explain": null, "start_percent": 90.454, "width_percent": 2.776}, {"sql": "select exists(select * from `ec_shipments`) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Tables/OrderTable.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Tables\\OrderTable.php", "line": 73}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 84}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.2556782, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "OrderTable.php:73", "source": {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Tables/OrderTable.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Tables\\OrderTable.php", "line": 73}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FTables%2FOrderTable.php&line=73", "ajax": false, "filename": "OrderTable.php", "line": "73"}, "connection": "martfury", "explain": null, "start_percent": 93.23, "width_percent": 4.198}, {"sql": "select exists(select * from `ec_shipments`) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Tables/OrderTable.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Tables\\OrderTable.php", "line": 73}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 84}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.280111, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "OrderTable.php:73", "source": {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Tables/OrderTable.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Tables\\OrderTable.php", "line": 73}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FTables%2FOrderTable.php&line=73", "ajax": false, "filename": "OrderTable.php", "line": "73"}, "connection": "martfury", "explain": null, "start_percent": 97.427, "width_percent": 2.573}]}, "models": {"data": {"Botble\\Ecommerce\\Models\\Shipment": {"retrieved": 20, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FShipment.php&line=1", "ajax": false, "filename": "Shipment.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Order": {"retrieved": 10, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FOrder.php&line=1", "ajax": false, "filename": "Order.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\OrderAddress": {"retrieved": 10, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FOrderAddress.php&line=1", "ajax": false, "filename": "OrderAddress.php", "line": "?"}}, "Botble\\Payment\\Models\\Payment": {"retrieved": 10, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fpayment%2Fsrc%2FModels%2FPayment.php&line=1", "ajax": false, "filename": "Payment.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Currency": {"retrieved": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Customer": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "Botble\\Marketplace\\Models\\Store": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FModels%2FStore.php&line=1", "ajax": false, "filename": "Store.php", "line": "?"}}, "Botble\\Slug\\Models\\Slug": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fslug%2Fsrc%2FModels%2FSlug.php&line=1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "Botble\\ACL\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Botble\\ACL\\Models\\UserMeta": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Facl%2Fsrc%2FModels%2FUserMeta.php&line=1", "ajax": false, "filename": "UserMeta.php", "line": "?"}}, "Botble\\Language\\Models\\Language": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Flanguage%2Fsrc%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}}, "count": 63, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 63}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://martfury.gc/admin/ecommerce/orders", "action_name": "orders.index", "controller_action": "Botble\\Ecommerce\\Http\\Controllers\\OrderController@index", "uri": "GET admin/ecommerce/orders", "controller": "Botble\\Ecommerce\\Http\\Controllers\\OrderController@index<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FHttp%2FControllers%2FOrderController.php&line=76\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Botble\\Ecommerce\\Http\\Controllers", "prefix": "admin/ecommerce/orders", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FHttp%2FControllers%2FOrderController.php&line=76\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/plugins/ecommerce/src/Http/Controllers/OrderController.php:76-81</a>", "middleware": "web, core, auth", "duration": "2.61s", "peak_memory": "58MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-343214614 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-343214614\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-624422771 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>draw</span>\" => \"<span class=sf-dump-str>2</span>\"\n  \"<span class=sf-dump-key>columns</span>\" => <span class=sf-dump-note>array:12</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"8 characters\">checkbox</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">checkbox</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"7 characters\">user_id</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">user_id</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"6 characters\">amount</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">amount</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"14 characters\">payment_method</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">payment_id</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"14 characters\">payment_status</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">payment_id</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"10 characters\">tax_amount</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">tax_amount</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"15 characters\">shipping_amount</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"15 characters\">shipping_amount</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"8 characters\">store_id</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">store_id</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"11 characters\">row_actions</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">row_actions</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>column</span>\" => \"<span class=sf-dump-str>1</span>\"\n      \"<span class=sf-dump-key>dir</span>\" => \"<span class=sf-dump-str title=\"4 characters\">desc</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>start</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>length</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-624422771\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-945334072 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">martfury.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2826</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">6AiUEtIqMruJngQhfx6V5J3mOZ7KIDVH5jSHUxUL</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">https://martfury.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">https://martfury.gc/admin/ecommerce/orders</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3215 characters\">cookie_for_consent=1; botble_footprints_cookie=eyJpdiI6Ilp2Q1lFM2s1MkVpWnI2blVTb2FtZWc9PSIsInZhbHVlIjoiU1N3dm1pVUNyMlJieWRscUU2Rkk3dzFSVmsxSFhpU3Bpc0lSQmRvOEl3M0ZwaW42RkFpdUoxV1NOWW52Q2lOWFh1WUVZUFVpWnBGdHBuc2xVWXV6TmJFRUd6VVljQ25vWWlDaVhBczVpNEpTU0NmbytXczNaTzdDdFNCN3U4anoiLCJtYWMiOiJjZDdlODUyY2JjNzc4MjNmNmEwMTc4YzA3ODFlM2MyNmQ0MzhjMGNkMTA2ZDY3MjYwYjQ0MmMzMjA1NDAzMjhjIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6ImNlT05BQnNiOExZUk94aE9NbHNkYWc9PSIsInZhbHVlIjoiTWVoMVdkQWFROThBdm9VWUV1cWd6TFlLUFR2emErYW5PWWNDUk5ycVBWOURCdzVHT0pRaEpET242bzEzSFh0bWlpT0RZZnoxVUd4U0swOGUyV1lteXU0SDJhdG80U0VhdnVHaFVFVUJ0amhRVFhTOEFMWUpnYmVmamdqQ2NrNERDMW1lbElBeWNYN2NHQTAyZm9PTk5FKzIvT3VjRjU3ZU40bjJVNGI5WmdSTHBoZTZrUnp0RE9MbEg0aytxMUVlNTlJTW9lZnJGVWxDbHI3Q3poR2RDbzVISnB1Y0pCSXRDb0cyYmFCRUVRMkl2RkxUQlpoanhhR2pINW1ZdkZmS091REtuSnRlNjB6ekR2clEweFZlMjh1M0dOanZQdTlpcHdTRlg2YzJxQm92VHRPdG9SekNPSEJVa0VKcjlEZjlJenVONVVwZTJnRjQ4N1JZKzhEd2pnZHJsUkM3eXVaMmhYblhRZmVoQlk0YTNSOFFYUEhqYi9hQzZIWkloa0llWGNaMEFYVFFmVGhnYnZJcnJIUHh0Z2pzREpUb2ptM1BnU25wV3M3eWF2RUl5UVdwU0UzUDdCZkErMXlCREdRSXExTzVVK1kwL0Jmd0laTGZ1VE9sc1BMbG5lVnVLNytLaEs1YjJpTEllK3Qwd3VUbHBEL3JLNlo0VEMwN2UxakRKK2hxUE9QTmI5TDdqeW03WkgwZjVnPT0iLCJtYWMiOiI1ZmIxN2YwMTBlNWZjMTY0MTY0NDMzZDUyZmZjNmQ5ZWZkYjk0NTYxZjg0ZTk4ODAzNTVlYzJhYWJmN2I0NmVhIiwidGFnIjoiIn0%3D; shortcode_cache_suggestion_dismissed=1; widget_cache_suggestion_dismissed=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Imtmd01aTUlkUmlvaEJqSmRGMnJ5aXc9PSIsInZhbHVlIjoiMGVSOUY2VWtsSitnWmpMdXIxbHd1WC9PeGRISXNyNXQwM1dJeXBwUVZweUphTlViRWNWT09zVXpYUVpNWm1nTEJXeDlxQmlZTDUrOUNZdk9POERqSzQ0V0N4NXhGMFlyd1hPa2xKVGZjTjhWSWFQbWpRcmdGT1ViRFEyQ2IrMjUwMXNtOW1hbWM0MU5xR2UwdmkycTUyTlNibmRhdENJeXZnYkVlQlFlSkZYVm9BZk43WXU0RUhtOWt5NFZNVTJlN3BSMDRkaUd2Z0RwNzBqei9RUDZRVVhpWm5idEpKK3dJTWJ2dVg1OVQ0Zz0iLCJtYWMiOiJlZTQ5Mzc4NzBkNjk4ODUxYjJmNzI5ZDljM2E1OGY4NzM4YzUyYzc1MGFhYzgwMGY2NjJmM2JlN2Q5OWUwODJmIiwidGFnIjoiIn0%3D; botble_cookie_newsletter=1; remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpibjFnQzQrTDFHeWZNOTFoK1F4R2c9PSIsInZhbHVlIjoiWlBCZ2pyclRiZU9razRVZjF3YU1JVjl3ZlcrTVZKVW9JVlplS2lQOStvUVdjM0RCeWx3a2YrOEpicWtScGdBcXJtY1hZZ3BJOTdhR2dHdmRPYXB0VlZjcG5Mb1hjYWc3TnNxT1Z3RVJZaEM0VXRsSjMvWHp1VTIzR3gvTmJtbUh1dlhiSFhjZzZqMU1JVUVjRVQwZURXeUt5eGFuclh4TVFlSkdyeFQzMVkvdGhRWVlsenZPSG8rNWRNUVU5RG9KYUh6S1YwWmVwOUtCTWd5ckROdnpkZ3gxeTJ1WWdpR29mMUJ6TUpRUTdHQT0iLCJtYWMiOiI3M2Q5NDM1Y2Q2ZWU1NTMzNzczMjdhYWM3ZmRjNTJhZTFjNThiNTFlOTllZGZhM2RlOTIwNWI0OWI1M2U2MGI0IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IllRZXo5YUpKNzh4cEV4L0tFbitxSnc9PSIsInZhbHVlIjoiTXBHamVoeXNkMWNoRldpY3JySVQ0elAwaUZPaWFjUENaMUZTOVkvN0F3RUtpWjBsM2EwMmt6bFlOUk8yVWNxZDBxNnNsbENBZk95TFQ4YSs4YUJBOVNoODVQQ2dEQ1YrajdhUmEwM3hucTR2b2h2V3IzWmdQRXFPaFZ6dVVjdE8iLCJtYWMiOiI2MGY5MmE1MTRmMjViNzFhY2U4ZWMyNGYyMmMwOWMzZjAyMDA0MGU4M2IwOWMzMzE5MmQ0YmE3ZDdlODZjZTYyIiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6IjdzbWtCOUU2bDB3NVNSN0NIRGRzTGc9PSIsInZhbHVlIjoiZzgxQ1BmRUY3em1kb2VaeXc0ZldpNEk4a29SbDVESHRrckxtQiswUzhabWpVdXIzVmJ4Tk9IQ2NUa1J2Yk9qRnVrT0VEemlkMFBsMTVFSzRCVC9Zci9wVXErRExrQ1VKVjUwaGJpUkpvUC85a1dXbVg0NDlGYlUvN0d0ZzI1MW8iLCJtYWMiOiIwMDk0NGYzMmM4NmVlMWMyZDc0MmM4YjMzNTdjY2JjYmM4NjBiMGNjMmJmNTg4Njg1MjYzYThiODFkOTA4ZGU0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-945334072\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1363708161 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5d4a7afb407de91a3f785dca41f29624d4035861</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"347 characters\">{&quot;footprint&quot;:&quot;5d4a7afb407de91a3f785dca41f29624d4035861&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;martfury.gc&quot;,&quot;landing_page&quot;:&quot;\\/&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:&quot;http:\\/\\/localhost\\/&quot;,&quot;referrer_domain&quot;:&quot;localhost&quot;}</span>\"\n  \"<span class=sf-dump-key>shortcode_cache_suggestion_dismissed</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>widget_cache_suggestion_dismissed</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|mr2hzXolFPMrElNypPbMsV1VAiAKgY80Q2lBNyezSZXf4Bow8DqaNYnj6c6c|$2y$12$faChkoM7UV9t8mCoo6oJFOLYCOlwKR0CpAW2E1NPY0Bj1MIeVQw92</span>\"\n  \"<span class=sf-dump-key>botble_cookie_newsletter</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">2|rEY2fnBoxjVikI7FHOoHpfsUL6HmShbqbiBbMajXCGvqp4DJneBXZNsMDEhJ|$2y$12$mAIYinlq8PEmipS0gyL5O.c3YY4axUf.8ExV94mmLdgzhultxp2KS</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6AiUEtIqMruJngQhfx6V5J3mOZ7KIDVH5jSHUxUL</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IlGMU8ikcYH0PyBAGquP1yLFBc9E8t7YmNqOCnbe</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1363708161\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-637164556 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 15 Aug 2025 15:09:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-637164556\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-243822913 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6AiUEtIqMruJngQhfx6V5J3mOZ7KIDVH5jSHUxUL</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>math-captcha</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>first</span>\" => <span class=sf-dump-num>4</span>\n    \"<span class=sf-dump-key>second</span>\" => <span class=sf-dump-num>8</span>\n    \"<span class=sf-dump-key>operand</span>\" => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"19 characters\">https://martfury.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>cart</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>cart_updated_at</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1755270129\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1755270129</span></span> {<a class=sf-dump-ref href=#sf-dump-243822913-ref24335 title=\"3 occurrences\">#4335</a><samp data-depth=3 id=sf-dump-243822913-ref24335 class=sf-dump-compact>\n      #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n      #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n      #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">00000000000010ef0000000000000000</span>\"\n      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n        <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n      </samp>]\n      #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n      <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Friday, August 15, 2025\n- 00:07:19.159364 from now\nDST Off\">2025-08-15 15:02:09.138999 UTC (+00:00)</span>\n    </samp>}\n  </samp>]\n  \"<span class=sf-dump-key>tracked_start_checkout</span>\" => \"<span class=sf-dump-str title=\"32 characters\">2bbdbd08f9ec9fc36d8b3e7ced0cf369</span>\"\n  \"<span class=sf-dump-key>2162c5409405ee8ecb45d303ef2ad5a8</span>\" => <span class=sf-dump-note>array:15</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>promotion_discount_amount</span>\" => <span class=sf-dump-num>0</span>\n    \"<span class=sf-dump-key>billing_address_same_as_shipping_address</span>\" => <span class=sf-dump-const>true</span>\n    \"<span class=sf-dump-key>billing_address</span>\" => []\n    \"<span class=sf-dump-key>marketplace</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:17</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>billing_address_same_as_shipping_address</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>billing_address</span>\" => []\n        \"<span class=sf-dump-key>created_order</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1755269989\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1755269989</span></span> {<a class=sf-dump-ref href=#sf-dump-243822913-ref24336 title=\"2 occurrences\">#4336</a><samp data-depth=5 id=sf-dump-243822913-ref24336 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">00000000000010f00000000000000000</span>\"\n          -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n            <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n          <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Friday, August 15, 2025\n- 00:09:38.639127 from now\nDST Off\">2025-08-15 14:59:49.659652 UTC (+00:00)</span>\n        </samp>}\n        \"<span class=sf-dump-key>created_order_id</span>\" => <span class=sf-dump-num>47</span>\n        \"<span class=sf-dump-key>is_save_order_shipping_address</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>created_order_product</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1755269989\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1755269989</span></span> {<a class=sf-dump-ref href=#sf-dump-243822913-ref24336 title=\"2 occurrences\">#4336</a>}\n        \"<span class=sf-dump-key>coupon_discount_amount</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>applied_coupon_code</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>is_free_shipping</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>promotion_discount_amount</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>shipping_method</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n        \"<span class=sf-dump-key>shipping_option</span>\" => \"<span class=sf-dump-str>3</span>\"\n        \"<span class=sf-dump-key>shipping_amount</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"\n        \"<span class=sf-dump-key>shipping</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>default</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Local Pickup</span>\"\n              \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"\n            </samp>]\n            <span class=sf-dump-key>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Flat Rate</span>\"\n              \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">20.00</span>\"\n            </samp>]\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>default_shipping_method</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n        \"<span class=sf-dump-key>default_shipping_option</span>\" => \"<span class=sf-dump-str>3</span>\"\n        \"<span class=sf-dump-key>is_available_shipping</span>\" => <span class=sf-dump-const>true</span>\n      </samp>]\n      <span class=sf-dump-key>1</span> => <span class=sf-dump-note>array:27</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Ishtiaq Ahmed</span>\"\n        \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"22 characters\"><EMAIL></span>\"\n        \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"13 characters\">+923147552550</span>\"\n        \"<span class=sf-dump-key>address</span>\" => \"<span class=sf-dump-str title=\"4 characters\">test</span>\"\n        \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"2 characters\">PK</span>\"\n        \"<span class=sf-dump-key>state</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Test</span>\"\n        \"<span class=sf-dump-key>city</span>\" => \"<span class=sf-dump-str title=\"4 characters\">test</span>\"\n        \"<span class=sf-dump-key>zip_code</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>created_order</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1755270129\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1755270129</span></span> {<a class=sf-dump-ref href=#sf-dump-243822913-ref24335 title=\"3 occurrences\">#4335</a>}\n        \"<span class=sf-dump-key>created_order_id</span>\" => <span class=sf-dump-num>48</span>\n        \"<span class=sf-dump-key>is_save_order_shipping_address</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>created_order_address</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>created_order_address_id</span>\" => <span class=sf-dump-num>42</span>\n        \"<span class=sf-dump-key>created_order_product</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1755270129\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1755270129</span></span> {<a class=sf-dump-ref href=#sf-dump-243822913-ref24335 title=\"3 occurrences\">#4335</a>}\n        \"<span class=sf-dump-key>billing_address_same_as_shipping_address</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>billing_address</span>\" => []\n        \"<span class=sf-dump-key>coupon_discount_amount</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>applied_coupon_code</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>is_free_shipping</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>promotion_discount_amount</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>shipping_method</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n        \"<span class=sf-dump-key>shipping_option</span>\" => \"<span class=sf-dump-str>3</span>\"\n        \"<span class=sf-dump-key>shipping_amount</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"\n        \"<span class=sf-dump-key>shipping</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>default</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Local Pickup</span>\"\n              \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"\n            </samp>]\n            <span class=sf-dump-key>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Flat Rate</span>\"\n              \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">20.00</span>\"\n            </samp>]\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>default_shipping_method</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n        \"<span class=sf-dump-key>default_shipping_option</span>\" => \"<span class=sf-dump-str>3</span>\"\n        \"<span class=sf-dump-key>is_available_shipping</span>\" => <span class=sf-dump-const>true</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Ishtiaq Ahmed</span>\"\n    \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"22 characters\"><EMAIL></span>\"\n    \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"13 characters\">+923147552550</span>\"\n    \"<span class=sf-dump-key>address</span>\" => \"<span class=sf-dump-str title=\"4 characters\">test</span>\"\n    \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"2 characters\">PK</span>\"\n    \"<span class=sf-dump-key>state</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Test</span>\"\n    \"<span class=sf-dump-key>city</span>\" => \"<span class=sf-dump-str title=\"4 characters\">test</span>\"\n    \"<span class=sf-dump-key>zip_code</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>shipping_method</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Ecommerce\\Enums\\ShippingMethodEnum\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Ecommerce\\Enums</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ShippingMethodEnum</span></span> {<a class=sf-dump-ref>#4337</a><samp data-depth=3 class=sf-dump-compact>\n      #<span class=sf-dump-protected title=\"Protected property\">value</span>: \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n    </samp>}\n    \"<span class=sf-dump-key>shipping_option</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>shipping_amount</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>selected_payment_method</span>\" => \"<span class=sf-dump-str title=\"8 characters\">hyperpay</span>\"\n  \"<span class=sf-dump-key>order_id</span>\" => <span class=sf-dump-num>48</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-243822913\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://martfury.gc/admin/ecommerce/orders", "action_name": "orders.index", "controller_action": "Botble\\Ecommerce\\Http\\Controllers\\OrderController@index"}, "badge": null}}