<?php

namespace Shaqi\ERPNext\Tests;

use Illuminate\Foundation\Testing\TestCase;
use Illuminate\Support\Facades\Http;
use Botble\Ecommerce\Models\Product;
use Botble\Ecommerce\Models\ProductCategory;
use Shaqi\ERPNext\Services\ERPNextService;
use Shaqi\ERPNext\Services\ERPNextProductImportService;

class ProductImportServiceTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();
        
        // Mock settings
        config([
            'settings.enable_erpnext' => true,
            'settings.erpnext_api_url' => 'https://test.erpnext.com',
            'settings.erpnext_api_key' => 'test_key',
            'settings.erpnext_api_secret' => 'test_secret',
            'settings.erpnext_warehouse' => 'Test Warehouse'
        ]);
    }

    public function testGetItemGroups()
    {
        // Mock HTTP response for Item Groups
        Http::fake([
            'https://test.erpnext.com/api/resource/Item Group*' => Http::response([
                'data' => [
                    [
                        'name' => 'Electronics',
                        'item_group_name' => 'Electronics',
                        'parent_item_group' => null,
                        'is_group' => 1
                    ],
                    [
                        'name' => 'Computers',
                        'item_group_name' => 'Computers',
                        'parent_item_group' => 'Electronics',
                        'is_group' => 1
                    ]
                ]
            ], 200)
        ]);

        $itemGroups = ERPNextService::getItemGroups();

        $this->assertIsArray($itemGroups);
        $this->assertCount(2, $itemGroups);
        $this->assertEquals('Electronics', $itemGroups[0]['name']);
        $this->assertEquals('Computers', $itemGroups[1]['name']);
    }

    public function testGetProductsByItemGroup()
    {
        // Mock HTTP response for Products
        Http::fake([
            'https://test.erpnext.com/api/resource/Item*' => Http::response([
                'data' => [
                    [
                        'name' => 'LAPTOP001',
                        'item_name' => 'Gaming Laptop',
                        'item_code' => 'LAPTOP001',
                        'item_group' => 'Computers',
                        'description' => 'High-performance gaming laptop',
                        'standard_rate' => 1500.00,
                        'stock_uom' => 'Nos',
                        'image' => '/files/laptop.jpg',
                        'brand' => 'TechBrand',
                        'disabled' => 0
                    ]
                ]
            ], 200)
        ]);

        $products = ERPNextService::getProductsByItemGroup('Computers');

        $this->assertIsArray($products);
        $this->assertCount(1, $products);
        $this->assertEquals('LAPTOP001', $products[0]['item_code']);
        $this->assertEquals('Gaming Laptop', $products[0]['item_name']);
        $this->assertEquals(1500.00, $products[0]['standard_rate']);
    }

    public function testImportProductsService()
    {
        // Mock HTTP responses
        Http::fake([
            'https://test.erpnext.com/api/resource/Item*' => Http::response([
                'data' => [
                    [
                        'name' => 'TEST001',
                        'item_name' => 'Test Product',
                        'item_code' => 'TEST001',
                        'item_group' => 'Test Group',
                        'description' => 'Test product description',
                        'standard_rate' => 100.00,
                        'stock_uom' => 'Nos',
                        'disabled' => 0
                    ]
                ]
            ], 200)
        ]);

        $importService = new ERPNextProductImportService();
        $results = $importService->importProducts(['Test Group']);

        $this->assertIsArray($results);
        $this->assertArrayHasKey('total', $results);
        $this->assertArrayHasKey('imported', $results);
        $this->assertArrayHasKey('skipped', $results);
        $this->assertArrayHasKey('errors', $results);
        $this->assertEquals(1, $results['total']);
    }

    public function testDuplicateSkuPrevention()
    {
        // Create existing product with same SKU
        Product::factory()->create(['sku' => 'DUPLICATE001']);

        // Mock HTTP response with duplicate SKU
        Http::fake([
            'https://test.erpnext.com/api/resource/Item*' => Http::response([
                'data' => [
                    [
                        'name' => 'DUPLICATE001',
                        'item_name' => 'Duplicate Product',
                        'item_code' => 'DUPLICATE001',
                        'item_group' => 'Test Group',
                        'description' => 'This should be skipped',
                        'standard_rate' => 50.00,
                        'disabled' => 0
                    ]
                ]
            ], 200)
        ]);

        $importService = new ERPNextProductImportService();
        $results = $importService->importProducts(['Test Group']);

        $this->assertEquals(1, $results['total']);
        $this->assertEquals(0, $results['imported']);
        $this->assertEquals(1, $results['skipped']);
        $this->assertEquals(0, $results['errors']);
    }

    public function testCategoryCreation()
    {
        // Mock HTTP response
        Http::fake([
            'https://test.erpnext.com/api/resource/Item*' => Http::response([
                'data' => [
                    [
                        'name' => 'NEWCAT001',
                        'item_name' => 'New Category Product',
                        'item_code' => 'NEWCAT001',
                        'item_group' => 'New Category',
                        'description' => 'Product in new category',
                        'standard_rate' => 75.00,
                        'disabled' => 0
                    ]
                ]
            ], 200)
        ]);

        $importService = new ERPNextProductImportService();
        $results = $importService->importProducts(['New Category']);

        // Check if category was created
        $category = ProductCategory::where('name', 'New Category')->first();
        $this->assertNotNull($category);
        $this->assertEquals('New Category', $category->name);

        // Check if product was created and assigned to category
        $product = Product::where('sku', 'NEWCAT001')->first();
        $this->assertNotNull($product);
        $this->assertTrue($product->categories->contains($category));
    }

    public function testApiErrorHandling()
    {
        // Mock HTTP error response
        Http::fake([
            'https://test.erpnext.com/api/resource/Item*' => Http::response([], 500)
        ]);

        $products = ERPNextService::getProductsByItemGroup('NonExistent');
        $this->assertIsArray($products);
        $this->assertEmpty($products);
    }
}
