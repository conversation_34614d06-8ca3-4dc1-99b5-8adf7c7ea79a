{"__meta": {"id": "01K2Q3XKMFVAW2A8KSHGKM6VN2", "datetime": "2025-08-15 15:00:08", "utime": **********.464573, "method": "GET", "uri": "/checkout/1e7379c0bdec717cab106bc037c750d5/recover", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 4, "start": 1755270007.470233, "end": **********.464589, "duration": 0.9943561553955078, "duration_str": "994ms", "measures": [{"label": "Booting", "start": 1755270007.470233, "relative_start": 0, "end": **********.093431, "relative_end": **********.093431, "duration": 0.****************, "duration_str": "623ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.093441, "relative_start": 0.****************, "end": **********.464591, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "371ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.109467, "relative_start": 0.****************, "end": **********.117748, "relative_end": **********.117748, "duration": 0.*****************, "duration_str": "8.28ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.401366, "relative_start": 0.****************, "end": **********.461592, "relative_end": **********.461592, "duration": 0.****************, "duration_str": "60.23ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "52MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.21.0", "PHP Version": "8.3.17", "Environment": "local", "Debug Mode": "Enabled", "URL": "martfury.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 74, "nb_statements": 74, "nb_visible_statements": 74, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.09903000000000003, "accumulated_duration_str": "99.03ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Footprints/TrackingFilter.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Footprints\\TrackingFilter.php", "line": 45}], "start": **********.132698, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 0, "width_percent": 0.394}, {"sql": "select * from `ec_orders` where (`token` = '1e7379c0bdec717cab106bc037c750d5' and `is_finished` = 0)", "type": "query", "params": [], "bindings": ["1e7379c0bdec717cab106bc037c750d5", 0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1075}, {"index": 20, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.13682, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 0.394, "width_percent": 0.818}, {"sql": "select * from `ec_order_addresses` where `type` = 'shipping_address' and `ec_order_addresses`.`order_id` in (45)", "type": "query", "params": [], "bindings": ["shipping_address"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1075}, {"index": 26, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.142612, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 1.212, "width_percent": 0.687}, {"sql": "select * from `ec_order_product` where `ec_order_product`.`order_id` in (45)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1075}, {"index": 26, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.1456752, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 1.898, "width_percent": 0.98}, {"sql": "select * from `ec_products` where `ec_products`.`id` in (28) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 27, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 28, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1075}, {"index": 32, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}], "start": **********.149503, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 2.878, "width_percent": 1.06}, {"sql": "select * from `ec_orders` where (`token` = '2bbdbd08f9ec9fc36d8b3e7ced0cf369' and `is_finished` = 0)", "type": "query", "params": [], "bindings": ["2bbdbd08f9ec9fc36d8b3e7ced0cf369", 0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1090}, {"index": 20, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.15481, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 3.938, "width_percent": 0.838}, {"sql": "select * from `ec_order_addresses` where `type` = 'shipping_address' and `ec_order_addresses`.`order_id` in (47)", "type": "query", "params": [], "bindings": ["shipping_address"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1090}, {"index": 26, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.156955, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 4.776, "width_percent": 0.707}, {"sql": "delete from `ec_orders` where `id` = 47", "type": "query", "params": [], "bindings": [47], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1093}, {"index": 18, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.1611192, "duration": 0.0023799999999999997, "duration_str": "2.38ms", "memory": 0, "memory_str": null, "filename": "OrderSupportServiceProvider.php:1093", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1093}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FProviders%2FOrderSupportServiceProvider.php&line=1093", "ajax": false, "filename": "OrderSupportServiceProvider.php", "line": "1093"}, "connection": "martfury", "explain": null, "start_percent": 5.483, "width_percent": 2.403}, {"sql": "select * from `ec_shipments` where `ec_shipments`.`order_id` = 47 and `ec_shipments`.`order_id` is not null order by `ec_shipments`.`id` asc limit 1000 offset 0", "type": "query", "params": [], "bindings": [47], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Order.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Order.php", "line": 62}, {"index": 27, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1093}, {"index": 31, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.165405, "duration": 0.00216, "duration_str": "2.16ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 7.886, "width_percent": 2.181}, {"sql": "delete from `ec_order_histories` where `ec_order_histories`.`order_id` = 47 and `ec_order_histories`.`order_id` is not null", "type": "query", "params": [], "bindings": [47], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Order.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Order.php", "line": 63}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1093}, {"index": 25, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.169345, "duration": 0.00211, "duration_str": "2.11ms", "memory": 0, "memory_str": null, "filename": "Order.php:63", "source": {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Order.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Order.php", "line": 63}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FOrder.php&line=63", "ajax": false, "filename": "Order.php", "line": "63"}, "connection": "martfury", "explain": null, "start_percent": 10.068, "width_percent": 2.131}, {"sql": "delete from `ec_order_product` where `ec_order_product`.`order_id` = 47 and `ec_order_product`.`order_id` is not null", "type": "query", "params": [], "bindings": [47], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Order.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Order.php", "line": 64}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1093}, {"index": 25, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.17234, "duration": 0.00746, "duration_str": "7.46ms", "memory": 0, "memory_str": null, "filename": "Order.php:64", "source": {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Order.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Order.php", "line": 64}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FOrder.php&line=64", "ajax": false, "filename": "Order.php", "line": "64"}, "connection": "martfury", "explain": null, "start_percent": 12.198, "width_percent": 7.533}, {"sql": "delete from `ec_order_addresses` where `ec_order_addresses`.`order_id` = 47 and `ec_order_addresses`.`order_id` is not null and `type` = 'shipping_address'", "type": "query", "params": [], "bindings": [47, "shipping_address"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Order.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Order.php", "line": 65}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1093}, {"index": 25, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.1809, "duration": 0.00749, "duration_str": "7.49ms", "memory": 0, "memory_str": null, "filename": "Order.php:65", "source": {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Order.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Order.php", "line": 65}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FOrder.php&line=65", "ajax": false, "filename": "Order.php", "line": "65"}, "connection": "martfury", "explain": null, "start_percent": 19.731, "width_percent": 7.563}, {"sql": "select * from `ec_invoices` where `ec_invoices`.`reference_id` = 47 and `ec_invoices`.`reference_id` is not null order by `ec_invoices`.`id` asc limit 1000 offset 0", "type": "query", "params": [], "bindings": [47], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Order.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Order.php", "line": 66}, {"index": 27, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1093}, {"index": 31, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.190126, "duration": 0.0024700000000000004, "duration_str": "2.47ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 27.295, "width_percent": 2.494}, {"sql": "delete from `payments` where `payments`.`id` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Order.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Order.php", "line": 69}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1093}, {"index": 25, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.1939359, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "Order.php:69", "source": {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Order.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Order.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FOrder.php&line=69", "ajax": false, "filename": "Order.php", "line": "69"}, "connection": "martfury", "explain": null, "start_percent": 29.789, "width_percent": 0.788}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`product_id` in (28)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 25, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1118}, {"index": 29, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.200253, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 30.577, "width_percent": 0.858}, {"sql": "select * from `ec_products` where `ec_products`.`id` in (3) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 26, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1118}, {"index": 30, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.2033691, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 31.435, "width_percent": 0.98}, {"sql": "select `ec_taxes`.`id`, `ec_taxes`.`title`, `ec_taxes`.`percentage` from `ec_tax_products` inner join `ec_taxes` on `ec_taxes`.`id` = `ec_tax_products`.`tax_id` where `ec_tax_products`.`product_id` = 3", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 616}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1133}, {"index": 19, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.209506, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "OrderHelper.php:616", "source": {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 616}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FOrderHelper.php&line=616", "ajax": false, "filename": "OrderHelper.php", "line": "616"}, "connection": "martfury", "explain": null, "start_percent": 32.414, "width_percent": 0.646}, {"sql": "select `ec_taxes`.*, `ec_tax_products`.`product_id` as `pivot_product_id`, `ec_tax_products`.`tax_id` as `pivot_tax_id` from `ec_taxes` inner join `ec_tax_products` on `ec_taxes`.`id` = `ec_tax_products`.`tax_id` where `ec_tax_products`.`product_id` = 3", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 534}, {"index": 28, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 29, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 622}, {"index": 31, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1133}], "start": **********.2148259, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:28", "source": {"index": 20, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseModel.php&line=28", "ajax": false, "filename": "BaseModel.php", "line": "28"}, "connection": "martfury", "explain": null, "start_percent": 33.061, "width_percent": 0.939}, {"sql": "select * from `ec_tax_rules` where `ec_tax_rules`.`tax_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 26, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 534}, {"index": 34, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 35, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 622}], "start": **********.217935, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 34, "width_percent": 0.283}, {"sql": "select * from `ec_flash_sales` where date(`end_date`) >= '2025-08-15' and `status` = 'published' order by `created_at` desc", "type": "query", "params": [], "bindings": ["2025-08-15", "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/FlashSaleRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\FlashSaleRepository.php", "line": 35}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/FlashSaleSupport.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\FlashSaleSupport.php", "line": 52}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/FlashSaleSupport.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\FlashSaleSupport.php", "line": 22}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Concerns/ProductPrices.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Concerns\\ProductPrices.php", "line": 46}], "start": **********.2243261, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 34.283, "width_percent": 0.293}, {"sql": "select * from `ec_customers` where `id` = '2' limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 74}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 216}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 187}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}], "start": **********.228243, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 34.575, "width_percent": 0.475}, {"sql": "select * from `ec_discounts` where `type` = 'promotion' and `start_date` <= '2025-08-15 15:00:08' and (`end_date` is null or `end_date` >= '2025-08-15 15:00:08') and (`target` in ('all-orders', 'amount-minimum-order') or (`target` in ('customer', 'group-products', 'products-by-category', 'specific-product', 'product-variant') and `product_quantity` = 1))", "type": "query", "params": [], "bindings": ["promotion", "2025-08-15 15:00:08", "2025-08-15 15:00:08", "all-orders", "amount-minimum-order", "customer", "group-products", "products-by-category", "specific-product", "product-variant", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/DiscountRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\DiscountRepository.php", "line": 50}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/DiscountSupport.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\DiscountSupport.php", "line": 116}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/DiscountSupport.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\DiscountSupport.php", "line": 42}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Concerns/ProductPrices.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Concerns\\ProductPrices.php", "line": 58}], "start": **********.232633, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 35.05, "width_percent": 0.879}, {"sql": "select distinct `ec_product_variations`.`product_id`, `ec_product_variations`.`configurable_product_id`, `ec_product_attributes`.*, `ec_product_attribute_sets`.`title` as `attribute_set_title`, `ec_product_attribute_sets`.`slug` as `attribute_set_slug`, `ec_product_attribute_sets`.`order` as `attribute_set_order` from `ec_product_variations` inner join `ec_product_variation_items` on `ec_product_variation_items`.`variation_id` = `ec_product_variations`.`id` inner join `ec_product_attributes` on `ec_product_attributes`.`id` = `ec_product_variation_items`.`attribute_id` inner join `ec_product_attribute_sets` on `ec_product_attribute_sets`.`id` = `ec_product_attributes`.`attribute_set_id` where `ec_product_variations`.`product_id` = 28 and `ec_product_variations`.`product_id` is not null order by `attribute_set_order` asc", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 578}, {"index": 29, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 30, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 647}], "start": **********.236023, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 35.929, "width_percent": 0.343}, {"sql": "select * from `ec_currencies` order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/CurrencySupport.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\CurrencySupport.php", "line": 107}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/CurrencySupport.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\CurrencySupport.php", "line": 41}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/helpers/currencies.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\helpers\\currencies.php", "line": 141}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}], "start": **********.2409382, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 36.272, "width_percent": 0.273}, {"sql": "select distinct `ec_products`.*, `ec_products`.`with_storehouse_management`, `ec_products`.`stock_status`, `ec_products`.`quantity`, `ec_products`.`allow_checkout_when_out_of_stock`, (select count(*) from `ec_reviews` where `ec_products`.`id` = `ec_reviews`.`product_id` and `status` = 'published') as `reviews_count`, (select avg(`ec_reviews`.`star`) from `ec_reviews` where `ec_products`.`id` = `ec_reviews`.`product_id` and `status` = 'published') as `reviews_avg` from `ec_products` inner join\n(\nSELECT DISTINCT\nec_products.id,\nCASE\nWHEN (\nec_products.sale_type = 0 AND\nec_products.sale_price <> 0\n) THEN ec_products.sale_price\nWHEN (\nec_products.sale_type = 0 AND\nec_products.sale_price = 0\n) THEN ec_products.price\nWHEN (\nec_products.sale_type = 1 AND\n(\nec_products.start_date > '2025-08-15 15:00:08' OR\nec_products.end_date < '2025-08-15 15:00:08'\n)\n) THEN ec_products.price\nWHEN (\nec_products.sale_type = 1 AND\nec_products.start_date <= '2025-08-15 15:00:08' AND\nec_products.end_date >= '2025-08-15 15:00:08'\n) THEN ec_products.sale_price\nWHEN (\nec_products.sale_type = 1 AND\nec_products.start_date IS NULL AND\nec_products.end_date >= '2025-08-15 15:00:08'\n) THEN ec_products.sale_price\nWHEN (\nec_products.sale_type = 1 AND\nec_products.start_date <= '2025-08-15 15:00:08' AND\nec_products.end_date IS NULL\n) THEN ec_products.sale_price\nELSE ec_products.price\nEND AS final_price\nFROM ec_products\n) AS products_with_final_price\non `products_with_final_price`.`id` = `ec_products`.`id` where `ec_products`.`id` in (28) and `ec_products`.`status` = 'published' and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published')) order by\nCASE\nWHEN ec_products.with_storehouse_management = 0 THEN\nCASE WHEN ec_products.stock_status = 'out_of_stock' THEN 1 ELSE 0 END\nELSE\nCASE WHEN ec_products.quantity <= 0 AND ec_products.allow_checkout_when_out_of_stock = 0 THEN 1 ELSE 0 END\nEND ASC\n, `ec_products`.`order` asc, `ec_products`.`created_at` desc", "type": "query", "params": [], "bindings": ["published", "published", 28, "published", 1, "published", "out_of_stock"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/helpers/products.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\helpers\\products.php", "line": 54}], "start": **********.247422, "duration": 0.0016899999999999999, "duration_str": "1.69ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 36.544, "width_percent": 1.707}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (28) and `slugs`.`reference_type` = 'Botble\\\\Ecommerce\\\\Models\\\\Product'", "type": "query", "params": [], "bindings": ["Botble\\Ecommerce\\Models\\Product"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}], "start": **********.250926, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 38.251, "width_percent": 0.596}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`is_default` = 1 and `ec_product_variations`.`configurable_product_id` in (28)", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}], "start": **********.253065, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 38.847, "width_percent": 0.515}, {"sql": "select `ec_product_collections`.*, `ec_product_collection_products`.`product_id` as `pivot_product_id`, `ec_product_collection_products`.`product_collection_id` as `pivot_product_collection_id` from `ec_product_collections` inner join `ec_product_collection_products` on `ec_product_collections`.`id` = `ec_product_collection_products`.`product_collection_id` where `ec_product_collection_products`.`product_id` in (28)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/helpers/products.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\helpers\\products.php", "line": 54}], "start": **********.254891, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 19, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 39.362, "width_percent": 0.606}, {"sql": "select `ec_product_labels`.*, `ec_product_label_products`.`product_id` as `pivot_product_id`, `ec_product_label_products`.`product_label_id` as `pivot_product_label_id` from `ec_product_labels` inner join `ec_product_label_products` on `ec_product_labels`.`id` = `ec_product_label_products`.`product_label_id` where `ec_product_label_products`.`product_id` in (28)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/helpers/products.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\helpers\\products.php", "line": 54}], "start": **********.256575, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 19, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 39.968, "width_percent": 0.656}, {"sql": "select `ec_products`.*, `ec_product_cross_sale_relations`.`from_product_id` as `pivot_from_product_id`, `ec_product_cross_sale_relations`.`to_product_id` as `pivot_to_product_id`, `ec_product_cross_sale_relations`.`price` as `pivot_price`, `ec_product_cross_sale_relations`.`price_type` as `pivot_price_type`, `ec_product_cross_sale_relations`.`apply_to_all_variations` as `pivot_apply_to_all_variations`, `ec_product_cross_sale_relations`.`is_variant` as `pivot_is_variant` from `ec_products` inner join `ec_product_cross_sale_relations` on `ec_products`.`id` = `ec_product_cross_sale_relations`.`to_product_id` where `ec_product_cross_sale_relations`.`from_product_id` in (28) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/helpers/products.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\helpers\\products.php", "line": 54}], "start": **********.259461, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 19, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 40.624, "width_percent": 0.879}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`product_id` in (28)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}], "start": **********.261623, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 41.503, "width_percent": 0.343}, {"sql": "select * from `ec_products` where `ec_products`.`id` in (3) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 27, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 28, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 29, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}], "start": **********.26368, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 41.846, "width_percent": 0.424}, {"sql": "select `ec_products`.*, `ec_product_cross_sale_relations`.`from_product_id` as `pivot_from_product_id`, `ec_product_cross_sale_relations`.`to_product_id` as `pivot_to_product_id`, `ec_product_cross_sale_relations`.`price` as `pivot_price`, `ec_product_cross_sale_relations`.`price_type` as `pivot_price_type`, `ec_product_cross_sale_relations`.`apply_to_all_variations` as `pivot_apply_to_all_variations`, `ec_product_cross_sale_relations`.`is_variant` as `pivot_is_variant` from `ec_products` inner join `ec_product_cross_sale_relations` on `ec_products`.`id` = `ec_product_cross_sale_relations`.`to_product_id` where `ec_product_cross_sale_relations`.`from_product_id` in (3) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 17}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/EventServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\EventServiceProvider.php", "line": 147}, {"index": 29, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 704}], "start": **********.2668529, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "ProductCrossSalePriceService.php:17", "source": {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FProducts%2FProductCrossSalePriceService.php&line=17", "ajax": false, "filename": "ProductCrossSalePriceService.php", "line": "17"}, "connection": "martfury", "explain": null, "start_percent": 42.27, "width_percent": 1.121}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 1 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 26}, {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/EventServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\EventServiceProvider.php", "line": 147}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 704}], "start": **********.269612, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "ProductCrossSalePriceService.php:26", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 26}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FProducts%2FProductCrossSalePriceService.php&line=26", "ajax": false, "filename": "ProductCrossSalePriceService.php", "line": "26"}, "connection": "martfury", "explain": null, "start_percent": 43.391, "width_percent": 0.535}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 27}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 26, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/EventServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\EventServiceProvider.php", "line": 147}], "start": **********.27103, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 43.926, "width_percent": 0.555}, {"sql": "select * from `ec_products` where `ec_products`.`id` in (24, 25) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 27}, {"index": 26, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 28, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/EventServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\EventServiceProvider.php", "line": 147}], "start": **********.273585, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 44.481, "width_percent": 0.838}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 5 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 26}, {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/EventServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\EventServiceProvider.php", "line": 147}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 704}], "start": **********.27611, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "ProductCrossSalePriceService.php:26", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 26}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FProducts%2FProductCrossSalePriceService.php&line=26", "ajax": false, "filename": "ProductCrossSalePriceService.php", "line": "26"}, "connection": "martfury", "explain": null, "start_percent": 45.32, "width_percent": 0.697}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` in (5)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 27}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 26, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/EventServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\EventServiceProvider.php", "line": 147}], "start": **********.27757, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 46.016, "width_percent": 0.677}, {"sql": "select * from `ec_products` where `ec_products`.`id` in (35, 36, 37) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 27}, {"index": 26, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 28, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/EventServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\EventServiceProvider.php", "line": 147}], "start": **********.2806258, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 46.693, "width_percent": 0.969}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 7 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 26}, {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/EventServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\EventServiceProvider.php", "line": 147}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 704}], "start": **********.283601, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "ProductCrossSalePriceService.php:26", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 26}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FProducts%2FProductCrossSalePriceService.php&line=26", "ajax": false, "filename": "ProductCrossSalePriceService.php", "line": "26"}, "connection": "martfury", "explain": null, "start_percent": 47.662, "width_percent": 0.545}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` in (7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 27}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 26, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/EventServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\EventServiceProvider.php", "line": 147}], "start": **********.285003, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 48.208, "width_percent": 0.606}, {"sql": "select * from `ec_products` where `ec_products`.`id` in (39) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 27}, {"index": 26, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 28, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/EventServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\EventServiceProvider.php", "line": 147}], "start": **********.287544, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 48.813, "width_percent": 1.131}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 10 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 26}, {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/EventServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\EventServiceProvider.php", "line": 147}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 704}], "start": **********.2900941, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "ProductCrossSalePriceService.php:26", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 26}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FProducts%2FProductCrossSalePriceService.php&line=26", "ajax": false, "filename": "ProductCrossSalePriceService.php", "line": "26"}, "connection": "martfury", "explain": null, "start_percent": 49.944, "width_percent": 0.273}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` in (10)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 27}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 26, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/EventServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\EventServiceProvider.php", "line": 147}], "start": **********.2911038, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 50.217, "width_percent": 0.242}, {"sql": "select * from `ec_products` where `ec_products`.`id` in (43) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 27}, {"index": 26, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 28, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/EventServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\EventServiceProvider.php", "line": 147}], "start": **********.293075, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 50.459, "width_percent": 0.374}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 14 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 26}, {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/EventServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\EventServiceProvider.php", "line": 147}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 704}], "start": **********.294664, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "ProductCrossSalePriceService.php:26", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 26}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FProducts%2FProductCrossSalePriceService.php&line=26", "ajax": false, "filename": "ProductCrossSalePriceService.php", "line": "26"}, "connection": "martfury", "explain": null, "start_percent": 50.833, "width_percent": 0.697}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` in (14)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 27}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 26, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/EventServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\EventServiceProvider.php", "line": 147}], "start": **********.296163, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 51.53, "width_percent": 0.646}, {"sql": "select * from `ec_products` where `ec_products`.`id` in (51) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 27}, {"index": 26, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 28, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/EventServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\EventServiceProvider.php", "line": 147}], "start": **********.298867, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 52.176, "width_percent": 0.828}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 17 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [17], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 26}, {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/EventServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\EventServiceProvider.php", "line": 147}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 704}], "start": **********.301084, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "ProductCrossSalePriceService.php:26", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 26}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FProducts%2FProductCrossSalePriceService.php&line=26", "ajax": false, "filename": "ProductCrossSalePriceService.php", "line": "26"}, "connection": "martfury", "explain": null, "start_percent": 53.004, "width_percent": 0.535}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` in (17)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 27}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 26, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/EventServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\EventServiceProvider.php", "line": 147}], "start": **********.302451, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 53.539, "width_percent": 0.565}, {"sql": "select * from `ec_products` where `ec_products`.`id` in (57, 58) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 27}, {"index": 26, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 28, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/EventServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\EventServiceProvider.php", "line": 147}], "start": **********.305032, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 54.105, "width_percent": 0.919}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 18 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 26}, {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/EventServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\EventServiceProvider.php", "line": 147}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 704}], "start": **********.3075259, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "ProductCrossSalePriceService.php:26", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 26}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FProducts%2FProductCrossSalePriceService.php&line=26", "ajax": false, "filename": "ProductCrossSalePriceService.php", "line": "26"}, "connection": "martfury", "explain": null, "start_percent": 55.024, "width_percent": 0.555}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` in (18)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 27}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 26, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/EventServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\EventServiceProvider.php", "line": 147}], "start": **********.308882, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 55.579, "width_percent": 0.666}, {"sql": "select * from `ec_products` where `ec_products`.`id` in (59, 60, 61) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 27}, {"index": 26, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 28, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/EventServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\EventServiceProvider.php", "line": 147}], "start": **********.3117151, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 56.246, "width_percent": 1.232}, {"sql": "select distinct `ec_products`.*, `products_with_final_price`.`final_price`, `ec_products`.`with_storehouse_management`, `ec_products`.`stock_status`, `ec_products`.`quantity`, `ec_products`.`allow_checkout_when_out_of_stock` from `ec_products` inner join\n(\nSELECT DISTINCT\nec_products.id,\nCASE\nWHEN (\nec_products.sale_type = 0 AND\nec_products.sale_price <> 0\n) THEN ec_products.sale_price\nWHEN (\nec_products.sale_type = 0 AND\nec_products.sale_price = 0\n) THEN ec_products.price\nWHEN (\nec_products.sale_type = 1 AND\n(\nec_products.start_date > '2025-08-15 15:00:08' OR\nec_products.end_date < '2025-08-15 15:00:08'\n)\n) THEN ec_products.price\nWHEN (\nec_products.sale_type = 1 AND\nec_products.start_date <= '2025-08-15 15:00:08' AND\nec_products.end_date >= '2025-08-15 15:00:08'\n) THEN ec_products.sale_price\nWHEN (\nec_products.sale_type = 1 AND\nec_products.start_date IS NULL AND\nec_products.end_date >= '2025-08-15 15:00:08'\n) THEN ec_products.sale_price\nWHEN (\nec_products.sale_type = 1 AND\nec_products.start_date <= '2025-08-15 15:00:08' AND\nec_products.end_date IS NULL\n) THEN ec_products.sale_price\nELSE ec_products.price\nEND AS final_price\nFROM ec_products\n) AS products_with_final_price\non `products_with_final_price`.`id` = `ec_products`.`id` where `status` = 'published' and `ec_products`.`id` in (28) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published')) order by\nCASE\nWHEN ec_products.with_storehouse_management = 0 THEN\nCASE WHEN ec_products.stock_status = 'out_of_stock' THEN 1 ELSE 0 END\nELSE\nCASE WHEN ec_products.quantity <= 0 AND ec_products.allow_checkout_when_out_of_stock = 0 THEN 1 ELSE 0 END\nEND ASC\n, `order` asc, `created_at` desc", "type": "query", "params": [], "bindings": ["published", 28, 1, "published", "out_of_stock"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 605}], "start": **********.3185039, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 57.478, "width_percent": 1.313}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (28) and `slugs`.`reference_type` = 'Botble\\\\Ecommerce\\\\Models\\\\Product'", "type": "query", "params": [], "bindings": ["Botble\\Ecommerce\\Models\\Product"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}], "start": **********.321067, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 58.79, "width_percent": 0.273}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`is_default` = 1 and `ec_product_variations`.`configurable_product_id` in (28)", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}], "start": **********.322695, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 59.063, "width_percent": 0.232}, {"sql": "select `ec_product_collections`.*, `ec_product_collection_products`.`product_id` as `pivot_product_id`, `ec_product_collection_products`.`product_collection_id` as `pivot_product_collection_id` from `ec_product_collections` inner join `ec_product_collection_products` on `ec_product_collections`.`id` = `ec_product_collection_products`.`product_collection_id` where `ec_product_collection_products`.`product_id` in (28)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 605}], "start": **********.324018, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 19, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 59.295, "width_percent": 0.263}, {"sql": "select `ec_product_labels`.*, `ec_product_label_products`.`product_id` as `pivot_product_id`, `ec_product_label_products`.`product_label_id` as `pivot_product_label_id` from `ec_product_labels` inner join `ec_product_label_products` on `ec_product_labels`.`id` = `ec_product_label_products`.`product_label_id` where `ec_product_label_products`.`product_id` in (28)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 605}], "start": **********.3251631, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 19, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 59.558, "width_percent": 0.283}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`product_id` in (28)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}], "start": **********.3266978, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 59.84, "width_percent": 0.263}, {"sql": "select * from `ec_products` where `ec_products`.`id` in (3) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 27, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 28, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 29, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}], "start": **********.328514, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 60.103, "width_percent": 0.485}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (3) and `slugs`.`reference_type` = 'Botble\\\\Ecommerce\\\\Models\\\\Product'", "type": "query", "params": [], "bindings": ["Botble\\Ecommerce\\Models\\Product"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 27, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 33, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 34, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}], "start": **********.329837, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 60.588, "width_percent": 0.879}, {"sql": "select * from `mp_stores` where `mp_stores`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 27, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 33, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 34, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}], "start": **********.331842, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 61.466, "width_percent": 0.697}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (1) and `slugs`.`reference_type` = 'Botble\\\\Marketplace\\\\Models\\\\Store'", "type": "query", "params": [], "bindings": ["Botble\\Marketplace\\Models\\Store"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 27, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 33, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 39, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}], "start": **********.3333, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 62.163, "width_percent": 0.687}, {"sql": "select distinct `ec_product_variations`.`product_id`, `ec_product_variations`.`configurable_product_id`, `ec_product_attributes`.*, `ec_product_attribute_sets`.`title` as `attribute_set_title`, `ec_product_attribute_sets`.`slug` as `attribute_set_slug`, `ec_product_attribute_sets`.`order` as `attribute_set_order` from `ec_product_variations` inner join `ec_product_variation_items` on `ec_product_variation_items`.`variation_id` = `ec_product_variations`.`id` inner join `ec_product_attributes` on `ec_product_attributes`.`id` = `ec_product_variation_items`.`attribute_id` inner join `ec_product_attribute_sets` on `ec_product_attribute_sets`.`id` = `ec_product_attributes`.`attribute_set_id` where `ec_product_variations`.`product_id` in (28) order by `attribute_set_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}], "start": **********.336225, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 62.85, "width_percent": 0.828}, {"sql": "select * from `ec_orders` where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1168}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1143}, {"index": 21, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.340165, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 63.678, "width_percent": 0.545}, {"sql": "select max(`id`) as aggregate from `ec_orders`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Order.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Order.php", "line": 269}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Order.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Order.php", "line": 73}, {"index": 28, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 1049}, {"index": 30, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1253}, {"index": 31, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1197}], "start": **********.344576, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Order.php:269", "source": {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Order.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Order.php", "line": 269}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FOrder.php&line=269", "ajax": false, "filename": "Order.php", "line": "269"}, "connection": "martfury", "explain": null, "start_percent": 64.223, "width_percent": 0.586}, {"sql": "select exists(select * from `ec_orders` where `code` = '#10000047') as `exists`", "type": "query", "params": [], "bindings": ["#10000047"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Order.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Order.php", "line": 276}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Order.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Order.php", "line": 73}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 1049}, {"index": 25, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1253}, {"index": 26, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1197}], "start": **********.346087, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Order.php:276", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Order.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Order.php", "line": 276}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FOrder.php&line=276", "ajax": false, "filename": "Order.php", "line": "276"}, "connection": "martfury", "explain": null, "start_percent": 64.809, "width_percent": 0.656}, {"sql": "insert into `ec_orders` (`amount`, `shipping_method`, `shipping_option`, `payment_fee`, `tax_amount`, `sub_total`, `coupon_code`, `user_id`, `token`, `shipping_amount`, `discount_amount`, `status`, `is_finished`, `code`, `updated_at`, `created_at`) values (154, 'default', null, 0, 14, 140, null, 0, '2bbdbd08f9ec9fc36d8b3e7ced0cf369', 0, 0, 'pending', 0, '#10000047', '2025-08-15 15:00:08', '2025-08-15 15:00:08')", "type": "query", "params": [], "bindings": [154, {"value": "default", "label": "<PERSON><PERSON><PERSON>"}, null, 0, 14, 140, null, 0, "2bbdbd08f9ec9fc36d8b3e7ced0cf369", 0, 0, {"value": "pending", "label": "Pending"}, 0, "#10000047", "2025-08-15 15:00:08", "2025-08-15 15:00:08"], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 1049}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1197}, {"index": 22, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1143}, {"index": 26, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}], "start": **********.347805, "duration": 0.00715, "duration_str": "7.15ms", "memory": 0, "memory_str": null, "filename": "OrderHelper.php:1049", "source": {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 1049}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FOrderHelper.php&line=1049", "ajax": false, "filename": "OrderHelper.php", "line": "1049"}, "connection": "martfury", "explain": null, "start_percent": 65.465, "width_percent": 7.22}, {"sql": "select * from `ec_order_addresses` where (`order_id` = 48 and `type` = 'shipping_address') limit 1", "type": "query", "params": [], "bindings": [48, "shipping_address"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 879}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 794}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 772}, {"index": 22, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1268}], "start": **********.35901, "duration": 0.0024, "duration_str": "2.4ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 72.685, "width_percent": 2.424}, {"sql": "insert into `ec_order_addresses` (`order_id`, `name`, `email`, `phone`, `address`, `country`, `state`, `city`, `zip_code`, `type`) values (48, '<PERSON><PERSON><PERSON><PERSON> Ahmed', 'raja<PERSON><EMAIL>', '+923147552550', 'test', 'PK', 'Test', 'test', null, 'shipping_address')", "type": "query", "params": [], "bindings": [48, "<PERSON><PERSON><PERSON><PERSON>", "<EMAIL>", "+923147552550", "test", "PK", "Test", "test", null, {"value": "shipping_address", "label": "Shipping address"}], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 883}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 794}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 772}, {"index": 19, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1268}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1197}], "start": **********.364245, "duration": 0.00717, "duration_str": "7.17ms", "memory": 0, "memory_str": null, "filename": "OrderHelper.php:883", "source": {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 883}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FOrderHelper.php&line=883", "ajax": false, "filename": "OrderHelper.php", "line": "883"}, "connection": "martfury", "explain": null, "start_percent": 75.109, "width_percent": 7.24}, {"sql": "update `ec_orders` set `store_id` = 1, `ec_orders`.`updated_at` = '2025-08-15 15:00:08' where `id` = 48", "type": "query", "params": [], "bindings": [1, "2025-08-15 15:00:08", 48], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1271}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1197}, {"index": 16, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1143}, {"index": 20, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.373177, "duration": 0.00736, "duration_str": "7.36ms", "memory": 0, "memory_str": null, "filename": "OrderSupportServiceProvider.php:1271", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1271}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FProviders%2FOrderSupportServiceProvider.php&line=1271", "ajax": false, "filename": "OrderSupportServiceProvider.php", "line": "1271"}, "connection": "martfury", "explain": null, "start_percent": 82.349, "width_percent": 7.432}, {"sql": "select * from `ec_order_product` where `order_id` = 48", "type": "query", "params": [], "bindings": [48], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 934}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1273}, {"index": 19, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1197}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1143}], "start": **********.3816411, "duration": 0.00713, "duration_str": "7.13ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 89.781, "width_percent": 7.2}, {"sql": "insert into `ec_order_product` (`order_id`, `product_id`, `product_name`, `product_image`, `qty`, `weight`, `price`, `tax_amount`, `options`, `product_type`, `product_options`, `updated_at`, `created_at`) values (48, 28, 'Beat Headphone', 'products/3.jpg', 7, 4403, 20, 14, '{\\\"image\\\":\\\"products\\\\/3.jpg\\\",\\\"attributes\\\":\\\"(Color: Green, Size: L)\\\",\\\"taxRate\\\":10,\\\"taxClasses\\\":{\\\"VAT\\\":10},\\\"options\\\":[],\\\"extras\\\":[],\\\"sku\\\":\\\"SW-154-A0\\\",\\\"weight\\\":629}', 'physical', '[]', '2025-08-15 15:00:08', '2025-08-15 15:00:08')", "type": "query", "params": [], "bindings": [48, 28, "Beat Headphone", "products/3.jpg", 7, 4403, 20, 14, "{\"image\":\"products\\/3.jpg\",\"attributes\":\"(Color: Green, Size: L)\",\"taxRate\":10,\"taxClasses\":{\"VAT\":10},\"options\":[],\"extras\":[],\"sku\":\"SW-154-A0\",\"weight\":629}", {"value": "physical", "label": "Physical"}, "[]", "2025-08-15 15:00:08", "2025-08-15 15:00:08"], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 969}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1273}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1197}, {"index": 22, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 1143}, {"index": 26, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}], "start": **********.393114, "duration": 0.00299, "duration_str": "2.99ms", "memory": 0, "memory_str": null, "filename": "OrderHelper.php:969", "source": {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 969}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FOrderHelper.php&line=969", "ajax": false, "filename": "OrderHelper.php", "line": "969"}, "connection": "martfury", "explain": null, "start_percent": 96.981, "width_percent": 3.019}]}, "models": {"data": {"Botble\\Ecommerce\\Models\\Product": {"retrieved": 26, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\ProductVariation": {"retrieved": 20, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProductVariation.php&line=1", "ajax": false, "filename": "ProductVariation.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Order": {"retrieved": 2, "deleted": 1, "created": 1, "updated": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FOrder.php&line=1", "ajax": false, "filename": "Order.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Currency": {"retrieved": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "Botble\\Slug\\Models\\Slug": {"retrieved": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fslug%2Fsrc%2FModels%2FSlug.php&line=1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\OrderAddress": {"retrieved": 1, "created": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FOrderAddress.php&line=1", "ajax": false, "filename": "OrderAddress.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\OrderProduct": {"retrieved": 1, "created": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FOrderProduct.php&line=1", "ajax": false, "filename": "OrderProduct.php", "line": "?"}}, "Botble\\ACL\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Tax": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FTax.php&line=1", "ajax": false, "filename": "Tax.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Customer": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "Botble\\Marketplace\\Models\\Store": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FModels%2FStore.php&line=1", "ajax": false, "filename": "Store.php", "line": "?"}}}, "count": 67, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 62, "deleted": 1, "created": 3, "updated": 1}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "302 Found", "full_url": "https://martfury.gc/checkout/1e7379c0bdec717cab106bc037c750d5/recover", "action_name": "public.checkout.recover", "controller_action": "Botble\\Ecommerce\\Http\\Controllers\\Fronts\\PublicCheckoutController@getCheckoutRecover", "uri": "GET checkout/{token}/recover", "controller": "Botble\\Ecommerce\\Http\\Controllers\\Fronts\\PublicCheckoutController@getCheckoutRecover<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FHttp%2FControllers%2FFronts%2FPublicCheckoutController.php&line=1003\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Botble\\Ecommerce\\Http\\Controllers\\Fronts", "prefix": "/checkout/{token}", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FHttp%2FControllers%2FFronts%2FPublicCheckoutController.php&line=1003\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/plugins/ecommerce/src/Http/Controllers/Fronts/PublicCheckoutController.php:1003-1064</a>", "middleware": "web, core, localeSessionRedirect, localizationRedirect", "duration": "1s", "peak_memory": "54MB", "response": "Redirect to https://martfury.gc/checkout/2bbdbd08f9ec9fc36d8b3e7ced0cf369", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-2095397426 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>address</span>\" => <span class=sf-dump-note>array:11</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Isht<PERSON>q Ahmed</span>\"\n    \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"22 characters\"><EMAIL></span>\"\n    \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"13 characters\">+923147552550</span>\"\n    \"<span class=sf-dump-key>address</span>\" => \"<span class=sf-dump-str title=\"4 characters\">test</span>\"\n    \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"2 characters\">PK</span>\"\n    \"<span class=sf-dump-key>state</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Test</span>\"\n    \"<span class=sf-dump-key>city</span>\" => \"<span class=sf-dump-str title=\"4 characters\">test</span>\"\n    \"<span class=sf-dump-key>zip_code</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>shipping_method</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Ecommerce\\Enums\\ShippingMethodEnum\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Ecommerce\\Enums</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ShippingMethodEnum</span></span> {<a class=sf-dump-ref>#4483</a><samp data-depth=3 class=sf-dump-compact>\n      #<span class=sf-dump-protected title=\"Protected property\">value</span>: \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n    </samp>}\n    \"<span class=sf-dump-key>shipping_option</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>shipping_amount</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>qty</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2095397426\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-300503781 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-300503781\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-279718595 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">martfury.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3215 characters\">cookie_for_consent=1; botble_footprints_cookie=eyJpdiI6Ilp2Q1lFM2s1MkVpWnI2blVTb2FtZWc9PSIsInZhbHVlIjoiU1N3dm1pVUNyMlJieWRscUU2Rkk3dzFSVmsxSFhpU3Bpc0lSQmRvOEl3M0ZwaW42RkFpdUoxV1NOWW52Q2lOWFh1WUVZUFVpWnBGdHBuc2xVWXV6TmJFRUd6VVljQ25vWWlDaVhBczVpNEpTU0NmbytXczNaTzdDdFNCN3U4anoiLCJtYWMiOiJjZDdlODUyY2JjNzc4MjNmNmEwMTc4YzA3ODFlM2MyNmQ0MzhjMGNkMTA2ZDY3MjYwYjQ0MmMzMjA1NDAzMjhjIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6ImNlT05BQnNiOExZUk94aE9NbHNkYWc9PSIsInZhbHVlIjoiTWVoMVdkQWFROThBdm9VWUV1cWd6TFlLUFR2emErYW5PWWNDUk5ycVBWOURCdzVHT0pRaEpET242bzEzSFh0bWlpT0RZZnoxVUd4U0swOGUyV1lteXU0SDJhdG80U0VhdnVHaFVFVUJ0amhRVFhTOEFMWUpnYmVmamdqQ2NrNERDMW1lbElBeWNYN2NHQTAyZm9PTk5FKzIvT3VjRjU3ZU40bjJVNGI5WmdSTHBoZTZrUnp0RE9MbEg0aytxMUVlNTlJTW9lZnJGVWxDbHI3Q3poR2RDbzVISnB1Y0pCSXRDb0cyYmFCRUVRMkl2RkxUQlpoanhhR2pINW1ZdkZmS091REtuSnRlNjB6ekR2clEweFZlMjh1M0dOanZQdTlpcHdTRlg2YzJxQm92VHRPdG9SekNPSEJVa0VKcjlEZjlJenVONVVwZTJnRjQ4N1JZKzhEd2pnZHJsUkM3eXVaMmhYblhRZmVoQlk0YTNSOFFYUEhqYi9hQzZIWkloa0llWGNaMEFYVFFmVGhnYnZJcnJIUHh0Z2pzREpUb2ptM1BnU25wV3M3eWF2RUl5UVdwU0UzUDdCZkErMXlCREdRSXExTzVVK1kwL0Jmd0laTGZ1VE9sc1BMbG5lVnVLNytLaEs1YjJpTEllK3Qwd3VUbHBEL3JLNlo0VEMwN2UxakRKK2hxUE9QTmI5TDdqeW03WkgwZjVnPT0iLCJtYWMiOiI1ZmIxN2YwMTBlNWZjMTY0MTY0NDMzZDUyZmZjNmQ5ZWZkYjk0NTYxZjg0ZTk4ODAzNTVlYzJhYWJmN2I0NmVhIiwidGFnIjoiIn0%3D; shortcode_cache_suggestion_dismissed=1; widget_cache_suggestion_dismissed=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Imtmd01aTUlkUmlvaEJqSmRGMnJ5aXc9PSIsInZhbHVlIjoiMGVSOUY2VWtsSitnWmpMdXIxbHd1WC9PeGRISXNyNXQwM1dJeXBwUVZweUphTlViRWNWT09zVXpYUVpNWm1nTEJXeDlxQmlZTDUrOUNZdk9POERqSzQ0V0N4NXhGMFlyd1hPa2xKVGZjTjhWSWFQbWpRcmdGT1ViRFEyQ2IrMjUwMXNtOW1hbWM0MU5xR2UwdmkycTUyTlNibmRhdENJeXZnYkVlQlFlSkZYVm9BZk43WXU0RUhtOWt5NFZNVTJlN3BSMDRkaUd2Z0RwNzBqei9RUDZRVVhpWm5idEpKK3dJTWJ2dVg1OVQ0Zz0iLCJtYWMiOiJlZTQ5Mzc4NzBkNjk4ODUxYjJmNzI5ZDljM2E1OGY4NzM4YzUyYzc1MGFhYzgwMGY2NjJmM2JlN2Q5OWUwODJmIiwidGFnIjoiIn0%3D; botble_cookie_newsletter=1; remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpibjFnQzQrTDFHeWZNOTFoK1F4R2c9PSIsInZhbHVlIjoiWlBCZ2pyclRiZU9razRVZjF3YU1JVjl3ZlcrTVZKVW9JVlplS2lQOStvUVdjM0RCeWx3a2YrOEpicWtScGdBcXJtY1hZZ3BJOTdhR2dHdmRPYXB0VlZjcG5Mb1hjYWc3TnNxT1Z3RVJZaEM0VXRsSjMvWHp1VTIzR3gvTmJtbUh1dlhiSFhjZzZqMU1JVUVjRVQwZURXeUt5eGFuclh4TVFlSkdyeFQzMVkvdGhRWVlsenZPSG8rNWRNUVU5RG9KYUh6S1YwWmVwOUtCTWd5ckROdnpkZ3gxeTJ1WWdpR29mMUJ6TUpRUTdHQT0iLCJtYWMiOiI3M2Q5NDM1Y2Q2ZWU1NTMzNzczMjdhYWM3ZmRjNTJhZTFjNThiNTFlOTllZGZhM2RlOTIwNWI0OWI1M2U2MGI0IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjdnT2dMc1RMOGxsc0Y3Sm8vVTRUdFE9PSIsInZhbHVlIjoiUWlKZkRhcUl5U3d1S3R2ZG5KSUF1bHJjTWwwOWE5RTZ2a0dRWXpIbGFMeDlWMXBPVVc1eHYvcjhPcTdUMHo2Q0t2WEZQTGxBdkp4eWltQkF1RmJCVDlRY3BNcktBdEtUeVNXVFhTcWNQMzlqbFBGeTJmSW5iaHRpMUFGSVpwYnoiLCJtYWMiOiI4ZTZiYzA3NTUzY2I1YmFmY2RiZDdkZGU3MjhkMTdiYjJjZGYxMmVkMTEzODBkODY2OTNhODgxMGRiZTRhYjJhIiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6IlUvSUdQdGFlU1ZjVDR1MEt6a2Z2QXc9PSIsInZhbHVlIjoiYjJFSVMwRDUrRENiQzBHanQxNEQ0WDQvOE9ueXpuMEo4Y0JNeWJBcS9nYUVkUytLM2l1NVk5a0p4MDZLR0ZpRWNaenpLSThIckxDTEVWQnFKMGl5N0R6NlNORndKOXZaZUd2cENxSHE1YVowTHVkV0FXaWdqeUl1S0Irc0JJSHkiLCJtYWMiOiI1OTlmZjA2ZDJkMjQ5ZmJlNjZiYWNiMGMyMzQ1YjhiNTg4MTRjZWI0NjY3MTQxOWM0OWM0OTAwYzc5NjA3M2U4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-279718595\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-810727986 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5d4a7afb407de91a3f785dca41f29624d4035861</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"347 characters\">{&quot;footprint&quot;:&quot;5d4a7afb407de91a3f785dca41f29624d4035861&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;martfury.gc&quot;,&quot;landing_page&quot;:&quot;\\/&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:&quot;http:\\/\\/localhost\\/&quot;,&quot;referrer_domain&quot;:&quot;localhost&quot;}</span>\"\n  \"<span class=sf-dump-key>shortcode_cache_suggestion_dismissed</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>widget_cache_suggestion_dismissed</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|mr2hzXolFPMrElNypPbMsV1VAiAKgY80Q2lBNyezSZXf4Bow8DqaNYnj6c6c|$2y$12$faChkoM7UV9t8mCoo6oJFOLYCOlwKR0CpAW2E1NPY0Bj1MIeVQw92</span>\"\n  \"<span class=sf-dump-key>botble_cookie_newsletter</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">2|rEY2fnBoxjVikI7FHOoHpfsUL6HmShbqbiBbMajXCGvqp4DJneBXZNsMDEhJ|$2y$12$mAIYinlq8PEmipS0gyL5O.c3YY4axUf.8ExV94mmLdgzhultxp2KS</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6AiUEtIqMruJngQhfx6V5J3mOZ7KIDVH5jSHUxUL</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IlGMU8ikcYH0PyBAGquP1yLFBc9E8t7YmNqOCnbe</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-810727986\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-174060038 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 15 Aug 2025 15:00:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"61 characters\">https://martfury.gc/checkout/2bbdbd08f9ec9fc36d8b3e7ced0cf369</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-174060038\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1177660960 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6AiUEtIqMruJngQhfx6V5J3mOZ7KIDVH5jSHUxUL</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>math-captcha</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>first</span>\" => <span class=sf-dump-num>4</span>\n    \"<span class=sf-dump-key>second</span>\" => <span class=sf-dump-num>8</span>\n    \"<span class=sf-dump-key>operand</span>\" => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"61 characters\">https://martfury.gc/checkout/2bbdbd08f9ec9fc36d8b3e7ced0cf369</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">success_msg</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>cart</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>cart_updated_at</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @**********\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @**********</span></span> {<a class=sf-dump-ref href=#sf-dump-1177660960-ref24643 title=\"3 occurrences\">#4643</a><samp data-depth=3 id=sf-dump-1177660960-ref24643 class=sf-dump-compact>\n      #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n      #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n      #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">00000000000012230000000000000000</span>\"\n      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n        <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n      </samp>]\n      #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n      <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Friday, August 15, 2025\n- 00:00:00.239378 from now\nDST Off\">2025-08-15 15:00:08.237535 UTC (+00:00)</span>\n    </samp>}\n    \"<span class=sf-dump-key>cart</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#2277</a><samp data-depth=3 class=sf-dump-compact>\n      #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>9ec40d1bca2d2170e5a512f9751f6bd2</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Ecommerce\\Cart\\CartItem\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Ecommerce\\Cart</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CartItem</span></span> {<a class=sf-dump-ref>#4636</a><samp data-depth=5 class=sf-dump-compact>\n          +<span class=sf-dump-public title=\"Public property\">rowId</span>: \"<span class=sf-dump-str title=\"32 characters\">9ec40d1bca2d2170e5a512f9751f6bd2</span>\"\n          +<span class=sf-dump-public title=\"Public property\">id</span>: <span class=sf-dump-num>28</span>\n          +<span class=sf-dump-public title=\"Public property\">qty</span>: <span class=sf-dump-num>7</span>\n          +<span class=sf-dump-public title=\"Public property\">name</span>: \"<span class=sf-dump-str title=\"14 characters\">Beat Headphone</span>\"\n          +<span class=sf-dump-public title=\"Public property\">price</span>: <span class=sf-dump-num>20.0</span>\n          +<span class=sf-dump-public title=\"Public property\">options</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Ecommerce\\Cart\\CartItemOptions\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Ecommerce\\Cart</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CartItemOptions</span></span> {<a class=sf-dump-ref>#4642</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"14 characters\">products/3.jpg</span>\"\n              \"<span class=sf-dump-key>attributes</span>\" => \"<span class=sf-dump-str title=\"23 characters\">(Color: Green, Size: L)</span>\"\n              \"<span class=sf-dump-key>taxRate</span>\" => <span class=sf-dump-num>10.0</span>\n              \"<span class=sf-dump-key>taxClasses</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>VAT</span>\" => <span class=sf-dump-num>10.0</span>\n              </samp>]\n              \"<span class=sf-dump-key>options</span>\" => []\n              \"<span class=sf-dump-key>extras</span>\" => []\n              \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"9 characters\">SW-154-A0</span>\"\n              \"<span class=sf-dump-key>weight</span>\" => <span class=sf-dump-num>629.0</span>\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          </samp>}\n          #<span class=sf-dump-protected title=\"Protected property\">associatedModel</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">taxRate</span>: <span class=sf-dump-num>10.0</span>\n          +<span class=sf-dump-public title=\"Public property\">updated_at</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @**********\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @**********</span></span> {<a class=sf-dump-ref>#4669</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">000000000000123d0000000000000000</span>\"\n            -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n            <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Friday, August 15, 2025\n- 00:00:00.239943 from now\nDST Off\">2025-08-15 15:00:08.237505 UTC (+00:00)</span>\n          </samp>}\n          +<span class=sf-dump-public title=\"Public property\">created_at</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @**********\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @**********</span></span> {<a class=sf-dump-ref>#4670</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">000000000000123e0000000000000000</span>\"\n            -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n            <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Friday, August 15, 2025\n- 00:00:00.239991 from now\nDST Off\">2025-08-15 15:00:08.237499 UTC (+00:00)</span>\n          </samp>}\n        </samp>}\n      </samp>]\n      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n    </samp>}\n  </samp>]\n  \"<span class=sf-dump-key>tracked_start_checkout</span>\" => \"<span class=sf-dump-str title=\"32 characters\">2bbdbd08f9ec9fc36d8b3e7ced0cf369</span>\"\n  \"<span class=sf-dump-key>2162c5409405ee8ecb45d303ef2ad5a8</span>\" => <span class=sf-dump-note>array:15</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>promotion_discount_amount</span>\" => <span class=sf-dump-num>0</span>\n    \"<span class=sf-dump-key>billing_address_same_as_shipping_address</span>\" => <span class=sf-dump-const>true</span>\n    \"<span class=sf-dump-key>billing_address</span>\" => []\n    \"<span class=sf-dump-key>marketplace</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:17</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>billing_address_same_as_shipping_address</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>billing_address</span>\" => []\n        \"<span class=sf-dump-key>created_order</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1755269989\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1755269989</span></span> {<a class=sf-dump-ref href=#sf-dump-1177660960-ref24370 title=\"2 occurrences\">#4370</a><samp data-depth=5 id=sf-dump-1177660960-ref24370 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">00000000000011120000000000000000</span>\"\n          -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n            <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n          <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Friday, August 15, 2025\n- 00:00:18.817714 from now\nDST Off\">2025-08-15 14:59:49.659652 UTC (+00:00)</span>\n        </samp>}\n        \"<span class=sf-dump-key>created_order_id</span>\" => <span class=sf-dump-num>47</span>\n        \"<span class=sf-dump-key>is_save_order_shipping_address</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>created_order_product</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1755269989\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1755269989</span></span> {<a class=sf-dump-ref href=#sf-dump-1177660960-ref24370 title=\"2 occurrences\">#4370</a>}\n        \"<span class=sf-dump-key>coupon_discount_amount</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>applied_coupon_code</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>is_free_shipping</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>promotion_discount_amount</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>shipping_method</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n        \"<span class=sf-dump-key>shipping_option</span>\" => \"<span class=sf-dump-str>3</span>\"\n        \"<span class=sf-dump-key>shipping_amount</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"\n        \"<span class=sf-dump-key>shipping</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>default</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Local Pickup</span>\"\n              \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"\n            </samp>]\n            <span class=sf-dump-key>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Flat Rate</span>\"\n              \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">20.00</span>\"\n            </samp>]\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>default_shipping_method</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n        \"<span class=sf-dump-key>default_shipping_option</span>\" => \"<span class=sf-dump-str>3</span>\"\n        \"<span class=sf-dump-key>is_available_shipping</span>\" => <span class=sf-dump-const>true</span>\n      </samp>]\n      <span class=sf-dump-key>1</span> => <span class=sf-dump-note>array:14</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Ishtiaq Ahmed</span>\"\n        \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"22 characters\"><EMAIL></span>\"\n        \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"13 characters\">+923147552550</span>\"\n        \"<span class=sf-dump-key>address</span>\" => \"<span class=sf-dump-str title=\"4 characters\">test</span>\"\n        \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"2 characters\">PK</span>\"\n        \"<span class=sf-dump-key>state</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Test</span>\"\n        \"<span class=sf-dump-key>city</span>\" => \"<span class=sf-dump-str title=\"4 characters\">test</span>\"\n        \"<span class=sf-dump-key>zip_code</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>created_order</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @**********\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @**********</span></span> {<a class=sf-dump-ref href=#sf-dump-1177660960-ref24643 title=\"3 occurrences\">#4643</a>}\n        \"<span class=sf-dump-key>created_order_id</span>\" => <span class=sf-dump-num>48</span>\n        \"<span class=sf-dump-key>is_save_order_shipping_address</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>created_order_address</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>created_order_address_id</span>\" => <span class=sf-dump-num>42</span>\n        \"<span class=sf-dump-key>created_order_product</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @**********\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @**********</span></span> {<a class=sf-dump-ref href=#sf-dump-1177660960-ref24643 title=\"3 occurrences\">#4643</a>}\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Ishtiaq Ahmed</span>\"\n    \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"22 characters\"><EMAIL></span>\"\n    \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"13 characters\">+923147552550</span>\"\n    \"<span class=sf-dump-key>address</span>\" => \"<span class=sf-dump-str title=\"4 characters\">test</span>\"\n    \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"2 characters\">PK</span>\"\n    \"<span class=sf-dump-key>state</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Test</span>\"\n    \"<span class=sf-dump-key>city</span>\" => \"<span class=sf-dump-str title=\"4 characters\">test</span>\"\n    \"<span class=sf-dump-key>zip_code</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>shipping_method</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Ecommerce\\Enums\\ShippingMethodEnum\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Ecommerce\\Enums</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ShippingMethodEnum</span></span> {<a class=sf-dump-ref>#4483</a><samp data-depth=3 class=sf-dump-compact>\n      #<span class=sf-dump-protected title=\"Protected property\">value</span>: \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n    </samp>}\n    \"<span class=sf-dump-key>shipping_option</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>shipping_amount</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>selected_payment_method</span>\" => \"<span class=sf-dump-str title=\"8 characters\">hyperpay</span>\"\n  \"<span class=sf-dump-key>success_msg</span>\" => \"<span class=sf-dump-str title=\"40 characters\">You have recovered from previous orders!</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1177660960\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "https://martfury.gc/checkout/1e7379c0bdec717cab106bc037c750d5/recover", "action_name": "public.checkout.recover", "controller_action": "Botble\\Ecommerce\\Http\\Controllers\\Fronts\\PublicCheckoutController@getCheckoutRecover"}, "badge": "302 Found"}}