<?php

namespace Shaqi\ERPNext\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\File;

class ERPNextLogger
{
    protected static $logChannel = 'erpnext';
    
    /**
     * Initialize the ERPNext logging channel
     */
    public static function init(): void
    {
        // Ensure the log directory exists
        $logPath = storage_path('logs/erpnext');
        if (!File::exists($logPath)) {
            File::makeDirectory($logPath, 0755, true);
        }
        
        // Configure the logging channel if not already configured
        if (!config('logging.channels.erpnext')) {
            config([
                'logging.channels.erpnext' => [
                    'driver' => 'daily',
                    'path' => storage_path('logs/erpnext/erpnext.log'),
                    'level' => env('LOG_LEVEL', 'debug'),
                    'days' => 14,
                    'replace_placeholders' => true,
                ]
            ]);
        }
    }
    
    /**
     * Log info message with context
     */
    public static function info(string $message, array $context = []): void
    {
        self::init();
        Log::channel(self::$logChannel)->info($message, $context);
    }
    
    /**
     * Log error message with context
     */
    public static function error(string $message, array $context = []): void
    {
        self::init();
        Log::channel(self::$logChannel)->error($message, $context);
    }
    
    /**
     * Log warning message with context
     */
    public static function warning(string $message, array $context = []): void
    {
        self::init();
        Log::channel(self::$logChannel)->warning($message, $context);
    }
    
    /**
     * Log debug message with context
     */
    public static function debug(string $message, array $context = []): void
    {
        self::init();
        Log::channel(self::$logChannel)->debug($message, $context);
    }
    
    /**
     * Log API request details
     */
    public static function logApiRequest(string $method, string $url, array $data = [], array $headers = []): void
    {
        self::info("ERPNext API Request", [
            'method' => $method,
            'url' => $url,
            'data' => $data,
            'headers' => array_filter($headers, function($key) {
                return !in_array(strtolower($key), ['authorization']);
            }, ARRAY_FILTER_USE_KEY)
        ]);
    }
    
    /**
     * Log API response details
     */
    public static function logApiResponse(string $method, string $url, int $statusCode, array $response = [], ?string $error = null): void
    {
        $context = [
            'method' => $method,
            'url' => $url,
            'status_code' => $statusCode,
            'response' => $response
        ];
        
        if ($error) {
            $context['error'] = $error;
            self::error("ERPNext API Response Error", $context);
        } else {
            self::info("ERPNext API Response", $context);
        }
    }
    
    /**
     * Log customer operation
     */
    public static function logCustomerOperation(string $operation, string $email, ?string $customerId = null, ?string $error = null): void
    {
        $context = [
            'operation' => $operation,
            'customer_email' => $email,
            'customer_id' => $customerId
        ];
        
        if ($error) {
            $context['error'] = $error;
            self::error("ERPNext Customer Operation Failed", $context);
        } else {
            self::info("ERPNext Customer Operation Success", $context);
        }
    }
    
    /**
     * Log sales order operation
     */
    public static function logSalesOrderOperation(string $operation, int $orderId, ?string $customerId = null, ?string $salesOrderId = null, ?string $error = null): void
    {
        $context = [
            'operation' => $operation,
            'order_id' => $orderId,
            'customer_id' => $customerId,
            'sales_order_id' => $salesOrderId
        ];
        
        if ($error) {
            $context['error'] = $error;
            self::error("ERPNext Sales Order Operation Failed", $context);
        } else {
            self::info("ERPNext Sales Order Operation Success", $context);
        }
    }
}
