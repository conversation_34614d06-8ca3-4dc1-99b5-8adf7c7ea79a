<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{ __('Redirecting to Tabby...') }}</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            margin: 0;
            padding: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .redirect-container {
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 400px;
            width: 100%;
        }
        .tabby-logo {
            height: 40px;
            margin-bottom: 20px;
        }
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007bff;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .message {
            color: #666;
            margin-bottom: 20px;
        }
        .manual-redirect {
            margin-top: 20px;
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            text-decoration: none;
            display: inline-block;
            cursor: pointer;
        }
        .btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="redirect-container">
        <img src="{{ url('vendor/core/plugins/tabby/images/tabby.svg') }}" alt="Tabby" class="tabby-logo">
        
        <div class="spinner"></div>
        
        <div class="message">
            {{ __('Redirecting you to Tabby to complete your payment...') }}
        </div>
        
        <div class="manual-redirect">
            <p>{{ __('If you are not redirected automatically,') }}</p>
            <a href="{{ $data['web_url'] }}" class="btn">{{ __('Click here to continue') }}</a>
        </div>
    </div>

    <script>
        // Automatic redirect
        setTimeout(function() {
            window.location.href = '{{ $data['web_url'] }}';
        }, 2000);
    </script>
</body>
</html>
