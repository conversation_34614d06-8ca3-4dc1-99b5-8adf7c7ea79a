<?php

/**
 * HyperPay Configuration Test Script
 * 
 * This script tests the HyperPay configuration and entityId retrieval
 * to ensure the fixes are working correctly.
 */

require_once __DIR__ . '/../../../bootstrap/app.php';

use Bo<PERSON>ble\HyperPay\Services\Gateways\HyperPayPaymentService;
use Botble\HyperPay\Supports\HyperPayHelper;

echo "=== HyperPay Configuration Test ===\n\n";

// Test 1: Check if HyperPay is enabled
echo "1. Checking if HyperPay is enabled...\n";
$isEnabled = get_payment_setting('status', HYPERPAY_PAYMENT_METHOD_NAME, false);
echo "   Status: " . ($isEnabled ? "ENABLED" : "DISABLED") . "\n\n";

// Test 2: Check access token
echo "2. Checking access token...\n";
$accessToken = get_payment_setting('access_token', HYPERPAY_PAYMENT_METHOD_NAME);
echo "   Access Token: " . ($accessToken ? "CONFIGURED" : "NOT CONFIGURED") . "\n\n";

// Test 3: Check entity IDs for different payment types
echo "3. Checking entity IDs...\n";
$paymentTypes = ['visa', 'mada', 'amex', 'applepay'];

foreach ($paymentTypes as $type) {
    $entityId = HyperPayHelper::getEntityIdForPaymentType($type);
    echo "   {$type}: " . ($entityId ? $entityId : "NOT CONFIGURED") . "\n";
}
echo "\n";

// Test 4: Check sandbox mode
echo "4. Checking sandbox mode...\n";
$sandboxMode = get_payment_setting('sandbox_mode', HYPERPAY_PAYMENT_METHOD_NAME, false);
echo "   Sandbox Mode: " . ($sandboxMode ? "ENABLED" : "DISABLED") . "\n\n";

// Test 5: Test API URL generation
echo "5. Testing API URL generation...\n";
$service = new HyperPayPaymentService();
$apiUrl = $service->getApiUrl();
echo "   API URL: {$apiUrl}\n\n";

// Test 6: Test entityId retrieval for different payment types
echo "6. Testing entityId retrieval method...\n";
foreach ($paymentTypes as $type) {
    try {
        $entityId = $service->getEntityId($type);
        echo "   {$type}: " . ($entityId ? $entityId : "NOT CONFIGURED") . "\n";
    } catch (Exception $e) {
        echo "   {$type}: ERROR - " . $e->getMessage() . "\n";
    }
}
echo "\n";

// Test 7: Test payment status patterns
echo "7. Testing payment status patterns...\n";
$testCodes = [
    '000.000.000' => 'Success',
    '000.100.110' => 'Success',
    '000.200.000' => 'Pending',
    '800.100.153' => 'Failed (Invalid CVV)',
    '200.300.404' => 'Failed (Invalid Parameter)'
];

foreach ($testCodes as $code => $expected) {
    $isSuccess = $service->isSuccessfulPayment($code);
    $isPending = $service->isPendingPayment($code);
    
    if ($isSuccess) {
        $status = 'SUCCESS';
    } elseif ($isPending) {
        $status = 'PENDING';
    } else {
        $status = 'FAILED';
    }
    
    echo "   Code {$code}: {$status} (Expected: {$expected})\n";
}
echo "\n";

echo "=== Test Complete ===\n";
echo "If all configurations show as CONFIGURED and the API URL is correct,\n";
echo "the HyperPay integration should work properly.\n\n";

echo "Next steps:\n";
echo "1. Test a real payment transaction\n";
echo "2. Check the logs for any entityId errors\n";
echo "3. Verify redirect flow to success page\n";
