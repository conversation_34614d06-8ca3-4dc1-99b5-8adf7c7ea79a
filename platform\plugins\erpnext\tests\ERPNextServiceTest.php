<?php

namespace Shaqi\ERPNext\Tests;

use Illuminate\Foundation\Testing\TestCase;
use Illuminate\Support\Facades\Http;
use <PERSON>haqi\ERPNext\Services\ERPNextService;
use Shaqi\ERPNext\Services\ERPNextLogger;

class ERPNextServiceTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();
        
        // Mock settings
        config([
            'settings.erpnext_api_url' => 'https://test.erpnext.com',
            'settings.erpnext_api_key' => 'test_key',
            'settings.erpnext_api_secret' => 'test_secret',
            'settings.erpnext_warehouse' => 'Test Warehouse'
        ]);
    }

    public function test_create_sales_order_returns_proper_format()
    {
        // Mock successful HTTP response
        Http::fake([
            '*' => Http::response([
                'data' => [
                    'name' => 'SO-2025-00001',
                    'customer' => 'CUST-001',
                    'grand_total' => 100.00
                ]
            ], 200)
        ]);

        $mockOrder = (object) [
            'id' => 123,
            'amount' => 100.00
        ];

        $items = [
            [
                'item_code' => 'ITEM-001',
                'qty' => 1,
                'rate' => 100.00,
                'warehouse' => 'Test Warehouse'
            ]
        ];

        $result = ERPNextService::createSalesOrder('CUST-001', $items, $mockOrder);

        // Verify the new return format
        $this->assertIsArray($result);
        $this->assertTrue($result['success']);
        $this->assertEquals('SO-2025-00001', $result['sales_order_id']);
        $this->assertArrayHasKey('data', $result);
    }

    public function test_create_sales_order_handles_failure()
    {
        // Mock failed HTTP response
        Http::fake([
            '*' => Http::response('Internal Server Error', 500)
        ]);

        $mockOrder = (object) [
            'id' => 123,
            'amount' => 100.00
        ];

        $items = [
            [
                'item_code' => 'ITEM-001',
                'qty' => 1,
                'rate' => 100.00,
                'warehouse' => 'Test Warehouse'
            ]
        ];

        $result = ERPNextService::createSalesOrder('CUST-001', $items, $mockOrder);

        // Verify error handling
        $this->assertIsArray($result);
        $this->assertFalse($result['success']);
        $this->assertArrayHasKey('error', $result);
        $this->assertEquals(500, $result['status_code']);
    }

    public function test_logger_creates_directory()
    {
        // Test that the logger creates the log directory
        ERPNextLogger::info('Test message');
        
        $this->assertTrue(file_exists(storage_path('logs/erpnext')));
    }
}
