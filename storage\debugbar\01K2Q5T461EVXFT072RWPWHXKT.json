{"__meta": {"id": "01K2Q5T461EVXFT072RWPWHXKT", "datetime": "2025-08-15 15:33:11", "utime": **********.490398, "method": "GET", "uri": "/admin/ecommerce/orders", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 390, "start": 1755271989.887945, "end": **********.490412, "duration": 1.6024670600891113, "duration_str": "1.6s", "measures": [{"label": "Booting", "start": 1755271989.887945, "relative_start": 0, "end": **********.631399, "relative_end": **********.631399, "duration": 0.****************, "duration_str": "743ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.631413, "relative_start": 0.****************, "end": **********.490414, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "859ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.649042, "relative_start": 0.****************, "end": **********.65851, "relative_end": **********.65851, "duration": 0.*****************, "duration_str": "9.47ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: core/table::bulk-changes", "start": **********.73527, "relative_start": 0.****************, "end": **********.73527, "relative_end": **********.73527, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::dropdown.item", "start": **********.738868, "relative_start": 0.****************, "end": **********.738868, "relative_end": **********.738868, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::53dbbce7846c20f5734d935076bf04ca", "start": **********.741804, "relative_start": 0.8538589477539062, "end": **********.741804, "relative_end": **********.741804, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::dropdown.item", "start": **********.743537, "relative_start": 0.8555920124053955, "end": **********.743537, "relative_end": **********.743537, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::dropdown.item", "start": **********.744392, "relative_start": 0.8564469814300537, "end": **********.744392, "relative_end": **********.744392, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.create", "start": **********.746751, "relative_start": 0.8588061332702637, "end": **********.746751, "relative_end": **********.746751, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::baaf02f6c56c328f3c307011a60b6b9f", "start": **********.747728, "relative_start": 0.8597831726074219, "end": **********.747728, "relative_end": **********.747728, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::table-info", "start": **********.751038, "relative_start": 0.8630931377410889, "end": **********.751038, "relative_end": **********.751038, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c33cfbd01dd1d76718fcd68287a40728", "start": **********.752396, "relative_start": 0.8644511699676514, "end": **********.752396, "relative_end": **********.752396, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::badge", "start": **********.753156, "relative_start": 0.865211009979248, "end": **********.753156, "relative_end": **********.753156, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.762128, "relative_start": 0.874183177947998, "end": **********.487677, "relative_end": **********.487677, "duration": 0.7255489826202393, "duration_str": "726ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: core/table::table", "start": **********.763038, "relative_start": 0.8750929832458496, "end": **********.763038, "relative_end": **********.763038, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::base-table", "start": **********.763642, "relative_start": 0.875697135925293, "end": **********.763642, "relative_end": **********.763642, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.781329, "relative_start": 0.8933839797973633, "end": **********.781329, "relative_end": **********.781329, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::14ad31fb3af14d3ba24d3c578af35e73", "start": **********.783128, "relative_start": 0.8951830863952637, "end": **********.783128, "relative_end": **********.783128, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::filter", "start": **********.809481, "relative_start": 0.9215359687805176, "end": **********.809481, "relative_end": **********.809481, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.81137, "relative_start": 0.9234249591827393, "end": **********.81137, "relative_end": **********.81137, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.813898, "relative_start": 0.9259531497955322, "end": **********.813898, "relative_end": **********.813898, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.814445, "relative_start": 0.9265000820159912, "end": **********.814445, "relative_end": **********.814445, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.815287, "relative_start": 0.9273421764373779, "end": **********.815287, "relative_end": **********.815287, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.815669, "relative_start": 0.9277241230010986, "end": **********.815669, "relative_end": **********.815669, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d7b9d721822c50540453b6fa0f4bd081", "start": **********.816874, "relative_start": 0.9289290904998779, "end": **********.816874, "relative_end": **********.816874, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.817639, "relative_start": 0.9296941757202148, "end": **********.817639, "relative_end": **********.817639, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.81837, "relative_start": 0.9304251670837402, "end": **********.81837, "relative_end": **********.81837, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.81875, "relative_start": 0.930804967880249, "end": **********.81875, "relative_end": **********.81875, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.819408, "relative_start": 0.9314630031585693, "end": **********.819408, "relative_end": **********.819408, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.819762, "relative_start": 0.9318170547485352, "end": **********.819762, "relative_end": **********.819762, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.820328, "relative_start": 0.9323830604553223, "end": **********.820328, "relative_end": **********.820328, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.821132, "relative_start": 0.9331870079040527, "end": **********.821132, "relative_end": **********.821132, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0c8728926b3975e33a051ebb6ef68e5d", "start": **********.822195, "relative_start": 0.9342501163482666, "end": **********.822195, "relative_end": **********.822195, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.index", "start": **********.822836, "relative_start": 0.9348909854888916, "end": **********.822836, "relative_end": **********.822836, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.body.index", "start": **********.823289, "relative_start": 0.9353439807891846, "end": **********.823289, "relative_end": **********.823289, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.index", "start": **********.823644, "relative_start": 0.9356989860534668, "end": **********.823644, "relative_end": **********.823644, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::bulk-action", "start": **********.845304, "relative_start": 0.9573590755462646, "end": **********.845304, "relative_end": **********.845304, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::dropdown.item", "start": **********.84663, "relative_start": 0.9586851596832275, "end": **********.84663, "relative_end": **********.84663, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::dropdown.index", "start": **********.847252, "relative_start": 0.9593069553375244, "end": **********.847252, "relative_end": **********.847252, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.847861, "relative_start": 0.9599161148071289, "end": **********.847861, "relative_end": **********.847861, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.85804, "relative_start": 0.970095157623291, "end": **********.85804, "relative_end": **********.85804, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6f3b10173cc6f5c541f27080145e1a40", "start": **********.859305, "relative_start": 0.9713599681854248, "end": **********.859305, "relative_end": **********.859305, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::42e1966f95bce065f65d4b22e53f3772", "start": **********.860262, "relative_start": 0.9723169803619385, "end": **********.860262, "relative_end": **********.860262, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.create", "start": **********.860885, "relative_start": 0.9729399681091309, "end": **********.860885, "relative_end": **********.860885, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::baaf02f6c56c328f3c307011a60b6b9f", "start": **********.861247, "relative_start": 0.9733021259307861, "end": **********.861247, "relative_end": **********.861247, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b4335e9ff8c721e014fa31a65e4454d3", "start": **********.863536, "relative_start": 0.9755909442901611, "end": **********.863536, "relative_end": **********.863536, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::77e239c329a8f8a48840d651c6a8a9ee", "start": **********.864824, "relative_start": 0.9768791198730469, "end": **********.864824, "relative_end": **********.864824, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7b2a6f88f6865c9dfe436f51388f0b67", "start": **********.865881, "relative_start": 0.9779360294342041, "end": **********.865881, "relative_end": **********.865881, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.866464, "relative_start": 0.9785189628601074, "end": **********.866464, "relative_end": **********.866464, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0c8728926b3975e33a051ebb6ef68e5d", "start": **********.867147, "relative_start": 0.9792020320892334, "end": **********.867147, "relative_end": **********.867147, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.header.index", "start": **********.867466, "relative_start": 0.9795210361480713, "end": **********.867466, "relative_end": **********.867466, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.index", "start": **********.87886, "relative_start": 0.990915060043335, "end": **********.87886, "relative_end": **********.87886, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::modal", "start": **********.879672, "relative_start": 0.9917271137237549, "end": **********.879672, "relative_end": **********.879672, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.action", "start": **********.880706, "relative_start": 0.9927611351013184, "end": **********.880706, "relative_end": **********.880706, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.alert", "start": **********.881504, "relative_start": 0.9935591220855713, "end": **********.881504, "relative_end": **********.881504, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.close-button", "start": **********.882351, "relative_start": 0.99440598487854, "end": **********.882351, "relative_end": **********.882351, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d17cb0db54485d707113609802086895", "start": **********.88328, "relative_start": 0.9953351020812988, "end": **********.88328, "relative_end": **********.88328, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal", "start": **********.883959, "relative_start": 0.9960141181945801, "end": **********.883959, "relative_end": **********.883959, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.action", "start": **********.884769, "relative_start": 0.9968240261077881, "end": **********.884769, "relative_end": **********.884769, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.alert", "start": **********.885346, "relative_start": 0.9974009990692139, "end": **********.885346, "relative_end": **********.885346, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.close-button", "start": **********.885938, "relative_start": 0.997992992401123, "end": **********.885938, "relative_end": **********.885938, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d17cb0db54485d707113609802086895", "start": **********.886226, "relative_start": 0.9982810020446777, "end": **********.886226, "relative_end": **********.886226, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal", "start": **********.886443, "relative_start": 0.9984979629516602, "end": **********.886443, "relative_end": **********.886443, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.887235, "relative_start": 0.9992899894714355, "end": **********.887235, "relative_end": **********.887235, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.887823, "relative_start": 0.9998781681060791, "end": **********.887823, "relative_end": **********.887823, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal", "start": **********.888337, "relative_start": 1.000391960144043, "end": **********.888337, "relative_end": **********.888337, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.close-button", "start": **********.889098, "relative_start": 1.0011529922485352, "end": **********.889098, "relative_end": **********.889098, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.action", "start": **********.889366, "relative_start": 1.0014209747314453, "end": **********.889366, "relative_end": **********.889366, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.alert", "start": **********.889909, "relative_start": 1.0019640922546387, "end": **********.889909, "relative_end": **********.889909, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.close-button", "start": **********.890474, "relative_start": 1.0025291442871094, "end": **********.890474, "relative_end": **********.890474, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d17cb0db54485d707113609802086895", "start": **********.89074, "relative_start": 1.0027949810028076, "end": **********.89074, "relative_end": **********.89074, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal", "start": **********.891, "relative_start": 1.0030550956726074, "end": **********.891, "relative_end": **********.891, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.action", "start": **********.891651, "relative_start": 1.0037059783935547, "end": **********.891651, "relative_end": **********.891651, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.alert", "start": **********.892195, "relative_start": 1.0042500495910645, "end": **********.892195, "relative_end": **********.892195, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.close-button", "start": **********.892762, "relative_start": 1.004817008972168, "end": **********.892762, "relative_end": **********.892762, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d17cb0db54485d707113609802086895", "start": **********.893027, "relative_start": 1.005082130432129, "end": **********.893027, "relative_end": **********.893027, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal", "start": **********.893251, "relative_start": 1.0053060054779053, "end": **********.893251, "relative_end": **********.893251, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::script", "start": **********.895829, "relative_start": 1.0078840255737305, "end": **********.895829, "relative_end": **********.895829, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.master", "start": **********.896873, "relative_start": 1.0089280605316162, "end": **********.896873, "relative_end": **********.896873, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.vertical.partials.before-content", "start": **********.899389, "relative_start": 1.011444091796875, "end": **********.899389, "relative_end": **********.899389, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.vertical.partials.header", "start": **********.899829, "relative_start": 1.0118839740753174, "end": **********.899829, "relative_end": **********.899829, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::3a4eb377d01a3c4bb09865b43ffbd313", "start": **********.90113, "relative_start": 1.0131850242614746, "end": **********.90113, "relative_end": **********.90113, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.logo", "start": **********.901887, "relative_start": 1.013942003250122, "end": **********.901887, "relative_end": **********.901887, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::global-search.navbar-input", "start": **********.913916, "relative_start": 1.0259711742401123, "end": **********.913916, "relative_end": **********.913916, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.text-input", "start": **********.91452, "relative_start": 1.0265750885009766, "end": **********.91452, "relative_end": **********.91452, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.label", "start": **********.915474, "relative_start": 1.027529001235962, "end": **********.915474, "relative_end": **********.915474, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.error", "start": **********.916042, "relative_start": 1.028097152709961, "end": **********.916042, "relative_end": **********.916042, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.916424, "relative_start": 1.0284790992736816, "end": **********.916424, "relative_end": **********.916424, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.917226, "relative_start": 1.0292811393737793, "end": **********.917226, "relative_end": **********.917226, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d2cfde89f704c31422aff2fae16ddb81", "start": **********.918157, "relative_start": 1.030212163925171, "end": **********.918157, "relative_end": **********.918157, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.theme-toggle", "start": **********.918869, "relative_start": 1.0309240818023682, "end": **********.918869, "relative_end": **********.918869, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::a5645d2a1f3c74251fc89224c575fed8", "start": **********.920172, "relative_start": 1.0322270393371582, "end": **********.920172, "relative_end": **********.920172, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::notification.nav-item", "start": **********.921675, "relative_start": 1.0337300300598145, "end": **********.921675, "relative_end": **********.921675, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::639d159f54869d7a8362974885dec505", "start": **********.9226, "relative_start": 1.0346550941467285, "end": **********.9226, "relative_end": **********.9226, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/contact::partials.notification", "start": **********.925287, "relative_start": 1.0373420715332031, "end": **********.925287, "relative_end": **********.925287, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::cf41524c2db4e8ac4f30aba28550db55", "start": **********.926467, "relative_start": 1.0385220050811768, "end": **********.926467, "relative_end": **********.926467, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.title", "start": **********.927596, "relative_start": 1.0396511554718018, "end": **********.927596, "relative_end": **********.927596, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.actions", "start": **********.928245, "relative_start": 1.0403001308441162, "end": **********.928245, "relative_end": **********.928245, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.header.index", "start": **********.928541, "relative_start": 1.0405960083007812, "end": **********.928541, "relative_end": **********.928541, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.index", "start": **********.942899, "relative_start": 1.0549540519714355, "end": **********.942899, "relative_end": **********.942899, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::orders.notification", "start": **********.960535, "relative_start": 1.0725901126861572, "end": **********.960535, "relative_end": **********.960535, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::8394ebb1c2841e3a1166cd3fb0a6e03f", "start": **********.961892, "relative_start": 1.0739469528198242, "end": **********.961892, "relative_end": **********.961892, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.header.index", "start": **********.962619, "relative_start": 1.074674129486084, "end": **********.962619, "relative_end": **********.962619, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.index", "start": **********.979222, "relative_start": 1.0912771224975586, "end": **********.979222, "relative_end": **********.979222, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.user-menu", "start": **********.979793, "relative_start": 1.0918481349945068, "end": **********.979793, "relative_end": **********.979793, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::dropdown.item", "start": **********.983728, "relative_start": 1.095782995223999, "end": **********.983728, "relative_end": **********.983728, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d059faaba602d6895d68258ab3c890a6", "start": **********.984837, "relative_start": 1.0968921184539795, "end": **********.984837, "relative_end": **********.984837, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::dropdown.item", "start": **********.985521, "relative_start": 1.0975761413574219, "end": **********.985521, "relative_end": **********.985521, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::a3cb4601eb64a80dc01a3c268590a3c8", "start": **********.98654, "relative_start": 1.098595142364502, "end": **********.98654, "relative_end": **********.98654, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::dropdown.index", "start": **********.987108, "relative_start": 1.0991630554199219, "end": **********.987108, "relative_end": **********.987108, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.vertical.partials.aside", "start": **********.98772, "relative_start": 1.0997750759124756, "end": **********.98772, "relative_end": **********.98772, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::3a4eb377d01a3c4bb09865b43ffbd313", "start": **********.98851, "relative_start": 1.100564956665039, "end": **********.98851, "relative_end": **********.98851, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.logo", "start": **********.988737, "relative_start": 1.1007921695709229, "end": **********.988737, "relative_end": **********.988737, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::dropdown.item", "start": **********.990014, "relative_start": 1.1020691394805908, "end": **********.990014, "relative_end": **********.990014, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d059faaba602d6895d68258ab3c890a6", "start": **********.990514, "relative_start": 1.1025691032409668, "end": **********.990514, "relative_end": **********.990514, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::dropdown.item", "start": **********.99082, "relative_start": 1.102874994277954, "end": **********.99082, "relative_end": **********.99082, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::a3cb4601eb64a80dc01a3c268590a3c8", "start": **********.991333, "relative_start": 1.1033880710601807, "end": **********.991333, "relative_end": **********.991333, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::dropdown.index", "start": **********.991562, "relative_start": 1.1036169528961182, "end": **********.991562, "relative_end": **********.991562, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.vertical.partials.sidebar", "start": **********.992073, "relative_start": 1.104128122329712, "end": **********.992073, "relative_end": **********.992073, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav", "start": **********.992436, "relative_start": 1.1044909954071045, "end": **********.992436, "relative_end": **********.992436, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.00887, "relative_start": 1.120924949645996, "end": **********.00887, "relative_end": **********.00887, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.009658, "relative_start": 1.1217131614685059, "end": **********.009658, "relative_end": **********.009658, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::e3b17f7ce9738894b58a8b70b9624457", "start": **********.011247, "relative_start": 1.1233019828796387, "end": **********.011247, "relative_end": **********.011247, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.012199, "relative_start": 1.1242539882659912, "end": **********.012199, "relative_end": **********.012199, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.012846, "relative_start": 1.1249010562896729, "end": **********.012846, "relative_end": **********.012846, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0c6e6838aa476b78aace81114936689c", "start": **********.014608, "relative_start": 1.1266629695892334, "end": **********.014608, "relative_end": **********.014608, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.015703, "relative_start": 1.1277580261230469, "end": **********.015703, "relative_end": **********.015703, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::navbar.badge-count", "start": **********.016219, "relative_start": 1.1282739639282227, "end": **********.016219, "relative_end": **********.016219, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.016704, "relative_start": 1.1287591457366943, "end": **********.016704, "relative_end": **********.016704, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::37fae22c8e215ea2e54e69a5e3a007cc", "start": **********.018548, "relative_start": 1.1306030750274658, "end": **********.018548, "relative_end": **********.018548, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.019246, "relative_start": 1.1313011646270752, "end": **********.019246, "relative_end": **********.019246, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::8916176d99d4ae2024cd36e11e35b821", "start": **********.020386, "relative_start": 1.1324410438537598, "end": **********.020386, "relative_end": **********.020386, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.021069, "relative_start": 1.1331241130828857, "end": **********.021069, "relative_end": **********.021069, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::navbar.badge-count", "start": **********.021345, "relative_start": 1.1333999633789062, "end": **********.021345, "relative_end": **********.021345, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.021719, "relative_start": 1.1337740421295166, "end": **********.021719, "relative_end": **********.021719, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::bd27433a6607127acdaf6dc541ab2435", "start": **********.022807, "relative_start": 1.134861946105957, "end": **********.022807, "relative_end": **********.022807, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.023473, "relative_start": 1.1355280876159668, "end": **********.023473, "relative_end": **********.023473, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5f4f9ebbae249bdc8b0d599a1ac6ad06", "start": **********.024716, "relative_start": 1.1367709636688232, "end": **********.024716, "relative_end": **********.024716, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.025632, "relative_start": 1.1376869678497314, "end": **********.025632, "relative_end": **********.025632, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::navbar.badge-count", "start": **********.026012, "relative_start": 1.1380670070648193, "end": **********.026012, "relative_end": **********.026012, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.026408, "relative_start": 1.138463020324707, "end": **********.026408, "relative_end": **********.026408, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::080b92e00b37bcc97c1cd249894494a2", "start": **********.027711, "relative_start": 1.139765977859497, "end": **********.027711, "relative_end": **********.027711, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.029135, "relative_start": 1.1411900520324707, "end": **********.029135, "relative_end": **********.029135, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6a26943e77184871de1629f41c534094", "start": **********.031369, "relative_start": 1.1434240341186523, "end": **********.031369, "relative_end": **********.031369, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.032335, "relative_start": 1.1443901062011719, "end": **********.032335, "relative_end": **********.032335, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::8f29f8012139c7a3eb6593c906e1db38", "start": **********.033802, "relative_start": 1.1458570957183838, "end": **********.033802, "relative_end": **********.033802, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.034726, "relative_start": 1.1467809677124023, "end": **********.034726, "relative_end": **********.034726, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::navbar.badge-count", "start": **********.035046, "relative_start": 1.1471011638641357, "end": **********.035046, "relative_end": **********.035046, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.035493, "relative_start": 1.147547960281372, "end": **********.035493, "relative_end": **********.035493, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4553f5b37130b2effba490dbdf5419d2", "start": **********.037379, "relative_start": 1.1494340896606445, "end": **********.037379, "relative_end": **********.037379, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.0382, "relative_start": 1.1502549648284912, "end": **********.0382, "relative_end": **********.0382, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::59c947fc9b2121a5885d4f4e7b1242d8", "start": **********.039916, "relative_start": 1.1519711017608643, "end": **********.039916, "relative_end": **********.039916, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.040779, "relative_start": 1.152834177017212, "end": **********.040779, "relative_end": **********.040779, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1e9698c460b468bfcaf0f7dbbebf9bf0", "start": **********.042044, "relative_start": 1.1540989875793457, "end": **********.042044, "relative_end": **********.042044, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.042843, "relative_start": 1.1548981666564941, "end": **********.042843, "relative_end": **********.042843, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0d4eb4544a5328bea40b7b01743b8f82", "start": **********.044183, "relative_start": 1.156238079071045, "end": **********.044183, "relative_end": **********.044183, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.045332, "relative_start": 1.1573870182037354, "end": **********.045332, "relative_end": **********.045332, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5270fef4db64e6c2fedf42ea8ac88f25", "start": **********.047528, "relative_start": 1.1595830917358398, "end": **********.047528, "relative_end": **********.047528, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.048399, "relative_start": 1.1604540348052979, "end": **********.048399, "relative_end": **********.048399, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::84f17fac377525e2e49f32058361220b", "start": **********.050292, "relative_start": 1.1623470783233643, "end": **********.050292, "relative_end": **********.050292, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.051183, "relative_start": 1.1632380485534668, "end": **********.051183, "relative_end": **********.051183, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5270fef4db64e6c2fedf42ea8ac88f25", "start": **********.052518, "relative_start": 1.1645729541778564, "end": **********.052518, "relative_end": **********.052518, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.052986, "relative_start": 1.1650409698486328, "end": **********.052986, "relative_end": **********.052986, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1c8617dee734f51544a3883923ddca6f", "start": **********.054751, "relative_start": 1.1668059825897217, "end": **********.054751, "relative_end": **********.054751, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.055521, "relative_start": 1.1675760746002197, "end": **********.055521, "relative_end": **********.055521, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::3d3bfe5e8598abeb74083f6c26233cb5", "start": **********.056943, "relative_start": 1.1689980030059814, "end": **********.056943, "relative_end": **********.056943, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.057657, "relative_start": 1.1697120666503906, "end": **********.057657, "relative_end": **********.057657, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::35fe997a7b87ef55d749630606a50a1b", "start": **********.059443, "relative_start": 1.1714980602264404, "end": **********.059443, "relative_end": **********.059443, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.063234, "relative_start": 1.1752891540527344, "end": **********.063234, "relative_end": **********.063234, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::a4f1583597dec7e67a8ae044f0915dbe", "start": **********.06538, "relative_start": 1.1774351596832275, "end": **********.06538, "relative_end": **********.06538, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.066116, "relative_start": 1.178171157836914, "end": **********.066116, "relative_end": **********.066116, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c0cbd16b0cc2226ec5536610974ba3c3", "start": **********.067855, "relative_start": 1.1799099445343018, "end": **********.067855, "relative_end": **********.067855, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.068693, "relative_start": 1.1807479858398438, "end": **********.068693, "relative_end": **********.068693, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::70b8df706e60982a72f15e9e2d486203", "start": **********.070639, "relative_start": 1.1826939582824707, "end": **********.070639, "relative_end": **********.070639, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.071436, "relative_start": 1.1834909915924072, "end": **********.071436, "relative_end": **********.071436, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.072034, "relative_start": 1.184088945388794, "end": **********.072034, "relative_end": **********.072034, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::dc78b90963e9d9963376e0e829411cea", "start": **********.073803, "relative_start": 1.1858580112457275, "end": **********.073803, "relative_end": **********.073803, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.074736, "relative_start": 1.186791181564331, "end": **********.074736, "relative_end": **********.074736, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7a6e3d0dfcd673b5659893aa4dd54e33", "start": **********.076002, "relative_start": 1.1880569458007812, "end": **********.076002, "relative_end": **********.076002, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.077257, "relative_start": 1.1893119812011719, "end": **********.077257, "relative_end": **********.077257, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::481a833ebeb573258c941c925aa45f7b", "start": **********.079144, "relative_start": 1.1911990642547607, "end": **********.079144, "relative_end": **********.079144, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.079989, "relative_start": 1.1920440196990967, "end": **********.079989, "relative_end": **********.079989, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b42bb0aa5fceca31ad61711414a614f0", "start": **********.081423, "relative_start": 1.1934781074523926, "end": **********.081423, "relative_end": **********.081423, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.082302, "relative_start": 1.19435715675354, "end": **********.082302, "relative_end": **********.082302, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.083032, "relative_start": 1.19508695602417, "end": **********.083032, "relative_end": **********.083032, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1c969038219bd5c599f1ca2d81401cea", "start": **********.08554, "relative_start": 1.1975951194763184, "end": **********.08554, "relative_end": **********.08554, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.086366, "relative_start": 1.1984210014343262, "end": **********.086366, "relative_end": **********.086366, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::navbar.badge-count", "start": **********.086673, "relative_start": 1.198728084564209, "end": **********.086673, "relative_end": **********.086673, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.087098, "relative_start": 1.199152946472168, "end": **********.087098, "relative_end": **********.087098, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::471e83668278198d730a7a3f4a475d45", "start": **********.088339, "relative_start": 1.2003941535949707, "end": **********.088339, "relative_end": **********.088339, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.089088, "relative_start": 1.2011430263519287, "end": **********.089088, "relative_end": **********.089088, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1c969038219bd5c599f1ca2d81401cea", "start": **********.089805, "relative_start": 1.201859951019287, "end": **********.089805, "relative_end": **********.089805, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.09024, "relative_start": 1.2022950649261475, "end": **********.09024, "relative_end": **********.09024, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d7b40194b2b4ba91a975fc9aafe2d3e8", "start": **********.092066, "relative_start": 1.2041211128234863, "end": **********.092066, "relative_end": **********.092066, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.092829, "relative_start": 1.2048840522766113, "end": **********.092829, "relative_end": **********.092829, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::navbar.badge-count", "start": **********.093127, "relative_start": 1.2051820755004883, "end": **********.093127, "relative_end": **********.093127, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.093594, "relative_start": 1.2056491374969482, "end": **********.093594, "relative_end": **********.093594, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::70b8df706e60982a72f15e9e2d486203", "start": **********.094701, "relative_start": 1.2067561149597168, "end": **********.094701, "relative_end": **********.094701, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.0952, "relative_start": 1.2072551250457764, "end": **********.0952, "relative_end": **********.0952, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::acb69140835a74210411469faeab3034", "start": **********.09714, "relative_start": 1.2091951370239258, "end": **********.09714, "relative_end": **********.09714, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.097956, "relative_start": 1.2100110054016113, "end": **********.097956, "relative_end": **********.097956, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::navbar.badge-count", "start": **********.098267, "relative_start": 1.2103221416473389, "end": **********.098267, "relative_end": **********.098267, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.098698, "relative_start": 1.2107529640197754, "end": **********.098698, "relative_end": **********.098698, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::19cd49cd69455e40dc223df6b4eaf954", "start": **********.10027, "relative_start": 1.212325096130371, "end": **********.10027, "relative_end": **********.10027, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.100997, "relative_start": 1.2130520343780518, "end": **********.100997, "relative_end": **********.100997, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.10158, "relative_start": 1.213634967803955, "end": **********.10158, "relative_end": **********.10158, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::2979b72aeeca0047ecdecc3ad66e7e16", "start": **********.10272, "relative_start": 1.2147750854492188, "end": **********.10272, "relative_end": **********.10272, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.103446, "relative_start": 1.215501070022583, "end": **********.103446, "relative_end": **********.103446, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.104015, "relative_start": 1.2160701751708984, "end": **********.104015, "relative_end": **********.104015, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::3c40febd70fcdc245d99ae7cd02cface", "start": **********.105163, "relative_start": 1.2172181606292725, "end": **********.105163, "relative_end": **********.105163, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.105899, "relative_start": 1.217954158782959, "end": **********.105899, "relative_end": **********.105899, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::90ccac5c8bbb25741ef262bfd81c7551", "start": **********.107189, "relative_start": 1.2192440032958984, "end": **********.107189, "relative_end": **********.107189, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.108072, "relative_start": 1.2201271057128906, "end": **********.108072, "relative_end": **********.108072, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7a6e3d0dfcd673b5659893aa4dd54e33", "start": **********.109298, "relative_start": 1.2213530540466309, "end": **********.109298, "relative_end": **********.109298, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.109718, "relative_start": 1.2217731475830078, "end": **********.109718, "relative_end": **********.109718, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0d4eb4544a5328bea40b7b01743b8f82", "start": **********.110818, "relative_start": 1.2228729724884033, "end": **********.110818, "relative_end": **********.110818, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.111315, "relative_start": 1.22337007522583, "end": **********.111315, "relative_end": **********.111315, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.112274, "relative_start": 1.2243289947509766, "end": **********.112274, "relative_end": **********.112274, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d7ced212b797c29086a7922a858f3070", "start": **********.113985, "relative_start": 1.2260401248931885, "end": **********.113985, "relative_end": **********.113985, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.114959, "relative_start": 1.2270140647888184, "end": **********.114959, "relative_end": **********.114959, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::navbar.badge-count", "start": **********.115267, "relative_start": 1.2273221015930176, "end": **********.115267, "relative_end": **********.115267, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.115712, "relative_start": 1.227766990661621, "end": **********.115712, "relative_end": **********.115712, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9d41a7757b46012fb4a0d6634d04a1e0", "start": **********.116894, "relative_start": 1.2289490699768066, "end": **********.116894, "relative_end": **********.116894, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.11752, "relative_start": 1.2295751571655273, "end": **********.11752, "relative_end": **********.11752, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::navbar.badge-count", "start": **********.117839, "relative_start": 1.2298941612243652, "end": **********.117839, "relative_end": **********.117839, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.118326, "relative_start": 1.2303810119628906, "end": **********.118326, "relative_end": **********.118326, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::90ccac5c8bbb25741ef262bfd81c7551", "start": **********.118996, "relative_start": 1.231050968170166, "end": **********.118996, "relative_end": **********.118996, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.119403, "relative_start": 1.2314579486846924, "end": **********.119403, "relative_end": **********.119403, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::86b7e33bd2198279086ebb1f21c0e2cc", "start": **********.120736, "relative_start": 1.2327909469604492, "end": **********.120736, "relative_end": **********.120736, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.121446, "relative_start": 1.2335009574890137, "end": **********.121446, "relative_end": **********.121446, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.122016, "relative_start": 1.2340710163116455, "end": **********.122016, "relative_end": **********.122016, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::8c52d9b1ef0685ec10fdc3e877751e02", "start": **********.123186, "relative_start": 1.235241174697876, "end": **********.123186, "relative_end": **********.123186, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.123894, "relative_start": 1.2359490394592285, "end": **********.123894, "relative_end": **********.123894, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.1245, "relative_start": 1.2365550994873047, "end": **********.1245, "relative_end": **********.1245, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::42668be6e8e5266862c6994eaa88bb55", "start": **********.126254, "relative_start": 1.2383091449737549, "end": **********.126254, "relative_end": **********.126254, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.127068, "relative_start": 1.2391231060028076, "end": **********.127068, "relative_end": **********.127068, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::navbar.badge-count", "start": **********.127421, "relative_start": 1.239475965499878, "end": **********.127421, "relative_end": **********.127421, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.128022, "relative_start": 1.240077018737793, "end": **********.128022, "relative_end": **********.128022, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.128788, "relative_start": 1.2408430576324463, "end": **********.128788, "relative_end": **********.128788, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::311d8c591d63d3cbd12dceb2fb1ac1c1", "start": **********.130434, "relative_start": 1.2424890995025635, "end": **********.130434, "relative_end": **********.130434, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.131352, "relative_start": 1.2434070110321045, "end": **********.131352, "relative_end": **********.131352, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9d41a7757b46012fb4a0d6634d04a1e0", "start": **********.132206, "relative_start": 1.2442610263824463, "end": **********.132206, "relative_end": **********.132206, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.132637, "relative_start": 1.244692087173462, "end": **********.132637, "relative_end": **********.132637, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::86b7e33bd2198279086ebb1f21c0e2cc", "start": **********.133314, "relative_start": 1.2453689575195312, "end": **********.133314, "relative_end": **********.133314, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.13376, "relative_start": 1.2458150386810303, "end": **********.13376, "relative_end": **********.13376, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.134339, "relative_start": 1.246394157409668, "end": **********.134339, "relative_end": **********.134339, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::cf41524c2db4e8ac4f30aba28550db55", "start": **********.135005, "relative_start": 1.2470600605010986, "end": **********.135005, "relative_end": **********.135005, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.13543, "relative_start": 1.2474851608276367, "end": **********.13543, "relative_end": **********.13543, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::navbar.badge-count", "start": **********.135738, "relative_start": 1.2477929592132568, "end": **********.135738, "relative_end": **********.135738, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.136164, "relative_start": 1.2482190132141113, "end": **********.136164, "relative_end": **********.136164, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::613233f0072612a02c74dd1699c0b74c", "start": **********.137353, "relative_start": 1.2494080066680908, "end": **********.137353, "relative_end": **********.137353, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.138163, "relative_start": 1.250218152999878, "end": **********.138163, "relative_end": **********.138163, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::16225ede2ef5cc17292fd2eb9026fc80", "start": **********.139317, "relative_start": 1.2513720989227295, "end": **********.139317, "relative_end": **********.139317, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.140064, "relative_start": 1.2521190643310547, "end": **********.140064, "relative_end": **********.140064, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.140645, "relative_start": 1.2527000904083252, "end": **********.140645, "relative_end": **********.140645, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::46010cb1cb88bb5ead5d94603a4a3d16", "start": **********.141772, "relative_start": 1.2538270950317383, "end": **********.141772, "relative_end": **********.141772, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.142488, "relative_start": 1.2545430660247803, "end": **********.142488, "relative_end": **********.142488, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.143061, "relative_start": 1.2551159858703613, "end": **********.143061, "relative_end": **********.143061, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::34e3d89351b1208b7f313125eec52879", "start": **********.145371, "relative_start": 1.2574260234832764, "end": **********.145371, "relative_end": **********.145371, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.145895, "relative_start": 1.2579500675201416, "end": **********.145895, "relative_end": **********.145895, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::67034900569133b2c83b32da3dd4f5e5", "start": **********.147042, "relative_start": 1.2590970993041992, "end": **********.147042, "relative_end": **********.147042, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.14774, "relative_start": 1.2597949504852295, "end": **********.14774, "relative_end": **********.14774, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7a6e3d0dfcd673b5659893aa4dd54e33", "start": **********.14871, "relative_start": 1.2607650756835938, "end": **********.14871, "relative_end": **********.14871, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.149128, "relative_start": 1.2611830234527588, "end": **********.149128, "relative_end": **********.149128, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.149693, "relative_start": 1.2617480754852295, "end": **********.149693, "relative_end": **********.149693, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::cf41524c2db4e8ac4f30aba28550db55", "start": **********.150917, "relative_start": 1.262972116470337, "end": **********.150917, "relative_end": **********.150917, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.15133, "relative_start": 1.2633850574493408, "end": **********.15133, "relative_end": **********.15133, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.151882, "relative_start": 1.263936996459961, "end": **********.151882, "relative_end": **********.151882, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c33cfbd01dd1d76718fcd68287a40728", "start": **********.15306, "relative_start": 1.2651150226593018, "end": **********.15306, "relative_end": **********.15306, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.153478, "relative_start": 1.2655329704284668, "end": **********.153478, "relative_end": **********.153478, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0d623715926c24f9fbc8a4b72c106d5d", "start": **********.155483, "relative_start": 1.267538070678711, "end": **********.155483, "relative_end": **********.155483, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.156346, "relative_start": 1.2684011459350586, "end": **********.156346, "relative_end": **********.156346, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6c50fc55276d93f8ed03f5c85273b6cc", "start": **********.158041, "relative_start": 1.2700960636138916, "end": **********.158041, "relative_end": **********.158041, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.158795, "relative_start": 1.2708501815795898, "end": **********.158795, "relative_end": **********.158795, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::084d2b43c9ab4b881d9b34a15580aa2d", "start": **********.160345, "relative_start": 1.272400140762329, "end": **********.160345, "relative_end": **********.160345, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.161448, "relative_start": 1.273503065109253, "end": **********.161448, "relative_end": **********.161448, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b33d20952e90e5c4a596ff58ad010448", "start": **********.163434, "relative_start": 1.275489091873169, "end": **********.163434, "relative_end": **********.163434, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.164301, "relative_start": 1.2763559818267822, "end": **********.164301, "relative_end": **********.164301, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7db8cad89359963c1e9aa8fcc6c89817", "start": **********.165886, "relative_start": 1.2779409885406494, "end": **********.165886, "relative_end": **********.165886, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.166623, "relative_start": 1.2786781787872314, "end": **********.166623, "relative_end": **********.166623, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.167211, "relative_start": 1.279266119003296, "end": **********.167211, "relative_end": **********.167211, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7a6e3d0dfcd673b5659893aa4dd54e33", "start": **********.168901, "relative_start": 1.2809560298919678, "end": **********.168901, "relative_end": **********.168901, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.169341, "relative_start": 1.2813961505889893, "end": **********.169341, "relative_end": **********.169341, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.169957, "relative_start": 1.2820119857788086, "end": **********.169957, "relative_end": **********.169957, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::325be2a8c3ca3843efa76c03adaee1dc", "start": **********.171898, "relative_start": 1.2839529514312744, "end": **********.171898, "relative_end": **********.171898, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.17263, "relative_start": 1.2846851348876953, "end": **********.17263, "relative_end": **********.17263, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::e12a669ffa0346a27198bed32e63b7ba", "start": **********.17379, "relative_start": 1.2858450412750244, "end": **********.17379, "relative_end": **********.17379, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.174512, "relative_start": 1.286566972732544, "end": **********.174512, "relative_end": **********.174512, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9d0b20db301db9a47503a93a879bb206", "start": **********.175616, "relative_start": 1.2876710891723633, "end": **********.175616, "relative_end": **********.175616, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.176284, "relative_start": 1.2883391380310059, "end": **********.176284, "relative_end": **********.176284, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b985af7bcdacbeac70eaf3979ad19f5a", "start": **********.177622, "relative_start": 1.2896771430969238, "end": **********.177622, "relative_end": **********.177622, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.17883, "relative_start": 1.2908849716186523, "end": **********.17883, "relative_end": **********.17883, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::a0bb1d43b71cff86abe626fd376492e9", "start": **********.180563, "relative_start": 1.2926180362701416, "end": **********.180563, "relative_end": **********.180563, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.181338, "relative_start": 1.2933931350708008, "end": **********.181338, "relative_end": **********.181338, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::e25f2b305e6de46c04f91fa1ce50f68f", "start": **********.182686, "relative_start": 1.294741153717041, "end": **********.182686, "relative_end": **********.182686, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.1834, "relative_start": 1.295454978942871, "end": **********.1834, "relative_end": **********.1834, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::69152f707ea1358f8997b77a28e38a6f", "start": **********.185018, "relative_start": 1.2970731258392334, "end": **********.185018, "relative_end": **********.185018, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.185765, "relative_start": 1.2978200912475586, "end": **********.185765, "relative_end": **********.185765, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b85eba35d3b7929c2988678b725baebf", "start": **********.187269, "relative_start": 1.2993240356445312, "end": **********.187269, "relative_end": **********.187269, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.188044, "relative_start": 1.3000991344451904, "end": **********.188044, "relative_end": **********.188044, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1f3d3b83c612f68036b4d79d53ae851e", "start": **********.189294, "relative_start": 1.30134916305542, "end": **********.189294, "relative_end": **********.189294, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.190177, "relative_start": 1.302232027053833, "end": **********.190177, "relative_end": **********.190177, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.190788, "relative_start": 1.3028430938720703, "end": **********.190788, "relative_end": **********.190788, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::f143d1296cea16d82e2c87956e445593", "start": **********.192767, "relative_start": 1.3048219680786133, "end": **********.192767, "relative_end": **********.192767, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.193538, "relative_start": 1.3055930137634277, "end": **********.193538, "relative_end": **********.193538, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::8cae4e5056b67c6778a54389a62ac7a0", "start": **********.195219, "relative_start": 1.3072741031646729, "end": **********.195219, "relative_end": **********.195219, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.195766, "relative_start": 1.3078210353851318, "end": **********.195766, "relative_end": **********.195766, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d5e509b6eb9084ec382ec05ccab41d1a", "start": **********.197232, "relative_start": 1.3092870712280273, "end": **********.197232, "relative_end": **********.197232, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.198009, "relative_start": 1.3100640773773193, "end": **********.198009, "relative_end": **********.198009, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.198602, "relative_start": 1.310657024383545, "end": **********.198602, "relative_end": **********.198602, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::e81a46563ed9378aa4d9a4fcb55e743e", "start": **********.19984, "relative_start": 1.3118951320648193, "end": **********.19984, "relative_end": **********.19984, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.200678, "relative_start": 1.3127331733703613, "end": **********.200678, "relative_end": **********.200678, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b33d20952e90e5c4a596ff58ad010448", "start": **********.202355, "relative_start": 1.3144099712371826, "end": **********.202355, "relative_end": **********.202355, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.202803, "relative_start": 1.3148579597473145, "end": **********.202803, "relative_end": **********.202803, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.20338, "relative_start": 1.3154351711273193, "end": **********.20338, "relative_end": **********.20338, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::86b7e33bd2198279086ebb1f21c0e2cc", "start": **********.204187, "relative_start": 1.316241979598999, "end": **********.204187, "relative_end": **********.204187, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.204649, "relative_start": 1.3167040348052979, "end": **********.204649, "relative_end": **********.204649, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.205241, "relative_start": 1.317296028137207, "end": **********.205241, "relative_end": **********.205241, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::da3c3de008e5793cbbdad005d78f49b1", "start": **********.206457, "relative_start": 1.318511962890625, "end": **********.206457, "relative_end": **********.206457, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.page-header", "start": **********.207485, "relative_start": 1.319540023803711, "end": **********.207485, "relative_end": **********.207485, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::breadcrumb", "start": **********.208061, "relative_start": 1.3201160430908203, "end": **********.208061, "relative_end": **********.208061, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.footer", "start": **********.209018, "relative_start": 1.321073055267334, "end": **********.209018, "relative_end": **********.209018, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.copyright", "start": **********.209461, "relative_start": 1.3215160369873047, "end": **********.209461, "relative_end": **********.209461, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.vertical.partials.after-content", "start": **********.210475, "relative_start": 1.3225300312042236, "end": **********.210475, "relative_end": **********.210475, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::system.license-invalid", "start": **********.211467, "relative_start": 1.3235220909118652, "end": **********.211467, "relative_end": **********.211467, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::alert", "start": **********.212498, "relative_start": 1.3245530128479004, "end": **********.212498, "relative_end": **********.212498, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::system.partials.license-activation-modal", "start": **********.213208, "relative_start": 1.3252630233764648, "end": **********.213208, "relative_end": **********.213208, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::license.form", "start": **********.21391, "relative_start": 1.325965166091919, "end": **********.21391, "relative_end": **********.21391, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::alert", "start": **********.214861, "relative_start": 1.326915979385376, "end": **********.214861, "relative_end": **********.214861, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::27ec08f706fece52ef1cc0ec5563cef9", "start": **********.216086, "relative_start": 1.3281409740447998, "end": **********.216086, "relative_end": **********.216086, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.text-input", "start": **********.216665, "relative_start": 1.3287200927734375, "end": **********.216665, "relative_end": **********.216665, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.label", "start": **********.217412, "relative_start": 1.3294670581817627, "end": **********.217412, "relative_end": **********.217412, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.helper-text", "start": **********.217938, "relative_start": 1.3299930095672607, "end": **********.217938, "relative_end": **********.217938, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.error", "start": **********.218293, "relative_start": 1.330348014831543, "end": **********.218293, "relative_end": **********.218293, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.218677, "relative_start": 1.3307321071624756, "end": **********.218677, "relative_end": **********.218677, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.text-input", "start": **********.219044, "relative_start": 1.331099033355713, "end": **********.219044, "relative_end": **********.219044, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.label", "start": **********.219795, "relative_start": 1.3318500518798828, "end": **********.219795, "relative_end": **********.219795, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.helper-text", "start": **********.220244, "relative_start": 1.332298994064331, "end": **********.220244, "relative_end": **********.220244, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.error", "start": **********.22048, "relative_start": 1.3325350284576416, "end": **********.22048, "relative_end": **********.22048, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.220771, "relative_start": 1.3328261375427246, "end": **********.220771, "relative_end": **********.220771, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.on-off.checkbox", "start": **********.221254, "relative_start": 1.3333091735839844, "end": **********.221254, "relative_end": **********.221254, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::components.form.checkbox", "start": **********.22174, "relative_start": 1.3337950706481934, "end": **********.22174, "relative_end": **********.22174, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.22223, "relative_start": 1.334285020828247, "end": **********.22223, "relative_end": **********.22223, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.22268, "relative_start": 1.3347351551055908, "end": **********.22268, "relative_end": **********.22268, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form-group", "start": **********.223478, "relative_start": 1.3355331420898438, "end": **********.223478, "relative_end": **********.223478, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.223838, "relative_start": 1.335893154144287, "end": **********.223838, "relative_end": **********.223838, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal", "start": **********.224197, "relative_start": 1.336251974105835, "end": **********.224197, "relative_end": **********.224197, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.close-button", "start": **********.224875, "relative_start": 1.3369300365447998, "end": **********.224875, "relative_end": **********.224875, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::global-search.form", "start": **********.225288, "relative_start": 1.3373429775238037, "end": **********.225288, "relative_end": **********.225288, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6f3b10173cc6f5c541f27080145e1a40", "start": **********.226217, "relative_start": 1.3382720947265625, "end": **********.226217, "relative_end": **********.226217, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.text-input", "start": **********.226449, "relative_start": 1.3385040760040283, "end": **********.226449, "relative_end": **********.226449, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.label", "start": **********.227181, "relative_start": 1.3392360210418701, "end": **********.227181, "relative_end": **********.227181, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.error", "start": **********.227665, "relative_start": 1.3397200107574463, "end": **********.227665, "relative_end": **********.227665, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.228229, "relative_start": 1.3402841091156006, "end": **********.228229, "relative_end": **********.228229, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.index", "start": **********.228627, "relative_start": 1.340682029724121, "end": **********.228627, "relative_end": **********.228627, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::53362b6227831afe8e4d7d3436ab607f", "start": **********.229616, "relative_start": 1.3416709899902344, "end": **********.229616, "relative_end": **********.229616, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e76aef074ac8ea84c711b8437720a22", "start": **********.230944, "relative_start": 1.34299898147583, "end": **********.230944, "relative_end": **********.230944, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::04edbddbda254d131a3439b11c880f12", "start": **********.232151, "relative_start": 1.3442060947418213, "end": **********.232151, "relative_end": **********.232151, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal", "start": **********.232699, "relative_start": 1.3447539806365967, "end": **********.232699, "relative_end": **********.232699, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::custom-template", "start": **********.233659, "relative_start": 1.3457140922546387, "end": **********.233659, "relative_end": **********.233659, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/media::partials.media", "start": **********.234391, "relative_start": 1.3464460372924805, "end": **********.234391, "relative_end": **********.234391, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.close-button", "start": **********.23518, "relative_start": 1.3472349643707275, "end": **********.23518, "relative_end": **********.23518, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::loading", "start": **********.235518, "relative_start": 1.3475730419158936, "end": **********.235518, "relative_end": **********.235518, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.text-input", "start": **********.236056, "relative_start": 1.3481111526489258, "end": **********.236056, "relative_end": **********.236056, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.label", "start": **********.236842, "relative_start": 1.3488969802856445, "end": **********.236842, "relative_end": **********.236842, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.error", "start": **********.237202, "relative_start": 1.349256992340088, "end": **********.237202, "relative_end": **********.237202, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.237494, "relative_start": 1.3495490550994873, "end": **********.237494, "relative_end": **********.237494, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.checkbox", "start": **********.237862, "relative_start": 1.3499171733856201, "end": **********.237862, "relative_end": **********.237862, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.238861, "relative_start": 1.3509161472320557, "end": **********.238861, "relative_end": **********.238861, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.239435, "relative_start": 1.3514900207519531, "end": **********.239435, "relative_end": **********.239435, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal", "start": **********.240108, "relative_start": 1.3521630764007568, "end": **********.240108, "relative_end": **********.240108, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.close-button", "start": **********.240961, "relative_start": 1.3530161380767822, "end": **********.240961, "relative_end": **********.240961, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/media::config", "start": **********.241414, "relative_start": 1.3534691333770752, "end": **********.241414, "relative_end": **********.241414, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::debug-badge", "start": **********.469147, "relative_start": 1.5812020301818848, "end": **********.469147, "relative_end": **********.469147, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.action", "start": **********.469893, "relative_start": 1.5819480419158936, "end": **********.469893, "relative_end": **********.469893, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.alert", "start": **********.470641, "relative_start": 1.5826959609985352, "end": **********.470641, "relative_end": **********.470641, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.close-button", "start": **********.471414, "relative_start": 1.5834691524505615, "end": **********.471414, "relative_end": **********.471414, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::93ebde3601860db875cfe9a96164bda6", "start": **********.472475, "relative_start": 1.5845301151275635, "end": **********.472475, "relative_end": **********.472475, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal", "start": **********.473088, "relative_start": 1.5851430892944336, "end": **********.473088, "relative_end": **********.473088, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.action", "start": **********.474082, "relative_start": 1.586137056350708, "end": **********.474082, "relative_end": **********.474082, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.alert", "start": **********.474691, "relative_start": 1.5867459774017334, "end": **********.474691, "relative_end": **********.474691, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.close-button", "start": **********.475301, "relative_start": 1.5873560905456543, "end": **********.475301, "relative_end": **********.475301, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::16c15d36d71c18d83a7e9e7e1b68a92b", "start": **********.475952, "relative_start": 1.5880069732666016, "end": **********.475952, "relative_end": **********.475952, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal", "start": **********.476589, "relative_start": 1.588644027709961, "end": **********.476589, "relative_end": **********.476589, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::layouts.base", "start": **********.477682, "relative_start": 1.5897371768951416, "end": **********.477682, "relative_end": **********.477682, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::components.layouts.header", "start": **********.478808, "relative_start": 1.5908629894256592, "end": **********.478808, "relative_end": **********.478808, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: assets::header", "start": **********.482033, "relative_start": 1.594088077545166, "end": **********.482033, "relative_end": **********.482033, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::elements.common", "start": **********.484082, "relative_start": 1.5961370468139648, "end": **********.484082, "relative_end": **********.484082, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: assets::footer", "start": **********.485812, "relative_start": 1.5978670120239258, "end": **********.485812, "relative_end": **********.485812, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::notification.notification", "start": **********.486776, "relative_start": 1.5988311767578125, "end": **********.486776, "relative_end": **********.486776, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 74061160, "peak_usage_str": "71MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.21.0", "PHP Version": "8.3.17", "Environment": "local", "Debug Mode": "Enabled", "URL": "martfury.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 386, "nb_templates": 386, "templates": [{"name": "1x core/table::bulk-changes", "param_count": null, "params": [], "start": **********.735233, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/table/resources/views/bulk-changes.blade.phpcore/table::bulk-changes", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Fbulk-changes.blade.php&line=1", "ajax": false, "filename": "bulk-changes.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/table::bulk-changes"}, {"name": "8x 8def1252668913628243c4d363bee1ef::dropdown.item", "param_count": null, "params": [], "start": **********.738846, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/dropdown/item.blade.php8def1252668913628243c4d363bee1ef::dropdown.item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fdropdown%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 8, "name_original": "8def1252668913628243c4d363bee1ef::dropdown.item"}, {"name": "1x __components::53dbbce7846c20f5734d935076bf04ca", "param_count": null, "params": [], "start": **********.741784, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/53dbbce7846c20f5734d935076bf04ca.blade.php__components::53dbbce7846c20f5734d935076bf04ca", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F53dbbce7846c20f5734d935076bf04ca.blade.php&line=1", "ajax": false, "filename": "53dbbce7846c20f5734d935076bf04ca.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::53dbbce7846c20f5734d935076bf04ca"}, {"name": "2x core/table::partials.create", "param_count": null, "params": [], "start": **********.74673, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/table/resources/views/partials/create.blade.phpcore/table::partials.create", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Fpartials%2Fcreate.blade.php&line=1", "ajax": false, "filename": "create.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/table::partials.create"}, {"name": "2x __components::baaf02f6c56c328f3c307011a60b6b9f", "param_count": null, "params": [], "start": **********.747707, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/baaf02f6c56c328f3c307011a60b6b9f.blade.php__components::baaf02f6c56c328f3c307011a60b6b9f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fbaaf02f6c56c328f3c307011a60b6b9f.blade.php&line=1", "ajax": false, "filename": "baaf02f6c56c328f3c307011a60b6b9f.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::baaf02f6c56c328f3c307011a60b6b9f"}, {"name": "1x core/table::table-info", "param_count": null, "params": [], "start": **********.751006, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/table/resources/views/table-info.blade.phpcore/table::table-info", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Ftable-info.blade.php&line=1", "ajax": false, "filename": "table-info.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/table::table-info"}, {"name": "2x __components::c33cfbd01dd1d76718fcd68287a40728", "param_count": null, "params": [], "start": **********.752363, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/c33cfbd01dd1d76718fcd68287a40728.blade.php__components::c33cfbd01dd1d76718fcd68287a40728", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fc33cfbd01dd1d76718fcd68287a40728.blade.php&line=1", "ajax": false, "filename": "c33cfbd01dd1d76718fcd68287a40728.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::c33cfbd01dd1d76718fcd68287a40728"}, {"name": "1x 8def1252668913628243c4d363bee1ef::badge", "param_count": null, "params": [], "start": **********.753126, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/badge.blade.php8def1252668913628243c4d363bee1ef::badge", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fbadge.blade.php&line=1", "ajax": false, "filename": "badge.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::badge"}, {"name": "1x core/table::table", "param_count": null, "params": [], "start": **********.763013, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/table/resources/views/table.blade.phpcore/table::table", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Ftable.blade.php&line=1", "ajax": false, "filename": "table.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/table::table"}, {"name": "1x core/table::base-table", "param_count": null, "params": [], "start": **********.763621, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/table/resources/views/base-table.blade.phpcore/table::base-table", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Fbase-table.blade.php&line=1", "ajax": false, "filename": "base-table.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/table::base-table"}, {"name": "14x 8def1252668913628243c4d363bee1ef::button", "param_count": null, "params": [], "start": **********.781305, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/button.blade.php8def1252668913628243c4d363bee1ef::button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fbutton.blade.php&line=1", "ajax": false, "filename": "button.blade.php", "line": "?"}, "render_count": 14, "name_original": "8def1252668913628243c4d363bee1ef::button"}, {"name": "1x __components::14ad31fb3af14d3ba24d3c578af35e73", "param_count": null, "params": [], "start": **********.783092, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/14ad31fb3af14d3ba24d3c578af35e73.blade.php__components::14ad31fb3af14d3ba24d3c578af35e73", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F14ad31fb3af14d3ba24d3c578af35e73.blade.php&line=1", "ajax": false, "filename": "14ad31fb3af14d3ba24d3c578af35e73.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::14ad31fb3af14d3ba24d3c578af35e73"}, {"name": "1x core/table::filter", "param_count": null, "params": [], "start": **********.809453, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/table/resources/views/filter.blade.phpcore/table::filter", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Ffilter.blade.php&line=1", "ajax": false, "filename": "filter.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/table::filter"}, {"name": "4x 8def1252668913628243c4d363bee1ef::form.select", "param_count": null, "params": [], "start": **********.811328, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/form/select.blade.php8def1252668913628243c4d363bee1ef::form.select", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fselect.blade.php&line=1", "ajax": false, "filename": "select.blade.php", "line": "?"}, "render_count": 4, "name_original": "8def1252668913628243c4d363bee1ef::form.select"}, {"name": "11x 8def1252668913628243c4d363bee1ef::form-group", "param_count": null, "params": [], "start": **********.813877, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/form-group.blade.php8def1252668913628243c4d363bee1ef::form-group", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform-group.blade.php&line=1", "ajax": false, "filename": "form-group.blade.php", "line": "?"}, "render_count": 11, "name_original": "8def1252668913628243c4d363bee1ef::form-group"}, {"name": "1x __components::d7b9d721822c50540453b6fa0f4bd081", "param_count": null, "params": [], "start": **********.816855, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/d7b9d721822c50540453b6fa0f4bd081.blade.php__components::d7b9d721822c50540453b6fa0f4bd081", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fd7b9d721822c50540453b6fa0f4bd081.blade.php&line=1", "ajax": false, "filename": "d7b9d721822c50540453b6fa0f4bd081.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::d7b9d721822c50540453b6fa0f4bd081"}, {"name": "2x __components::0c8728926b3975e33a051ebb6ef68e5d", "param_count": null, "params": [], "start": **********.822176, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/0c8728926b3975e33a051ebb6ef68e5d.blade.php__components::0c8728926b3975e33a051ebb6ef68e5d", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F0c8728926b3975e33a051ebb6ef68e5d.blade.php&line=1", "ajax": false, "filename": "0c8728926b3975e33a051ebb6ef68e5d.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::0c8728926b3975e33a051ebb6ef68e5d"}, {"name": "2x 8def1252668913628243c4d363bee1ef::form.index", "param_count": null, "params": [], "start": **********.822818, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/form/index.blade.php8def1252668913628243c4d363bee1ef::form.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "8def1252668913628243c4d363bee1ef::form.index"}, {"name": "1x 8def1252668913628243c4d363bee1ef::card.body.index", "param_count": null, "params": [], "start": **********.823271, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/card/body/index.blade.php8def1252668913628243c4d363bee1ef::card.body.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Fbody%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::card.body.index"}, {"name": "4x 8def1252668913628243c4d363bee1ef::card.index", "param_count": null, "params": [], "start": **********.823626, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/card/index.blade.php8def1252668913628243c4d363bee1ef::card.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 4, "name_original": "8def1252668913628243c4d363bee1ef::card.index"}, {"name": "1x core/table::bulk-action", "param_count": null, "params": [], "start": **********.845282, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/table/resources/views/bulk-action.blade.phpcore/table::bulk-action", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Fbulk-action.blade.php&line=1", "ajax": false, "filename": "bulk-action.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/table::bulk-action"}, {"name": "3x 8def1252668913628243c4d363bee1ef::dropdown.index", "param_count": null, "params": [], "start": **********.847232, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/dropdown/index.blade.php8def1252668913628243c4d363bee1ef::dropdown.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fdropdown%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 3, "name_original": "8def1252668913628243c4d363bee1ef::dropdown.index"}, {"name": "2x __components::6f3b10173cc6f5c541f27080145e1a40", "param_count": null, "params": [], "start": **********.859286, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/6f3b10173cc6f5c541f27080145e1a40.blade.php__components::6f3b10173cc6f5c541f27080145e1a40", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F6f3b10173cc6f5c541f27080145e1a40.blade.php&line=1", "ajax": false, "filename": "6f3b10173cc6f5c541f27080145e1a40.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::6f3b10173cc6f5c541f27080145e1a40"}, {"name": "1x __components::42e1966f95bce065f65d4b22e53f3772", "param_count": null, "params": [], "start": **********.860241, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/42e1966f95bce065f65d4b22e53f3772.blade.php__components::42e1966f95bce065f65d4b22e53f3772", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F42e1966f95bce065f65d4b22e53f3772.blade.php&line=1", "ajax": false, "filename": "42e1966f95bce065f65d4b22e53f3772.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::42e1966f95bce065f65d4b22e53f3772"}, {"name": "1x __components::b4335e9ff8c721e014fa31a65e4454d3", "param_count": null, "params": [], "start": **********.863513, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/b4335e9ff8c721e014fa31a65e4454d3.blade.php__components::b4335e9ff8c721e014fa31a65e4454d3", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fb4335e9ff8c721e014fa31a65e4454d3.blade.php&line=1", "ajax": false, "filename": "b4335e9ff8c721e014fa31a65e4454d3.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::b4335e9ff8c721e014fa31a65e4454d3"}, {"name": "1x __components::77e239c329a8f8a48840d651c6a8a9ee", "param_count": null, "params": [], "start": **********.864804, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/77e239c329a8f8a48840d651c6a8a9ee.blade.php__components::77e239c329a8f8a48840d651c6a8a9ee", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F77e239c329a8f8a48840d651c6a8a9ee.blade.php&line=1", "ajax": false, "filename": "77e239c329a8f8a48840d651c6a8a9ee.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::77e239c329a8f8a48840d651c6a8a9ee"}, {"name": "1x __components::7b2a6f88f6865c9dfe436f51388f0b67", "param_count": null, "params": [], "start": **********.865862, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/7b2a6f88f6865c9dfe436f51388f0b67.blade.php__components::7b2a6f88f6865c9dfe436f51388f0b67", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F7b2a6f88f6865c9dfe436f51388f0b67.blade.php&line=1", "ajax": false, "filename": "7b2a6f88f6865c9dfe436f51388f0b67.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::7b2a6f88f6865c9dfe436f51388f0b67"}, {"name": "3x 8def1252668913628243c4d363bee1ef::card.header.index", "param_count": null, "params": [], "start": **********.867446, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/card/header/index.blade.php8def1252668913628243c4d363bee1ef::card.header.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Fheader%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 3, "name_original": "8def1252668913628243c4d363bee1ef::card.header.index"}, {"name": "1x core/table::modal", "param_count": null, "params": [], "start": **********.879654, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/table/resources/views/modal.blade.phpcore/table::modal", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Fmodal.blade.php&line=1", "ajax": false, "filename": "modal.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/table::modal"}, {"name": "6x 8def1252668913628243c4d363bee1ef::modal.action", "param_count": null, "params": [], "start": **********.880686, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/modal/action.blade.php8def1252668913628243c4d363bee1ef::modal.action", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fmodal%2Faction.blade.php&line=1", "ajax": false, "filename": "action.blade.php", "line": "?"}, "render_count": 6, "name_original": "8def1252668913628243c4d363bee1ef::modal.action"}, {"name": "6x 8def1252668913628243c4d363bee1ef::modal.alert", "param_count": null, "params": [], "start": **********.881485, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/modal/alert.blade.php8def1252668913628243c4d363bee1ef::modal.alert", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fmodal%2Falert.blade.php&line=1", "ajax": false, "filename": "alert.blade.php", "line": "?"}, "render_count": 6, "name_original": "8def1252668913628243c4d363bee1ef::modal.alert"}, {"name": "10x 8def1252668913628243c4d363bee1ef::modal.close-button", "param_count": null, "params": [], "start": **********.882332, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/modal/close-button.blade.php8def1252668913628243c4d363bee1ef::modal.close-button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fmodal%2Fclose-button.blade.php&line=1", "ajax": false, "filename": "close-button.blade.php", "line": "?"}, "render_count": 10, "name_original": "8def1252668913628243c4d363bee1ef::modal.close-button"}, {"name": "4x __components::d17cb0db54485d707113609802086895", "param_count": null, "params": [], "start": **********.883261, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/d17cb0db54485d707113609802086895.blade.php__components::d17cb0db54485d707113609802086895", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fd17cb0db54485d707113609802086895.blade.php&line=1", "ajax": false, "filename": "d17cb0db54485d707113609802086895.blade.php", "line": "?"}, "render_count": 4, "name_original": "__components::d17cb0db54485d707113609802086895"}, {"name": "10x 8def1252668913628243c4d363bee1ef::modal", "param_count": null, "params": [], "start": **********.88394, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/modal.blade.php8def1252668913628243c4d363bee1ef::modal", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fmodal.blade.php&line=1", "ajax": false, "filename": "modal.blade.php", "line": "?"}, "render_count": 10, "name_original": "8def1252668913628243c4d363bee1ef::modal"}, {"name": "1x core/table::script", "param_count": null, "params": [], "start": **********.895807, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/table/resources/views/script.blade.phpcore/table::script", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Fscript.blade.php&line=1", "ajax": false, "filename": "script.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/table::script"}, {"name": "1x core/base::layouts.master", "param_count": null, "params": [], "start": **********.896853, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/layouts/master.blade.phpcore/base::layouts.master", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fmaster.blade.php&line=1", "ajax": false, "filename": "master.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.master"}, {"name": "1x core/base::layouts.vertical.partials.before-content", "param_count": null, "params": [], "start": **********.899369, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/layouts/vertical/partials/before-content.blade.phpcore/base::layouts.vertical.partials.before-content", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fvertical%2Fpartials%2Fbefore-content.blade.php&line=1", "ajax": false, "filename": "before-content.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.vertical.partials.before-content"}, {"name": "1x core/base::layouts.vertical.partials.header", "param_count": null, "params": [], "start": **********.89981, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/layouts/vertical/partials/header.blade.phpcore/base::layouts.vertical.partials.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fvertical%2Fpartials%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.vertical.partials.header"}, {"name": "2x __components::3a4eb377d01a3c4bb09865b43ffbd313", "param_count": null, "params": [], "start": **********.901109, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/3a4eb377d01a3c4bb09865b43ffbd313.blade.php__components::3a4eb377d01a3c4bb09865b43ffbd313", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F3a4eb377d01a3c4bb09865b43ffbd313.blade.php&line=1", "ajax": false, "filename": "3a4eb377d01a3c4bb09865b43ffbd313.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::3a4eb377d01a3c4bb09865b43ffbd313"}, {"name": "2x core/base::partials.logo", "param_count": null, "params": [], "start": **********.901868, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/partials/logo.blade.phpcore/base::partials.logo", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fpartials%2Flogo.blade.php&line=1", "ajax": false, "filename": "logo.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/base::partials.logo"}, {"name": "1x core/base::global-search.navbar-input", "param_count": null, "params": [], "start": **********.91389, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/global-search/navbar-input.blade.phpcore/base::global-search.navbar-input", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fglobal-search%2Fnavbar-input.blade.php&line=1", "ajax": false, "filename": "navbar-input.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::global-search.navbar-input"}, {"name": "5x 8def1252668913628243c4d363bee1ef::form.text-input", "param_count": null, "params": [], "start": **********.9145, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/form/text-input.blade.php8def1252668913628243c4d363bee1ef::form.text-input", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Ftext-input.blade.php&line=1", "ajax": false, "filename": "text-input.blade.php", "line": "?"}, "render_count": 5, "name_original": "8def1252668913628243c4d363bee1ef::form.text-input"}, {"name": "5x 8def1252668913628243c4d363bee1ef::form.label", "param_count": null, "params": [], "start": **********.915454, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/form/label.blade.php8def1252668913628243c4d363bee1ef::form.label", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Flabel.blade.php&line=1", "ajax": false, "filename": "label.blade.php", "line": "?"}, "render_count": 5, "name_original": "8def1252668913628243c4d363bee1ef::form.label"}, {"name": "5x 8def1252668913628243c4d363bee1ef::form.error", "param_count": null, "params": [], "start": **********.916023, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/form/error.blade.php8def1252668913628243c4d363bee1ef::form.error", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": "?"}, "render_count": 5, "name_original": "8def1252668913628243c4d363bee1ef::form.error"}, {"name": "1x __components::d2cfde89f704c31422aff2fae16ddb81", "param_count": null, "params": [], "start": **********.918137, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/d2cfde89f704c31422aff2fae16ddb81.blade.php__components::d2cfde89f704c31422aff2fae16ddb81", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fd2cfde89f704c31422aff2fae16ddb81.blade.php&line=1", "ajax": false, "filename": "d2cfde89f704c31422aff2fae16ddb81.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::d2cfde89f704c31422aff2fae16ddb81"}, {"name": "1x core/base::layouts.partials.theme-toggle", "param_count": null, "params": [], "start": **********.91885, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/layouts/partials/theme-toggle.blade.phpcore/base::layouts.partials.theme-toggle", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Ftheme-toggle.blade.php&line=1", "ajax": false, "filename": "theme-toggle.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.theme-toggle"}, {"name": "1x __components::a5645d2a1f3c74251fc89224c575fed8", "param_count": null, "params": [], "start": **********.92015, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/a5645d2a1f3c74251fc89224c575fed8.blade.php__components::a5645d2a1f3c74251fc89224c575fed8", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fa5645d2a1f3c74251fc89224c575fed8.blade.php&line=1", "ajax": false, "filename": "a5645d2a1f3c74251fc89224c575fed8.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::a5645d2a1f3c74251fc89224c575fed8"}, {"name": "1x core/base::notification.nav-item", "param_count": null, "params": [], "start": **********.921655, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/notification/nav-item.blade.phpcore/base::notification.nav-item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fnotification%2Fnav-item.blade.php&line=1", "ajax": false, "filename": "nav-item.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::notification.nav-item"}, {"name": "1x __components::639d159f54869d7a8362974885dec505", "param_count": null, "params": [], "start": **********.922581, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/639d159f54869d7a8362974885dec505.blade.php__components::639d159f54869d7a8362974885dec505", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F639d159f54869d7a8362974885dec505.blade.php&line=1", "ajax": false, "filename": "639d159f54869d7a8362974885dec505.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::639d159f54869d7a8362974885dec505"}, {"name": "1x plugins/contact::partials.notification", "param_count": null, "params": [], "start": **********.925268, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/contact/resources/views/partials/notification.blade.phpplugins/contact::partials.notification", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fcontact%2Fresources%2Fviews%2Fpartials%2Fnotification.blade.php&line=1", "ajax": false, "filename": "notification.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/contact::partials.notification"}, {"name": "3x __components::cf41524c2db4e8ac4f30aba28550db55", "param_count": null, "params": [], "start": **********.926447, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/cf41524c2db4e8ac4f30aba28550db55.blade.php__components::cf41524c2db4e8ac4f30aba28550db55", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fcf41524c2db4e8ac4f30aba28550db55.blade.php&line=1", "ajax": false, "filename": "cf41524c2db4e8ac4f30aba28550db55.blade.php", "line": "?"}, "render_count": 3, "name_original": "__components::cf41524c2db4e8ac4f30aba28550db55"}, {"name": "1x 8def1252668913628243c4d363bee1ef::card.title", "param_count": null, "params": [], "start": **********.927575, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/card/title.blade.php8def1252668913628243c4d363bee1ef::card.title", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Ftitle.blade.php&line=1", "ajax": false, "filename": "title.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::card.title"}, {"name": "1x 8def1252668913628243c4d363bee1ef::card.actions", "param_count": null, "params": [], "start": **********.928224, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/card/actions.blade.php8def1252668913628243c4d363bee1ef::card.actions", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Factions.blade.php&line=1", "ajax": false, "filename": "actions.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::card.actions"}, {"name": "1x plugins/ecommerce::orders.notification", "param_count": null, "params": [], "start": **********.960511, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/ecommerce/resources/views/orders/notification.blade.phpplugins/ecommerce::orders.notification", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Forders%2Fnotification.blade.php&line=1", "ajax": false, "filename": "notification.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/ecommerce::orders.notification"}, {"name": "1x __components::8394ebb1c2841e3a1166cd3fb0a6e03f", "param_count": null, "params": [], "start": **********.96187, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/8394ebb1c2841e3a1166cd3fb0a6e03f.blade.php__components::8394ebb1c2841e3a1166cd3fb0a6e03f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F8394ebb1c2841e3a1166cd3fb0a6e03f.blade.php&line=1", "ajax": false, "filename": "8394ebb1c2841e3a1166cd3fb0a6e03f.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::8394ebb1c2841e3a1166cd3fb0a6e03f"}, {"name": "1x core/base::layouts.partials.user-menu", "param_count": null, "params": [], "start": **********.979774, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/layouts/partials/user-menu.blade.phpcore/base::layouts.partials.user-menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fuser-menu.blade.php&line=1", "ajax": false, "filename": "user-menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.user-menu"}, {"name": "2x __components::d059faaba602d6895d68258ab3c890a6", "param_count": null, "params": [], "start": **********.984818, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/d059faaba602d6895d68258ab3c890a6.blade.php__components::d059faaba602d6895d68258ab3c890a6", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fd059faaba602d6895d68258ab3c890a6.blade.php&line=1", "ajax": false, "filename": "d059faaba602d6895d68258ab3c890a6.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::d059faaba602d6895d68258ab3c890a6"}, {"name": "2x __components::a3cb4601eb64a80dc01a3c268590a3c8", "param_count": null, "params": [], "start": **********.986517, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/a3cb4601eb64a80dc01a3c268590a3c8.blade.php__components::a3cb4601eb64a80dc01a3c268590a3c8", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fa3cb4601eb64a80dc01a3c268590a3c8.blade.php&line=1", "ajax": false, "filename": "a3cb4601eb64a80dc01a3c268590a3c8.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::a3cb4601eb64a80dc01a3c268590a3c8"}, {"name": "1x core/base::layouts.vertical.partials.aside", "param_count": null, "params": [], "start": **********.987702, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/layouts/vertical/partials/aside.blade.phpcore/base::layouts.vertical.partials.aside", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fvertical%2Fpartials%2Faside.blade.php&line=1", "ajax": false, "filename": "aside.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.vertical.partials.aside"}, {"name": "1x core/base::layouts.vertical.partials.sidebar", "param_count": null, "params": [], "start": **********.992055, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/layouts/vertical/partials/sidebar.blade.phpcore/base::layouts.vertical.partials.sidebar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fvertical%2Fpartials%2Fsidebar.blade.php&line=1", "ajax": false, "filename": "sidebar.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.vertical.partials.sidebar"}, {"name": "1x core/base::layouts.partials.navbar-nav", "param_count": null, "params": [], "start": **********.992418, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/layouts/partials/navbar-nav.blade.phpcore/base::layouts.partials.navbar-nav", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fnavbar-nav.blade.php&line=1", "ajax": false, "filename": "navbar-nav.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.navbar-nav"}, {"name": "21x core/base::layouts.partials.navbar-nav-item", "param_count": null, "params": [], "start": **********.008849, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/layouts/partials/navbar-nav-item.blade.phpcore/base::layouts.partials.navbar-nav-item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fnavbar-nav-item.blade.php&line=1", "ajax": false, "filename": "navbar-nav-item.blade.php", "line": "?"}, "render_count": 21, "name_original": "core/base::layouts.partials.navbar-nav-item"}, {"name": "78x core/base::layouts.partials.navbar-nav-item-link", "param_count": null, "params": [], "start": **********.009639, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/layouts/partials/navbar-nav-item-link.blade.phpcore/base::layouts.partials.navbar-nav-item-link", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fnavbar-nav-item-link.blade.php&line=1", "ajax": false, "filename": "navbar-nav-item-link.blade.php", "line": "?"}, "render_count": 78, "name_original": "core/base::layouts.partials.navbar-nav-item-link"}, {"name": "1x __components::e3b17f7ce9738894b58a8b70b9624457", "param_count": null, "params": [], "start": **********.011226, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/e3b17f7ce9738894b58a8b70b9624457.blade.php__components::e3b17f7ce9738894b58a8b70b9624457", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fe3b17f7ce9738894b58a8b70b9624457.blade.php&line=1", "ajax": false, "filename": "e3b17f7ce9738894b58a8b70b9624457.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::e3b17f7ce9738894b58a8b70b9624457"}, {"name": "1x __components::0c6e6838aa476b78aace81114936689c", "param_count": null, "params": [], "start": **********.014586, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/0c6e6838aa476b78aace81114936689c.blade.php__components::0c6e6838aa476b78aace81114936689c", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F0c6e6838aa476b78aace81114936689c.blade.php&line=1", "ajax": false, "filename": "0c6e6838aa476b78aace81114936689c.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::0c6e6838aa476b78aace81114936689c"}, {"name": "11x core/base::partials.navbar.badge-count", "param_count": null, "params": [], "start": **********.015683, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/partials/navbar/badge-count.blade.phpcore/base::partials.navbar.badge-count", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fpartials%2Fnavbar%2Fbadge-count.blade.php&line=1", "ajax": false, "filename": "badge-count.blade.php", "line": "?"}, "render_count": 11, "name_original": "core/base::partials.navbar.badge-count"}, {"name": "11x 8def1252668913628243c4d363bee1ef::navbar.badge-count", "param_count": null, "params": [], "start": **********.016199, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/navbar/badge-count.blade.php8def1252668913628243c4d363bee1ef::navbar.badge-count", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fnavbar%2Fbadge-count.blade.php&line=1", "ajax": false, "filename": "badge-count.blade.php", "line": "?"}, "render_count": 11, "name_original": "8def1252668913628243c4d363bee1ef::navbar.badge-count"}, {"name": "1x __components::37fae22c8e215ea2e54e69a5e3a007cc", "param_count": null, "params": [], "start": **********.018528, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/37fae22c8e215ea2e54e69a5e3a007cc.blade.php__components::37fae22c8e215ea2e54e69a5e3a007cc", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F37fae22c8e215ea2e54e69a5e3a007cc.blade.php&line=1", "ajax": false, "filename": "37fae22c8e215ea2e54e69a5e3a007cc.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::37fae22c8e215ea2e54e69a5e3a007cc"}, {"name": "1x __components::8916176d99d4ae2024cd36e11e35b821", "param_count": null, "params": [], "start": **********.020366, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/8916176d99d4ae2024cd36e11e35b821.blade.php__components::8916176d99d4ae2024cd36e11e35b821", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F8916176d99d4ae2024cd36e11e35b821.blade.php&line=1", "ajax": false, "filename": "8916176d99d4ae2024cd36e11e35b821.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::8916176d99d4ae2024cd36e11e35b821"}, {"name": "1x __components::bd27433a6607127acdaf6dc541ab2435", "param_count": null, "params": [], "start": **********.022787, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/bd27433a6607127acdaf6dc541ab2435.blade.php__components::bd27433a6607127acdaf6dc541ab2435", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fbd27433a6607127acdaf6dc541ab2435.blade.php&line=1", "ajax": false, "filename": "bd27433a6607127acdaf6dc541ab2435.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::bd27433a6607127acdaf6dc541ab2435"}, {"name": "1x __components::5f4f9ebbae249bdc8b0d599a1ac6ad06", "param_count": null, "params": [], "start": **********.024685, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/5f4f9ebbae249bdc8b0d599a1ac6ad06.blade.php__components::5f4f9ebbae249bdc8b0d599a1ac6ad06", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F5f4f9ebbae249bdc8b0d599a1ac6ad06.blade.php&line=1", "ajax": false, "filename": "5f4f9ebbae249bdc8b0d599a1ac6ad06.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::5f4f9ebbae249bdc8b0d599a1ac6ad06"}, {"name": "1x __components::080b92e00b37bcc97c1cd249894494a2", "param_count": null, "params": [], "start": **********.027692, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/080b92e00b37bcc97c1cd249894494a2.blade.php__components::080b92e00b37bcc97c1cd249894494a2", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F080b92e00b37bcc97c1cd249894494a2.blade.php&line=1", "ajax": false, "filename": "080b92e00b37bcc97c1cd249894494a2.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::080b92e00b37bcc97c1cd249894494a2"}, {"name": "1x __components::6a26943e77184871de1629f41c534094", "param_count": null, "params": [], "start": **********.031347, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/6a26943e77184871de1629f41c534094.blade.php__components::6a26943e77184871de1629f41c534094", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F6a26943e77184871de1629f41c534094.blade.php&line=1", "ajax": false, "filename": "6a26943e77184871de1629f41c534094.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::6a26943e77184871de1629f41c534094"}, {"name": "1x __components::8f29f8012139c7a3eb6593c906e1db38", "param_count": null, "params": [], "start": **********.03377, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/8f29f8012139c7a3eb6593c906e1db38.blade.php__components::8f29f8012139c7a3eb6593c906e1db38", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F8f29f8012139c7a3eb6593c906e1db38.blade.php&line=1", "ajax": false, "filename": "8f29f8012139c7a3eb6593c906e1db38.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::8f29f8012139c7a3eb6593c906e1db38"}, {"name": "1x __components::4553f5b37130b2effba490dbdf5419d2", "param_count": null, "params": [], "start": **********.037359, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/4553f5b37130b2effba490dbdf5419d2.blade.php__components::4553f5b37130b2effba490dbdf5419d2", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F4553f5b37130b2effba490dbdf5419d2.blade.php&line=1", "ajax": false, "filename": "4553f5b37130b2effba490dbdf5419d2.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::4553f5b37130b2effba490dbdf5419d2"}, {"name": "1x __components::59c947fc9b2121a5885d4f4e7b1242d8", "param_count": null, "params": [], "start": **********.039896, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/59c947fc9b2121a5885d4f4e7b1242d8.blade.php__components::59c947fc9b2121a5885d4f4e7b1242d8", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F59c947fc9b2121a5885d4f4e7b1242d8.blade.php&line=1", "ajax": false, "filename": "59c947fc9b2121a5885d4f4e7b1242d8.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::59c947fc9b2121a5885d4f4e7b1242d8"}, {"name": "1x __components::1e9698c460b468bfcaf0f7dbbebf9bf0", "param_count": null, "params": [], "start": **********.042024, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/1e9698c460b468bfcaf0f7dbbebf9bf0.blade.php__components::1e9698c460b468bfcaf0f7dbbebf9bf0", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F1e9698c460b468bfcaf0f7dbbebf9bf0.blade.php&line=1", "ajax": false, "filename": "1e9698c460b468bfcaf0f7dbbebf9bf0.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::1e9698c460b468bfcaf0f7dbbebf9bf0"}, {"name": "2x __components::0d4eb4544a5328bea40b7b01743b8f82", "param_count": null, "params": [], "start": **********.04416, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/0d4eb4544a5328bea40b7b01743b8f82.blade.php__components::0d4eb4544a5328bea40b7b01743b8f82", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F0d4eb4544a5328bea40b7b01743b8f82.blade.php&line=1", "ajax": false, "filename": "0d4eb4544a5328bea40b7b01743b8f82.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::0d4eb4544a5328bea40b7b01743b8f82"}, {"name": "2x __components::5270fef4db64e6c2fedf42ea8ac88f25", "param_count": null, "params": [], "start": **********.047505, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/5270fef4db64e6c2fedf42ea8ac88f25.blade.php__components::5270fef4db64e6c2fedf42ea8ac88f25", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F5270fef4db64e6c2fedf42ea8ac88f25.blade.php&line=1", "ajax": false, "filename": "5270fef4db64e6c2fedf42ea8ac88f25.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::5270fef4db64e6c2fedf42ea8ac88f25"}, {"name": "1x __components::84f17fac377525e2e49f32058361220b", "param_count": null, "params": [], "start": **********.050271, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/84f17fac377525e2e49f32058361220b.blade.php__components::84f17fac377525e2e49f32058361220b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F84f17fac377525e2e49f32058361220b.blade.php&line=1", "ajax": false, "filename": "84f17fac377525e2e49f32058361220b.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::84f17fac377525e2e49f32058361220b"}, {"name": "1x __components::1c8617dee734f51544a3883923ddca6f", "param_count": null, "params": [], "start": **********.054732, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/1c8617dee734f51544a3883923ddca6f.blade.php__components::1c8617dee734f51544a3883923ddca6f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F1c8617dee734f51544a3883923ddca6f.blade.php&line=1", "ajax": false, "filename": "1c8617dee734f51544a3883923ddca6f.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::1c8617dee734f51544a3883923ddca6f"}, {"name": "1x __components::3d3bfe5e8598abeb74083f6c26233cb5", "param_count": null, "params": [], "start": **********.056923, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/3d3bfe5e8598abeb74083f6c26233cb5.blade.php__components::3d3bfe5e8598abeb74083f6c26233cb5", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F3d3bfe5e8598abeb74083f6c26233cb5.blade.php&line=1", "ajax": false, "filename": "3d3bfe5e8598abeb74083f6c26233cb5.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::3d3bfe5e8598abeb74083f6c26233cb5"}, {"name": "1x __components::35fe997a7b87ef55d749630606a50a1b", "param_count": null, "params": [], "start": **********.059423, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/35fe997a7b87ef55d749630606a50a1b.blade.php__components::35fe997a7b87ef55d749630606a50a1b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F35fe997a7b87ef55d749630606a50a1b.blade.php&line=1", "ajax": false, "filename": "35fe997a7b87ef55d749630606a50a1b.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::35fe997a7b87ef55d749630606a50a1b"}, {"name": "1x __components::a4f1583597dec7e67a8ae044f0915dbe", "param_count": null, "params": [], "start": **********.065359, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/a4f1583597dec7e67a8ae044f0915dbe.blade.php__components::a4f1583597dec7e67a8ae044f0915dbe", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fa4f1583597dec7e67a8ae044f0915dbe.blade.php&line=1", "ajax": false, "filename": "a4f1583597dec7e67a8ae044f0915dbe.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::a4f1583597dec7e67a8ae044f0915dbe"}, {"name": "1x __components::c0cbd16b0cc2226ec5536610974ba3c3", "param_count": null, "params": [], "start": **********.067825, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/c0cbd16b0cc2226ec5536610974ba3c3.blade.php__components::c0cbd16b0cc2226ec5536610974ba3c3", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fc0cbd16b0cc2226ec5536610974ba3c3.blade.php&line=1", "ajax": false, "filename": "c0cbd16b0cc2226ec5536610974ba3c3.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::c0cbd16b0cc2226ec5536610974ba3c3"}, {"name": "2x __components::70b8df706e60982a72f15e9e2d486203", "param_count": null, "params": [], "start": **********.070619, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/70b8df706e60982a72f15e9e2d486203.blade.php__components::70b8df706e60982a72f15e9e2d486203", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F70b8df706e60982a72f15e9e2d486203.blade.php&line=1", "ajax": false, "filename": "70b8df706e60982a72f15e9e2d486203.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::70b8df706e60982a72f15e9e2d486203"}, {"name": "1x __components::dc78b90963e9d9963376e0e829411cea", "param_count": null, "params": [], "start": **********.073783, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/dc78b90963e9d9963376e0e829411cea.blade.php__components::dc78b90963e9d9963376e0e829411cea", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fdc78b90963e9d9963376e0e829411cea.blade.php&line=1", "ajax": false, "filename": "dc78b90963e9d9963376e0e829411cea.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::dc78b90963e9d9963376e0e829411cea"}, {"name": "4x __components::7a6e3d0dfcd673b5659893aa4dd54e33", "param_count": null, "params": [], "start": **********.075972, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/7a6e3d0dfcd673b5659893aa4dd54e33.blade.php__components::7a6e3d0dfcd673b5659893aa4dd54e33", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F7a6e3d0dfcd673b5659893aa4dd54e33.blade.php&line=1", "ajax": false, "filename": "7a6e3d0dfcd673b5659893aa4dd54e33.blade.php", "line": "?"}, "render_count": 4, "name_original": "__components::7a6e3d0dfcd673b5659893aa4dd54e33"}, {"name": "1x __components::481a833ebeb573258c941c925aa45f7b", "param_count": null, "params": [], "start": **********.079121, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/481a833ebeb573258c941c925aa45f7b.blade.php__components::481a833ebeb573258c941c925aa45f7b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F481a833ebeb573258c941c925aa45f7b.blade.php&line=1", "ajax": false, "filename": "481a833ebeb573258c941c925aa45f7b.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::481a833ebeb573258c941c925aa45f7b"}, {"name": "1x __components::b42bb0aa5fceca31ad61711414a614f0", "param_count": null, "params": [], "start": **********.081402, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/b42bb0aa5fceca31ad61711414a614f0.blade.php__components::b42bb0aa5fceca31ad61711414a614f0", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fb42bb0aa5fceca31ad61711414a614f0.blade.php&line=1", "ajax": false, "filename": "b42bb0aa5fceca31ad61711414a614f0.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::b42bb0aa5fceca31ad61711414a614f0"}, {"name": "2x __components::1c969038219bd5c599f1ca2d81401cea", "param_count": null, "params": [], "start": **********.08552, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/1c969038219bd5c599f1ca2d81401cea.blade.php__components::1c969038219bd5c599f1ca2d81401cea", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F1c969038219bd5c599f1ca2d81401cea.blade.php&line=1", "ajax": false, "filename": "1c969038219bd5c599f1ca2d81401cea.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::1c969038219bd5c599f1ca2d81401cea"}, {"name": "1x __components::471e83668278198d730a7a3f4a475d45", "param_count": null, "params": [], "start": **********.088319, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/471e83668278198d730a7a3f4a475d45.blade.php__components::471e83668278198d730a7a3f4a475d45", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F471e83668278198d730a7a3f4a475d45.blade.php&line=1", "ajax": false, "filename": "471e83668278198d730a7a3f4a475d45.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::471e83668278198d730a7a3f4a475d45"}, {"name": "1x __components::d7b40194b2b4ba91a975fc9aafe2d3e8", "param_count": null, "params": [], "start": **********.092046, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/d7b40194b2b4ba91a975fc9aafe2d3e8.blade.php__components::d7b40194b2b4ba91a975fc9aafe2d3e8", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fd7b40194b2b4ba91a975fc9aafe2d3e8.blade.php&line=1", "ajax": false, "filename": "d7b40194b2b4ba91a975fc9aafe2d3e8.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::d7b40194b2b4ba91a975fc9aafe2d3e8"}, {"name": "1x __components::acb69140835a74210411469faeab3034", "param_count": null, "params": [], "start": **********.097118, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/acb69140835a74210411469faeab3034.blade.php__components::acb69140835a74210411469faeab3034", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Facb69140835a74210411469faeab3034.blade.php&line=1", "ajax": false, "filename": "acb69140835a74210411469faeab3034.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::acb69140835a74210411469faeab3034"}, {"name": "1x __components::19cd49cd69455e40dc223df6b4eaf954", "param_count": null, "params": [], "start": **********.100251, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/19cd49cd69455e40dc223df6b4eaf954.blade.php__components::19cd49cd69455e40dc223df6b4eaf954", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F19cd49cd69455e40dc223df6b4eaf954.blade.php&line=1", "ajax": false, "filename": "19cd49cd69455e40dc223df6b4eaf954.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::19cd49cd69455e40dc223df6b4eaf954"}, {"name": "1x __components::2979b72aeeca0047ecdecc3ad66e7e16", "param_count": null, "params": [], "start": **********.102701, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/2979b72aeeca0047ecdecc3ad66e7e16.blade.php__components::2979b72aeeca0047ecdecc3ad66e7e16", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F2979b72aeeca0047ecdecc3ad66e7e16.blade.php&line=1", "ajax": false, "filename": "2979b72aeeca0047ecdecc3ad66e7e16.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::2979b72aeeca0047ecdecc3ad66e7e16"}, {"name": "1x __components::3c40febd70fcdc245d99ae7cd02cface", "param_count": null, "params": [], "start": **********.105143, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/3c40febd70fcdc245d99ae7cd02cface.blade.php__components::3c40febd70fcdc245d99ae7cd02cface", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F3c40febd70fcdc245d99ae7cd02cface.blade.php&line=1", "ajax": false, "filename": "3c40febd70fcdc245d99ae7cd02cface.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::3c40febd70fcdc245d99ae7cd02cface"}, {"name": "2x __components::90ccac5c8bbb25741ef262bfd81c7551", "param_count": null, "params": [], "start": **********.107169, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/90ccac5c8bbb25741ef262bfd81c7551.blade.php__components::90ccac5c8bbb25741ef262bfd81c7551", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F90ccac5c8bbb25741ef262bfd81c7551.blade.php&line=1", "ajax": false, "filename": "90ccac5c8bbb25741ef262bfd81c7551.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::90ccac5c8bbb25741ef262bfd81c7551"}, {"name": "1x __components::d7ced212b797c29086a7922a858f3070", "param_count": null, "params": [], "start": **********.113963, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/d7ced212b797c29086a7922a858f3070.blade.php__components::d7ced212b797c29086a7922a858f3070", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fd7ced212b797c29086a7922a858f3070.blade.php&line=1", "ajax": false, "filename": "d7ced212b797c29086a7922a858f3070.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::d7ced212b797c29086a7922a858f3070"}, {"name": "2x __components::9d41a7757b46012fb4a0d6634d04a1e0", "param_count": null, "params": [], "start": **********.116874, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/9d41a7757b46012fb4a0d6634d04a1e0.blade.php__components::9d41a7757b46012fb4a0d6634d04a1e0", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F9d41a7757b46012fb4a0d6634d04a1e0.blade.php&line=1", "ajax": false, "filename": "9d41a7757b46012fb4a0d6634d04a1e0.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::9d41a7757b46012fb4a0d6634d04a1e0"}, {"name": "3x __components::86b7e33bd2198279086ebb1f21c0e2cc", "param_count": null, "params": [], "start": **********.120716, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/86b7e33bd2198279086ebb1f21c0e2cc.blade.php__components::86b7e33bd2198279086ebb1f21c0e2cc", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F86b7e33bd2198279086ebb1f21c0e2cc.blade.php&line=1", "ajax": false, "filename": "86b7e33bd2198279086ebb1f21c0e2cc.blade.php", "line": "?"}, "render_count": 3, "name_original": "__components::86b7e33bd2198279086ebb1f21c0e2cc"}, {"name": "1x __components::8c52d9b1ef0685ec10fdc3e877751e02", "param_count": null, "params": [], "start": **********.123166, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/8c52d9b1ef0685ec10fdc3e877751e02.blade.php__components::8c52d9b1ef0685ec10fdc3e877751e02", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F8c52d9b1ef0685ec10fdc3e877751e02.blade.php&line=1", "ajax": false, "filename": "8c52d9b1ef0685ec10fdc3e877751e02.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::8c52d9b1ef0685ec10fdc3e877751e02"}, {"name": "1x __components::42668be6e8e5266862c6994eaa88bb55", "param_count": null, "params": [], "start": **********.126236, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/42668be6e8e5266862c6994eaa88bb55.blade.php__components::42668be6e8e5266862c6994eaa88bb55", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F42668be6e8e5266862c6994eaa88bb55.blade.php&line=1", "ajax": false, "filename": "42668be6e8e5266862c6994eaa88bb55.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::42668be6e8e5266862c6994eaa88bb55"}, {"name": "1x __components::311d8c591d63d3cbd12dceb2fb1ac1c1", "param_count": null, "params": [], "start": **********.130408, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/311d8c591d63d3cbd12dceb2fb1ac1c1.blade.php__components::311d8c591d63d3cbd12dceb2fb1ac1c1", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F311d8c591d63d3cbd12dceb2fb1ac1c1.blade.php&line=1", "ajax": false, "filename": "311d8c591d63d3cbd12dceb2fb1ac1c1.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::311d8c591d63d3cbd12dceb2fb1ac1c1"}, {"name": "1x __components::613233f0072612a02c74dd1699c0b74c", "param_count": null, "params": [], "start": **********.137334, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/613233f0072612a02c74dd1699c0b74c.blade.php__components::613233f0072612a02c74dd1699c0b74c", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F613233f0072612a02c74dd1699c0b74c.blade.php&line=1", "ajax": false, "filename": "613233f0072612a02c74dd1699c0b74c.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::613233f0072612a02c74dd1699c0b74c"}, {"name": "1x __components::16225ede2ef5cc17292fd2eb9026fc80", "param_count": null, "params": [], "start": **********.139298, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/16225ede2ef5cc17292fd2eb9026fc80.blade.php__components::16225ede2ef5cc17292fd2eb9026fc80", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F16225ede2ef5cc17292fd2eb9026fc80.blade.php&line=1", "ajax": false, "filename": "16225ede2ef5cc17292fd2eb9026fc80.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::16225ede2ef5cc17292fd2eb9026fc80"}, {"name": "1x __components::46010cb1cb88bb5ead5d94603a4a3d16", "param_count": null, "params": [], "start": **********.141753, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/46010cb1cb88bb5ead5d94603a4a3d16.blade.php__components::46010cb1cb88bb5ead5d94603a4a3d16", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F46010cb1cb88bb5ead5d94603a4a3d16.blade.php&line=1", "ajax": false, "filename": "46010cb1cb88bb5ead5d94603a4a3d16.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::46010cb1cb88bb5ead5d94603a4a3d16"}, {"name": "1x __components::34e3d89351b1208b7f313125eec52879", "param_count": null, "params": [], "start": **********.14535, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/34e3d89351b1208b7f313125eec52879.blade.php__components::34e3d89351b1208b7f313125eec52879", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F34e3d89351b1208b7f313125eec52879.blade.php&line=1", "ajax": false, "filename": "34e3d89351b1208b7f313125eec52879.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::34e3d89351b1208b7f313125eec52879"}, {"name": "1x __components::67034900569133b2c83b32da3dd4f5e5", "param_count": null, "params": [], "start": **********.147022, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/67034900569133b2c83b32da3dd4f5e5.blade.php__components::67034900569133b2c83b32da3dd4f5e5", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F67034900569133b2c83b32da3dd4f5e5.blade.php&line=1", "ajax": false, "filename": "67034900569133b2c83b32da3dd4f5e5.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::67034900569133b2c83b32da3dd4f5e5"}, {"name": "1x __components::0d623715926c24f9fbc8a4b72c106d5d", "param_count": null, "params": [], "start": **********.155449, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/0d623715926c24f9fbc8a4b72c106d5d.blade.php__components::0d623715926c24f9fbc8a4b72c106d5d", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F0d623715926c24f9fbc8a4b72c106d5d.blade.php&line=1", "ajax": false, "filename": "0d623715926c24f9fbc8a4b72c106d5d.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::0d623715926c24f9fbc8a4b72c106d5d"}, {"name": "1x __components::6c50fc55276d93f8ed03f5c85273b6cc", "param_count": null, "params": [], "start": **********.158011, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/6c50fc55276d93f8ed03f5c85273b6cc.blade.php__components::6c50fc55276d93f8ed03f5c85273b6cc", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F6c50fc55276d93f8ed03f5c85273b6cc.blade.php&line=1", "ajax": false, "filename": "6c50fc55276d93f8ed03f5c85273b6cc.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::6c50fc55276d93f8ed03f5c85273b6cc"}, {"name": "1x __components::084d2b43c9ab4b881d9b34a15580aa2d", "param_count": null, "params": [], "start": **********.160325, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/084d2b43c9ab4b881d9b34a15580aa2d.blade.php__components::084d2b43c9ab4b881d9b34a15580aa2d", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F084d2b43c9ab4b881d9b34a15580aa2d.blade.php&line=1", "ajax": false, "filename": "084d2b43c9ab4b881d9b34a15580aa2d.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::084d2b43c9ab4b881d9b34a15580aa2d"}, {"name": "2x __components::b33d20952e90e5c4a596ff58ad010448", "param_count": null, "params": [], "start": **********.163315, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/b33d20952e90e5c4a596ff58ad010448.blade.php__components::b33d20952e90e5c4a596ff58ad010448", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fb33d20952e90e5c4a596ff58ad010448.blade.php&line=1", "ajax": false, "filename": "b33d20952e90e5c4a596ff58ad010448.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::b33d20952e90e5c4a596ff58ad010448"}, {"name": "1x __components::7db8cad89359963c1e9aa8fcc6c89817", "param_count": null, "params": [], "start": **********.165867, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/7db8cad89359963c1e9aa8fcc6c89817.blade.php__components::7db8cad89359963c1e9aa8fcc6c89817", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F7db8cad89359963c1e9aa8fcc6c89817.blade.php&line=1", "ajax": false, "filename": "7db8cad89359963c1e9aa8fcc6c89817.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::7db8cad89359963c1e9aa8fcc6c89817"}, {"name": "1x __components::325be2a8c3ca3843efa76c03adaee1dc", "param_count": null, "params": [], "start": **********.171879, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/325be2a8c3ca3843efa76c03adaee1dc.blade.php__components::325be2a8c3ca3843efa76c03adaee1dc", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F325be2a8c3ca3843efa76c03adaee1dc.blade.php&line=1", "ajax": false, "filename": "325be2a8c3ca3843efa76c03adaee1dc.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::325be2a8c3ca3843efa76c03adaee1dc"}, {"name": "1x __components::e12a669ffa0346a27198bed32e63b7ba", "param_count": null, "params": [], "start": **********.173771, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/e12a669ffa0346a27198bed32e63b7ba.blade.php__components::e12a669ffa0346a27198bed32e63b7ba", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fe12a669ffa0346a27198bed32e63b7ba.blade.php&line=1", "ajax": false, "filename": "e12a669ffa0346a27198bed32e63b7ba.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::e12a669ffa0346a27198bed32e63b7ba"}, {"name": "1x __components::9d0b20db301db9a47503a93a879bb206", "param_count": null, "params": [], "start": **********.175596, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/9d0b20db301db9a47503a93a879bb206.blade.php__components::9d0b20db301db9a47503a93a879bb206", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F9d0b20db301db9a47503a93a879bb206.blade.php&line=1", "ajax": false, "filename": "9d0b20db301db9a47503a93a879bb206.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::9d0b20db301db9a47503a93a879bb206"}, {"name": "1x __components::b985af7bcdacbeac70eaf3979ad19f5a", "param_count": null, "params": [], "start": **********.177587, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/b985af7bcdacbeac70eaf3979ad19f5a.blade.php__components::b985af7bcdacbeac70eaf3979ad19f5a", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fb985af7bcdacbeac70eaf3979ad19f5a.blade.php&line=1", "ajax": false, "filename": "b985af7bcdacbeac70eaf3979ad19f5a.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::b985af7bcdacbeac70eaf3979ad19f5a"}, {"name": "1x __components::a0bb1d43b71cff86abe626fd376492e9", "param_count": null, "params": [], "start": **********.180522, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/a0bb1d43b71cff86abe626fd376492e9.blade.php__components::a0bb1d43b71cff86abe626fd376492e9", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fa0bb1d43b71cff86abe626fd376492e9.blade.php&line=1", "ajax": false, "filename": "a0bb1d43b71cff86abe626fd376492e9.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::a0bb1d43b71cff86abe626fd376492e9"}, {"name": "1x __components::e25f2b305e6de46c04f91fa1ce50f68f", "param_count": null, "params": [], "start": **********.182666, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/e25f2b305e6de46c04f91fa1ce50f68f.blade.php__components::e25f2b305e6de46c04f91fa1ce50f68f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fe25f2b305e6de46c04f91fa1ce50f68f.blade.php&line=1", "ajax": false, "filename": "e25f2b305e6de46c04f91fa1ce50f68f.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::e25f2b305e6de46c04f91fa1ce50f68f"}, {"name": "1x __components::69152f707ea1358f8997b77a28e38a6f", "param_count": null, "params": [], "start": **********.184997, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/69152f707ea1358f8997b77a28e38a6f.blade.php__components::69152f707ea1358f8997b77a28e38a6f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F69152f707ea1358f8997b77a28e38a6f.blade.php&line=1", "ajax": false, "filename": "69152f707ea1358f8997b77a28e38a6f.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::69152f707ea1358f8997b77a28e38a6f"}, {"name": "1x __components::b85eba35d3b7929c2988678b725baebf", "param_count": null, "params": [], "start": **********.18725, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/b85eba35d3b7929c2988678b725baebf.blade.php__components::b85eba35d3b7929c2988678b725baebf", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fb85eba35d3b7929c2988678b725baebf.blade.php&line=1", "ajax": false, "filename": "b85eba35d3b7929c2988678b725baebf.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::b85eba35d3b7929c2988678b725baebf"}, {"name": "1x __components::1f3d3b83c612f68036b4d79d53ae851e", "param_count": null, "params": [], "start": **********.189273, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/1f3d3b83c612f68036b4d79d53ae851e.blade.php__components::1f3d3b83c612f68036b4d79d53ae851e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F1f3d3b83c612f68036b4d79d53ae851e.blade.php&line=1", "ajax": false, "filename": "1f3d3b83c612f68036b4d79d53ae851e.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::1f3d3b83c612f68036b4d79d53ae851e"}, {"name": "1x __components::f143d1296cea16d82e2c87956e445593", "param_count": null, "params": [], "start": **********.192747, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/f143d1296cea16d82e2c87956e445593.blade.php__components::f143d1296cea16d82e2c87956e445593", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Ff143d1296cea16d82e2c87956e445593.blade.php&line=1", "ajax": false, "filename": "f143d1296cea16d82e2c87956e445593.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::f143d1296cea16d82e2c87956e445593"}, {"name": "1x __components::8cae4e5056b67c6778a54389a62ac7a0", "param_count": null, "params": [], "start": **********.195196, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/8cae4e5056b67c6778a54389a62ac7a0.blade.php__components::8cae4e5056b67c6778a54389a62ac7a0", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F8cae4e5056b67c6778a54389a62ac7a0.blade.php&line=1", "ajax": false, "filename": "8cae4e5056b67c6778a54389a62ac7a0.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::8cae4e5056b67c6778a54389a62ac7a0"}, {"name": "1x __components::d5e509b6eb9084ec382ec05ccab41d1a", "param_count": null, "params": [], "start": **********.197212, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/d5e509b6eb9084ec382ec05ccab41d1a.blade.php__components::d5e509b6eb9084ec382ec05ccab41d1a", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fd5e509b6eb9084ec382ec05ccab41d1a.blade.php&line=1", "ajax": false, "filename": "d5e509b6eb9084ec382ec05ccab41d1a.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::d5e509b6eb9084ec382ec05ccab41d1a"}, {"name": "1x __components::e81a46563ed9378aa4d9a4fcb55e743e", "param_count": null, "params": [], "start": **********.199713, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/e81a46563ed9378aa4d9a4fcb55e743e.blade.php__components::e81a46563ed9378aa4d9a4fcb55e743e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fe81a46563ed9378aa4d9a4fcb55e743e.blade.php&line=1", "ajax": false, "filename": "e81a46563ed9378aa4d9a4fcb55e743e.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::e81a46563ed9378aa4d9a4fcb55e743e"}, {"name": "1x __components::da3c3de008e5793cbbdad005d78f49b1", "param_count": null, "params": [], "start": **********.206438, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/da3c3de008e5793cbbdad005d78f49b1.blade.php__components::da3c3de008e5793cbbdad005d78f49b1", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fda3c3de008e5793cbbdad005d78f49b1.blade.php&line=1", "ajax": false, "filename": "da3c3de008e5793cbbdad005d78f49b1.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::da3c3de008e5793cbbdad005d78f49b1"}, {"name": "1x core/base::layouts.partials.page-header", "param_count": null, "params": [], "start": **********.207466, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/layouts/partials/page-header.blade.phpcore/base::layouts.partials.page-header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fpage-header.blade.php&line=1", "ajax": false, "filename": "page-header.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.page-header"}, {"name": "1x core/base::breadcrumb", "param_count": null, "params": [], "start": **********.208042, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/breadcrumb.blade.phpcore/base::breadcrumb", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fbreadcrumb.blade.php&line=1", "ajax": false, "filename": "breadcrumb.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::breadcrumb"}, {"name": "1x core/base::layouts.partials.footer", "param_count": null, "params": [], "start": **********.208998, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/layouts/partials/footer.blade.phpcore/base::layouts.partials.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.footer"}, {"name": "1x core/base::partials.copyright", "param_count": null, "params": [], "start": **********.209442, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/partials/copyright.blade.phpcore/base::partials.copyright", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fpartials%2Fcopyright.blade.php&line=1", "ajax": false, "filename": "copyright.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::partials.copyright"}, {"name": "1x core/base::layouts.vertical.partials.after-content", "param_count": null, "params": [], "start": **********.210455, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/layouts/vertical/partials/after-content.blade.phpcore/base::layouts.vertical.partials.after-content", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fvertical%2Fpartials%2Fafter-content.blade.php&line=1", "ajax": false, "filename": "after-content.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.vertical.partials.after-content"}, {"name": "1x core/base::system.license-invalid", "param_count": null, "params": [], "start": **********.211346, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/system/license-invalid.blade.phpcore/base::system.license-invalid", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fsystem%2Flicense-invalid.blade.php&line=1", "ajax": false, "filename": "license-invalid.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::system.license-invalid"}, {"name": "2x 8def1252668913628243c4d363bee1ef::alert", "param_count": null, "params": [], "start": **********.212478, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/alert.blade.php8def1252668913628243c4d363bee1ef::alert", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Falert.blade.php&line=1", "ajax": false, "filename": "alert.blade.php", "line": "?"}, "render_count": 2, "name_original": "8def1252668913628243c4d363bee1ef::alert"}, {"name": "1x core/base::system.partials.license-activation-modal", "param_count": null, "params": [], "start": **********.213188, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/system/partials/license-activation-modal.blade.phpcore/base::system.partials.license-activation-modal", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fsystem%2Fpartials%2Flicense-activation-modal.blade.php&line=1", "ajax": false, "filename": "license-activation-modal.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::system.partials.license-activation-modal"}, {"name": "1x 8def1252668913628243c4d363bee1ef::license.form", "param_count": null, "params": [], "start": **********.21389, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/license/form.blade.php8def1252668913628243c4d363bee1ef::license.form", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Flicense%2Fform.blade.php&line=1", "ajax": false, "filename": "form.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::license.form"}, {"name": "1x __components::27ec08f706fece52ef1cc0ec5563cef9", "param_count": null, "params": [], "start": **********.216066, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/27ec08f706fece52ef1cc0ec5563cef9.blade.php__components::27ec08f706fece52ef1cc0ec5563cef9", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F27ec08f706fece52ef1cc0ec5563cef9.blade.php&line=1", "ajax": false, "filename": "27ec08f706fece52ef1cc0ec5563cef9.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::27ec08f706fece52ef1cc0ec5563cef9"}, {"name": "2x 8def1252668913628243c4d363bee1ef::form.helper-text", "param_count": null, "params": [], "start": **********.217899, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/form/helper-text.blade.php8def1252668913628243c4d363bee1ef::form.helper-text", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fhelper-text.blade.php&line=1", "ajax": false, "filename": "helper-text.blade.php", "line": "?"}, "render_count": 2, "name_original": "8def1252668913628243c4d363bee1ef::form.helper-text"}, {"name": "1x 8def1252668913628243c4d363bee1ef::form.on-off.checkbox", "param_count": null, "params": [], "start": **********.221221, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/form/on-off/checkbox.blade.php8def1252668913628243c4d363bee1ef::form.on-off.checkbox", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fon-off%2Fcheckbox.blade.php&line=1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::form.on-off.checkbox"}, {"name": "1x core/base::components.form.checkbox", "param_count": null, "params": [], "start": **********.22172, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/form/checkbox.blade.phpcore/base::components.form.checkbox", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php&line=1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::components.form.checkbox"}, {"name": "1x ********************************::form-group", "param_count": null, "params": [], "start": **********.223458, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/setting/resources/views/components/form-group.blade.php********************************::form-group", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fsetting%2Fresources%2Fviews%2Fcomponents%2Fform-group.blade.php&line=1", "ajax": false, "filename": "form-group.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::form-group"}, {"name": "1x core/base::global-search.form", "param_count": null, "params": [], "start": **********.225268, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/global-search/form.blade.phpcore/base::global-search.form", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fglobal-search%2Fform.blade.php&line=1", "ajax": false, "filename": "form.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::global-search.form"}, {"name": "1x __components::53362b6227831afe8e4d7d3436ab607f", "param_count": null, "params": [], "start": **********.229588, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/53362b6227831afe8e4d7d3436ab607f.blade.php__components::53362b6227831afe8e4d7d3436ab607f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F53362b6227831afe8e4d7d3436ab607f.blade.php&line=1", "ajax": false, "filename": "53362b6227831afe8e4d7d3436ab607f.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::53362b6227831afe8e4d7d3436ab607f"}, {"name": "1x __components::9e76aef074ac8ea84c711b8437720a22", "param_count": null, "params": [], "start": **********.230924, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/9e76aef074ac8ea84c711b8437720a22.blade.php__components::9e76aef074ac8ea84c711b8437720a22", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F9e76aef074ac8ea84c711b8437720a22.blade.php&line=1", "ajax": false, "filename": "9e76aef074ac8ea84c711b8437720a22.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::9e76aef074ac8ea84c711b8437720a22"}, {"name": "1x __components::04edbddbda254d131a3439b11c880f12", "param_count": null, "params": [], "start": **********.232121, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/04edbddbda254d131a3439b11c880f12.blade.php__components::04edbddbda254d131a3439b11c880f12", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F04edbddbda254d131a3439b11c880f12.blade.php&line=1", "ajax": false, "filename": "04edbddbda254d131a3439b11c880f12.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::04edbddbda254d131a3439b11c880f12"}, {"name": "1x 8def1252668913628243c4d363bee1ef::custom-template", "param_count": null, "params": [], "start": **********.233639, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/custom-template.blade.php8def1252668913628243c4d363bee1ef::custom-template", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcustom-template.blade.php&line=1", "ajax": false, "filename": "custom-template.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::custom-template"}, {"name": "1x core/media::partials.media", "param_count": null, "params": [], "start": **********.23437, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/media/resources/views/partials/media.blade.phpcore/media::partials.media", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fmedia%2Fresources%2Fviews%2Fpartials%2Fmedia.blade.php&line=1", "ajax": false, "filename": "media.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/media::partials.media"}, {"name": "1x 8def1252668913628243c4d363bee1ef::loading", "param_count": null, "params": [], "start": **********.235499, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/loading.blade.php8def1252668913628243c4d363bee1ef::loading", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Floading.blade.php&line=1", "ajax": false, "filename": "loading.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::loading"}, {"name": "1x 8def1252668913628243c4d363bee1ef::form.checkbox", "param_count": null, "params": [], "start": **********.237844, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/form/checkbox.blade.php8def1252668913628243c4d363bee1ef::form.checkbox", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php&line=1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::form.checkbox"}, {"name": "1x core/media::config", "param_count": null, "params": [], "start": **********.241395, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/media/resources/views/config.blade.phpcore/media::config", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fmedia%2Fresources%2Fviews%2Fconfig.blade.php&line=1", "ajax": false, "filename": "config.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/media::config"}, {"name": "1x 8def1252668913628243c4d363bee1ef::debug-badge", "param_count": null, "params": [], "start": **********.469114, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/debug-badge.blade.php8def1252668913628243c4d363bee1ef::debug-badge", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fdebug-badge.blade.php&line=1", "ajax": false, "filename": "debug-badge.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::debug-badge"}, {"name": "1x __components::93ebde3601860db875cfe9a96164bda6", "param_count": null, "params": [], "start": **********.472455, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/93ebde3601860db875cfe9a96164bda6.blade.php__components::93ebde3601860db875cfe9a96164bda6", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F93ebde3601860db875cfe9a96164bda6.blade.php&line=1", "ajax": false, "filename": "93ebde3601860db875cfe9a96164bda6.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::93ebde3601860db875cfe9a96164bda6"}, {"name": "1x __components::16c15d36d71c18d83a7e9e7e1b68a92b", "param_count": null, "params": [], "start": **********.475924, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/16c15d36d71c18d83a7e9e7e1b68a92b.blade.php__components::16c15d36d71c18d83a7e9e7e1b68a92b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F16c15d36d71c18d83a7e9e7e1b68a92b.blade.php&line=1", "ajax": false, "filename": "16c15d36d71c18d83a7e9e7e1b68a92b.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::16c15d36d71c18d83a7e9e7e1b68a92b"}, {"name": "1x 8def1252668913628243c4d363bee1ef::layouts.base", "param_count": null, "params": [], "start": **********.477652, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/layouts/base.blade.php8def1252668913628243c4d363bee1ef::layouts.base", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fbase.blade.php&line=1", "ajax": false, "filename": "base.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::layouts.base"}, {"name": "1x core/base::components.layouts.header", "param_count": null, "params": [], "start": **********.478787, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/layouts/header.blade.phpcore/base::components.layouts.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::components.layouts.header"}, {"name": "1x assets::header", "param_count": null, "params": [], "start": **********.482009, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\vendor\\botble\\assets\\src\\Providers/../../resources/views/header.blade.phpassets::header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fassets%2Fresources%2Fviews%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "assets::header"}, {"name": "1x core/base::elements.common", "param_count": null, "params": [], "start": **********.484062, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/elements/common.blade.phpcore/base::elements.common", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Felements%2Fcommon.blade.php&line=1", "ajax": false, "filename": "common.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::elements.common"}, {"name": "1x assets::footer", "param_count": null, "params": [], "start": **********.485789, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\vendor\\botble\\assets\\src\\Providers/../../resources/views/footer.blade.phpassets::footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fassets%2Fresources%2Fviews%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "assets::footer"}, {"name": "1x core/base::notification.notification", "param_count": null, "params": [], "start": **********.486755, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/notification/notification.blade.phpcore/base::notification.notification", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fnotification%2Fnotification.blade.php&line=1", "ajax": false, "filename": "notification.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::notification.notification"}]}, "queries": {"count": 17, "nb_statements": 17, "nb_visible_statements": 17, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00968, "accumulated_duration_str": "9.68ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}], "start": **********.679333, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 0, "width_percent": 4.959}, {"sql": "select exists(select * from `activations` where `activations`.`user_id` = 1 and `activations`.`user_id` is not null and `completed` = 1) as `exists`", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/botble/platform/acl/src/Models/User.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\acl\\src\\Models\\User.php", "line": 122}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\acl\\src\\Http\\Middleware\\Authenticate.php", "line": 22}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 87}], "start": **********.684576, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "User.php:122", "source": {"index": 14, "namespace": null, "name": "vendor/botble/platform/acl/src/Models/User.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\acl\\src\\Models\\User.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Facl%2Fsrc%2FModels%2FUser.php&line=122", "ajax": false, "filename": "User.php", "line": "122"}, "connection": "martfury", "explain": null, "start_percent": 4.959, "width_percent": 4.029}, {"sql": "select * from `user_meta` where `user_meta`.`user_id` = 1 and `user_meta`.`user_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/acl/src/Concerns/HasPreferences.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\acl\\src\\Concerns\\HasPreferences.php", "line": 62}, {"index": 18, "namespace": null, "name": "vendor/botble/platform/acl/src/Concerns/HasPreferences.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\acl\\src\\Concerns\\HasPreferences.php", "line": 32}, {"index": 19, "namespace": null, "name": "vendor/botble/platform/base/src/Supports/AdminAppearance.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Supports\\AdminAppearance.php", "line": 99}, {"index": 20, "namespace": null, "name": "vendor/botble/platform/base/src/Supports/AdminAppearance.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Supports\\AdminAppearance.php", "line": 39}], "start": **********.6915069, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 8.988, "width_percent": 5.682}, {"sql": "select `lang_locale`, `lang_code`, `lang_name`, `lang_flag`, `lang_is_rtl` from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\language\\src\\Providers\\HookServiceProvider.php", "line": 118}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "vendor/botble/platform/base/src/Http/Middleware/AdminLocaleMiddleware.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php", "line": 28}], "start": **********.69896, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 14.669, "width_percent": 5.062}, {"sql": "select exists(select `id`, `status`, `user_id`, `created_at`, `amount`, `tax_amount`, `shipping_amount`, `payment_id`, `ec_orders`.`store_id` from `ec_orders` where `is_finished` = 1) as `exists`", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/botble/platform/table/src/Abstracts/TableAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\table\\src\\Abstracts\\TableAbstract.php", "line": 940}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Tables/OrderTable.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Tables\\OrderTable.php", "line": 253}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/OrderController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\OrderController.php", "line": 80}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.725362, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "TableAbstract.php:940", "source": {"index": 11, "namespace": null, "name": "vendor/botble/platform/table/src/Abstracts/TableAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\table\\src\\Abstracts\\TableAbstract.php", "line": 940}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Ftable%2Fsrc%2FAbstracts%2FTableAbstract.php&line=940", "ajax": false, "filename": "TableAbstract.php", "line": "940"}, "connection": "martfury", "explain": null, "start_percent": 19.731, "width_percent": 10.021}, {"sql": "select `name`, `id` from `mp_stores`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Tables/OrderTable.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Tables\\OrderTable.php", "line": 244}, {"index": 14, "namespace": null, "name": "vendor/botble/platform/table/src/Abstracts/Concerns/HasFilters.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\table\\src\\Abstracts\\Concerns\\HasFilters.php", "line": 38}, {"index": 15, "namespace": "view", "name": "core/table::base-table", "file": "D:\\laragon\\www\\martfury\\platform/core/table/resources/views/base-table.blade.php", "line": 15}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}], "start": **********.778587, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "OrderTable.php:244", "source": {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Tables/OrderTable.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Tables\\OrderTable.php", "line": 244}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FTables%2FOrderTable.php&line=244", "ajax": false, "filename": "OrderTable.php", "line": "244"}, "connection": "martfury", "explain": null, "start_percent": 29.752, "width_percent": 4.545}, {"sql": "select `name`, `id` from `mp_stores`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Tables/OrderTable.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Tables\\OrderTable.php", "line": 244}, {"index": 14, "namespace": null, "name": "vendor/botble/platform/table/src/Abstracts/Concerns/HasFilters.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\table\\src\\Abstracts\\Concerns\\HasFilters.php", "line": 133}, {"index": 15, "namespace": "view", "name": "core/table::base-table", "file": "D:\\laragon\\www\\martfury\\platform/core/table/resources/views/base-table.blade.php", "line": 57}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}], "start": **********.792415, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "OrderTable.php:244", "source": {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Tables/OrderTable.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Tables\\OrderTable.php", "line": 244}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FTables%2FOrderTable.php&line=244", "ajax": false, "filename": "OrderTable.php", "line": "244"}, "connection": "martfury", "explain": null, "start_percent": 34.298, "width_percent": 4.236}, {"sql": "select `name`, `id` from `mp_stores`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Tables/OrderTable.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Tables\\OrderTable.php", "line": 244}, {"index": 14, "namespace": null, "name": "vendor/botble/platform/table/src/Abstracts/Concerns/HasFilters.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\table\\src\\Abstracts\\Concerns\\HasFilters.php", "line": 49}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/table/src/Abstracts/Concerns/HasFilters.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\table\\src\\Abstracts\\Concerns\\HasFilters.php", "line": 155}, {"index": 16, "namespace": "view", "name": "core/table::base-table", "file": "D:\\laragon\\www\\martfury\\platform/core/table/resources/views/base-table.blade.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.8077781, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "OrderTable.php:244", "source": {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Tables/OrderTable.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Tables\\OrderTable.php", "line": 244}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FTables%2FOrderTable.php&line=244", "ajax": false, "filename": "OrderTable.php", "line": "244"}, "connection": "martfury", "explain": null, "start_percent": 38.533, "width_percent": 4.959}, {"sql": "select `name`, `id` from `mp_stores`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Tables/OrderTable.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Tables\\OrderTable.php", "line": 244}, {"index": 14, "namespace": null, "name": "vendor/botble/platform/table/src/Abstracts/Concerns/HasFilters.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\table\\src\\Abstracts\\Concerns\\HasFilters.php", "line": 38}, {"index": 15, "namespace": "view", "name": "core/table::base-table", "file": "D:\\laragon\\www\\martfury\\platform/core/table/resources/views/base-table.blade.php", "line": 85}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}], "start": **********.83344, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "OrderTable.php:244", "source": {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Tables/OrderTable.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Tables\\OrderTable.php", "line": 244}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FTables%2FOrderTable.php&line=244", "ajax": false, "filename": "OrderTable.php", "line": "244"}, "connection": "martfury", "explain": null, "start_percent": 43.492, "width_percent": 4.545}, {"sql": "select `name`, `id` from `mp_stores`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Tables/OrderTable.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Tables\\OrderTable.php", "line": 244}, {"index": 14, "namespace": null, "name": "vendor/botble/platform/table/src/Abstracts/Concerns/HasFilters.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\table\\src\\Abstracts\\Concerns\\HasFilters.php", "line": 38}, {"index": 15, "namespace": "view", "name": "core/table::base-table", "file": "D:\\laragon\\www\\martfury\\platform/core/table/resources/views/base-table.blade.php", "line": 95}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}], "start": **********.843018, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "OrderTable.php:244", "source": {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Tables/OrderTable.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Tables\\OrderTable.php", "line": 244}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FTables%2FOrderTable.php&line=244", "ajax": false, "filename": "OrderTable.php", "line": "244"}, "connection": "martfury", "explain": null, "start_percent": 48.037, "width_percent": 3.926}, {"sql": "select `name`, `id` from `mp_stores`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Tables/OrderTable.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Tables\\OrderTable.php", "line": 244}, {"index": 14, "namespace": null, "name": "vendor/botble/platform/table/src/Abstracts/Concerns/HasFilters.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\table\\src\\Abstracts\\Concerns\\HasFilters.php", "line": 38}, {"index": 15, "namespace": "view", "name": "core/table::base-table", "file": "D:\\laragon\\www\\martfury\\platform/core/table/resources/views/base-table.blade.php", "line": 136}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}], "start": **********.856713, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "OrderTable.php:244", "source": {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Tables/OrderTable.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Tables\\OrderTable.php", "line": 244}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FTables%2FOrderTable.php&line=244", "ajax": false, "filename": "OrderTable.php", "line": "244"}, "connection": "martfury", "explain": null, "start_percent": 51.963, "width_percent": 3.822}, {"sql": "select `name`, `id` from `mp_stores`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Tables/OrderTable.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Tables\\OrderTable.php", "line": 244}, {"index": 14, "namespace": null, "name": "vendor/botble/platform/table/src/Abstracts/Concerns/HasFilters.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\table\\src\\Abstracts\\Concerns\\HasFilters.php", "line": 38}, {"index": 15, "namespace": "view", "name": "core/table::base-table", "file": "D:\\laragon\\www\\martfury\\platform/core/table/resources/views/base-table.blade.php", "line": 423}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}], "start": **********.87698, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "OrderTable.php:244", "source": {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Tables/OrderTable.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Tables\\OrderTable.php", "line": 244}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FTables%2FOrderTable.php&line=244", "ajax": false, "filename": "OrderTable.php", "line": "244"}, "connection": "martfury", "explain": null, "start_percent": 55.785, "width_percent": 5.062}, {"sql": "select count(*) as aggregate from `ec_orders` where (`status` = 'pending' and `is_finished` = 1)", "type": "query", "params": [], "bindings": ["pending", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 1402}, {"index": 20, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 76}], "start": **********.943889, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "HookServiceProvider.php:1402", "source": {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 1402}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FProviders%2FHookServiceProvider.php&line=1402", "ajax": false, "filename": "HookServiceProvider.php", "line": "1402"}, "connection": "martfury", "explain": null, "start_percent": 60.847, "width_percent": 11.777}, {"sql": "select * from `ec_orders` where (`status` = 'pending' and `is_finished` = 1) order by `created_at` desc limit 10 offset 0", "type": "query", "params": [], "bindings": ["pending", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 1402}, {"index": 21, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}], "start": **********.945911, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 72.624, "width_percent": 9.814}, {"sql": "select * from `ec_order_addresses` where `type` = 'shipping_address' and `ec_order_addresses`.`order_id` in (38, 39, 40, 41, 42, 43, 46, 48, 49, 50)", "type": "query", "params": [], "bindings": ["shipping_address"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 1402}, {"index": 27, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.953791, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 82.438, "width_percent": 9.194}, {"sql": "select * from `ec_customers` where `ec_customers`.`id` in (0, 2, 11)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 1402}, {"index": 27, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.958179, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 91.632, "width_percent": 4.132}, {"sql": "select count(*) as aggregate from `ec_reviews` where `status` = 'pending'", "type": "query", "params": [], "bindings": ["pending"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 1446}, {"index": 20, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 76}], "start": **********.061647, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "HookServiceProvider.php:1446", "source": {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 1446}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FProviders%2FHookServiceProvider.php&line=1446", "ajax": false, "filename": "HookServiceProvider.php", "line": "1446"}, "connection": "martfury", "explain": null, "start_percent": 95.764, "width_percent": 4.236}]}, "models": {"data": {"Botble\\Ecommerce\\Models\\Order": {"retrieved": 10, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FOrder.php&line=1", "ajax": false, "filename": "Order.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\OrderAddress": {"retrieved": 10, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FOrderAddress.php&line=1", "ajax": false, "filename": "OrderAddress.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Customer": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "Botble\\ACL\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Botble\\ACL\\Models\\UserMeta": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Facl%2Fsrc%2FModels%2FUserMeta.php&line=1", "ajax": false, "filename": "UserMeta.php", "line": "?"}}, "Botble\\Language\\Models\\Language": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Flanguage%2Fsrc%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}}, "count": 25, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 25}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://martfury.gc/admin/ecommerce/orders", "action_name": "orders.index", "controller_action": "Botble\\Ecommerce\\Http\\Controllers\\OrderController@index", "uri": "GET admin/ecommerce/orders", "controller": "Botble\\Ecommerce\\Http\\Controllers\\OrderController@index<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FHttp%2FControllers%2FOrderController.php&line=76\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Botble\\Ecommerce\\Http\\Controllers", "prefix": "admin/ecommerce/orders", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FHttp%2FControllers%2FOrderController.php&line=76\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/plugins/ecommerce/src/Http/Controllers/OrderController.php:76-81</a>", "middleware": "web, core, auth", "duration": "1.6s", "peak_memory": "74MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-927583432 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-927583432\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1518967179 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1518967179\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1651307877 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">martfury.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"61 characters\">https://martfury.gc/admin/ecommerce/incomplete-orders/view/45</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3215 characters\">cookie_for_consent=1; botble_footprints_cookie=eyJpdiI6Ilp2Q1lFM2s1MkVpWnI2blVTb2FtZWc9PSIsInZhbHVlIjoiU1N3dm1pVUNyMlJieWRscUU2Rkk3dzFSVmsxSFhpU3Bpc0lSQmRvOEl3M0ZwaW42RkFpdUoxV1NOWW52Q2lOWFh1WUVZUFVpWnBGdHBuc2xVWXV6TmJFRUd6VVljQ25vWWlDaVhBczVpNEpTU0NmbytXczNaTzdDdFNCN3U4anoiLCJtYWMiOiJjZDdlODUyY2JjNzc4MjNmNmEwMTc4YzA3ODFlM2MyNmQ0MzhjMGNkMTA2ZDY3MjYwYjQ0MmMzMjA1NDAzMjhjIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6ImNlT05BQnNiOExZUk94aE9NbHNkYWc9PSIsInZhbHVlIjoiTWVoMVdkQWFROThBdm9VWUV1cWd6TFlLUFR2emErYW5PWWNDUk5ycVBWOURCdzVHT0pRaEpET242bzEzSFh0bWlpT0RZZnoxVUd4U0swOGUyV1lteXU0SDJhdG80U0VhdnVHaFVFVUJ0amhRVFhTOEFMWUpnYmVmamdqQ2NrNERDMW1lbElBeWNYN2NHQTAyZm9PTk5FKzIvT3VjRjU3ZU40bjJVNGI5WmdSTHBoZTZrUnp0RE9MbEg0aytxMUVlNTlJTW9lZnJGVWxDbHI3Q3poR2RDbzVISnB1Y0pCSXRDb0cyYmFCRUVRMkl2RkxUQlpoanhhR2pINW1ZdkZmS091REtuSnRlNjB6ekR2clEweFZlMjh1M0dOanZQdTlpcHdTRlg2YzJxQm92VHRPdG9SekNPSEJVa0VKcjlEZjlJenVONVVwZTJnRjQ4N1JZKzhEd2pnZHJsUkM3eXVaMmhYblhRZmVoQlk0YTNSOFFYUEhqYi9hQzZIWkloa0llWGNaMEFYVFFmVGhnYnZJcnJIUHh0Z2pzREpUb2ptM1BnU25wV3M3eWF2RUl5UVdwU0UzUDdCZkErMXlCREdRSXExTzVVK1kwL0Jmd0laTGZ1VE9sc1BMbG5lVnVLNytLaEs1YjJpTEllK3Qwd3VUbHBEL3JLNlo0VEMwN2UxakRKK2hxUE9QTmI5TDdqeW03WkgwZjVnPT0iLCJtYWMiOiI1ZmIxN2YwMTBlNWZjMTY0MTY0NDMzZDUyZmZjNmQ5ZWZkYjk0NTYxZjg0ZTk4ODAzNTVlYzJhYWJmN2I0NmVhIiwidGFnIjoiIn0%3D; shortcode_cache_suggestion_dismissed=1; widget_cache_suggestion_dismissed=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Imtmd01aTUlkUmlvaEJqSmRGMnJ5aXc9PSIsInZhbHVlIjoiMGVSOUY2VWtsSitnWmpMdXIxbHd1WC9PeGRISXNyNXQwM1dJeXBwUVZweUphTlViRWNWT09zVXpYUVpNWm1nTEJXeDlxQmlZTDUrOUNZdk9POERqSzQ0V0N4NXhGMFlyd1hPa2xKVGZjTjhWSWFQbWpRcmdGT1ViRFEyQ2IrMjUwMXNtOW1hbWM0MU5xR2UwdmkycTUyTlNibmRhdENJeXZnYkVlQlFlSkZYVm9BZk43WXU0RUhtOWt5NFZNVTJlN3BSMDRkaUd2Z0RwNzBqei9RUDZRVVhpWm5idEpKK3dJTWJ2dVg1OVQ0Zz0iLCJtYWMiOiJlZTQ5Mzc4NzBkNjk4ODUxYjJmNzI5ZDljM2E1OGY4NzM4YzUyYzc1MGFhYzgwMGY2NjJmM2JlN2Q5OWUwODJmIiwidGFnIjoiIn0%3D; botble_cookie_newsletter=1; remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpibjFnQzQrTDFHeWZNOTFoK1F4R2c9PSIsInZhbHVlIjoiWlBCZ2pyclRiZU9razRVZjF3YU1JVjl3ZlcrTVZKVW9JVlplS2lQOStvUVdjM0RCeWx3a2YrOEpicWtScGdBcXJtY1hZZ3BJOTdhR2dHdmRPYXB0VlZjcG5Mb1hjYWc3TnNxT1Z3RVJZaEM0VXRsSjMvWHp1VTIzR3gvTmJtbUh1dlhiSFhjZzZqMU1JVUVjRVQwZURXeUt5eGFuclh4TVFlSkdyeFQzMVkvdGhRWVlsenZPSG8rNWRNUVU5RG9KYUh6S1YwWmVwOUtCTWd5ckROdnpkZ3gxeTJ1WWdpR29mMUJ6TUpRUTdHQT0iLCJtYWMiOiI3M2Q5NDM1Y2Q2ZWU1NTMzNzczMjdhYWM3ZmRjNTJhZTFjNThiNTFlOTllZGZhM2RlOTIwNWI0OWI1M2U2MGI0IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImowVTFVdlFvSmxrTjM3REp2d3dET3c9PSIsInZhbHVlIjoiaEZmTWdndm1xaWlxQ2ppUUVFNGdtM2FTTWN0bEIxdExXNWVrUGtZZnBlSkZRaW9pb0ZCMmZINWU1WDNuc1dHQ05IZnVwN2RUR1Nud2dwdlBPWDRMdlJzNnEwNDFkSmdCVXhxNndHV2tvZkVzc25xRGNjMENmakMvcG1PekVLT1ciLCJtYWMiOiJkMzI4MzZkNzUzNWVjNGUyYTU4NDQxNDgyNGU2YjkyYmI2OTQ3OTQzOTM2MjczM2IxZGI1MjVhZmU1OTZjMWMxIiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6IjBhcmRqZ0JhWlphbG1xQWZGRGUvclE9PSIsInZhbHVlIjoiOGVnMmo2L2I5MmtVOGgrTjk5TVFaUHVCWjB6L0dkMm1SZ0hBSW5TeHpIbCs3SkI0cUtUcm9KY1hCeDV4cVd1ZzFUeDRRNmtXVVg1cStLOUM3alBWTmhwM053Y1JaVkJMQzdtL2F0a0kxVGlVR2JTUTg5QjlRZmpaQ0VKbDlUUCsiLCJtYWMiOiIwNGNmNzFiYmQyNTk2NTA2Mjg5ZmRmOTE2MzljYWUxNTQ2OTEzNmZhYjhhYjJhMDc2MDA4NDBhMWEzYjZhZTY4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1651307877\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-669721620 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5d4a7afb407de91a3f785dca41f29624d4035861</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"347 characters\">{&quot;footprint&quot;:&quot;5d4a7afb407de91a3f785dca41f29624d4035861&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;martfury.gc&quot;,&quot;landing_page&quot;:&quot;\\/&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:&quot;http:\\/\\/localhost\\/&quot;,&quot;referrer_domain&quot;:&quot;localhost&quot;}</span>\"\n  \"<span class=sf-dump-key>shortcode_cache_suggestion_dismissed</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>widget_cache_suggestion_dismissed</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|mr2hzXolFPMrElNypPbMsV1VAiAKgY80Q2lBNyezSZXf4Bow8DqaNYnj6c6c|$2y$12$faChkoM7UV9t8mCoo6oJFOLYCOlwKR0CpAW2E1NPY0Bj1MIeVQw92</span>\"\n  \"<span class=sf-dump-key>botble_cookie_newsletter</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">2|rEY2fnBoxjVikI7FHOoHpfsUL6HmShbqbiBbMajXCGvqp4DJneBXZNsMDEhJ|$2y$12$mAIYinlq8PEmipS0gyL5O.c3YY4axUf.8ExV94mmLdgzhultxp2KS</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6AiUEtIqMruJngQhfx6V5J3mOZ7KIDVH5jSHUxUL</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IlGMU8ikcYH0PyBAGquP1yLFBc9E8t7YmNqOCnbe</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-669721620\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-235794253 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 15 Aug 2025 15:33:10 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-235794253\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-823624046 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6AiUEtIqMruJngQhfx6V5J3mOZ7KIDVH5jSHUxUL</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>math-captcha</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>first</span>\" => <span class=sf-dump-num>4</span>\n    \"<span class=sf-dump-key>second</span>\" => <span class=sf-dump-num>8</span>\n    \"<span class=sf-dump-key>operand</span>\" => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"24 characters\">https://martfury.gc/cart</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>cart</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>cart_updated_at</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1755271956\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1755271956</span></span> {<a class=sf-dump-ref href=#sf-dump-823624046-ref24359 title=\"3 occurrences\">#4359</a><samp data-depth=3 id=sf-dump-823624046-ref24359 class=sf-dump-compact>\n      #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n      #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n      #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">00000000000011070000000000000000</span>\"\n      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n        <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n      </samp>]\n      #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n      <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Friday, August 15, 2025\n- 00:00:34.76561 from now\nDST Off\">2025-08-15 15:32:36.732686 UTC (+00:00)</span>\n    </samp>}\n  </samp>]\n  \"<span class=sf-dump-key>tracked_start_checkout</span>\" => \"<span class=sf-dump-str title=\"32 characters\">2bbdbd08f9ec9fc36d8b3e7ced0cf369</span>\"\n  \"<span class=sf-dump-key>2162c5409405ee8ecb45d303ef2ad5a8</span>\" => <span class=sf-dump-note>array:15</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>promotion_discount_amount</span>\" => <span class=sf-dump-num>0</span>\n    \"<span class=sf-dump-key>billing_address_same_as_shipping_address</span>\" => <span class=sf-dump-const>true</span>\n    \"<span class=sf-dump-key>billing_address</span>\" => []\n    \"<span class=sf-dump-key>marketplace</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:17</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>billing_address_same_as_shipping_address</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>billing_address</span>\" => []\n        \"<span class=sf-dump-key>created_order</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1755269989\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1755269989</span></span> {<a class=sf-dump-ref href=#sf-dump-823624046-ref24360 title=\"2 occurrences\">#4360</a><samp data-depth=5 id=sf-dump-823624046-ref24360 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">00000000000011080000000000000000</span>\"\n          -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n            <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n          <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Friday, August 15, 2025\n- 00:33:21.839133 from now\nDST Off\">2025-08-15 14:59:49.659652 UTC (+00:00)</span>\n        </samp>}\n        \"<span class=sf-dump-key>created_order_id</span>\" => <span class=sf-dump-num>47</span>\n        \"<span class=sf-dump-key>is_save_order_shipping_address</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>created_order_product</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1755269989\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1755269989</span></span> {<a class=sf-dump-ref href=#sf-dump-823624046-ref24360 title=\"2 occurrences\">#4360</a>}\n        \"<span class=sf-dump-key>coupon_discount_amount</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>applied_coupon_code</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>is_free_shipping</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>promotion_discount_amount</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>shipping_method</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n        \"<span class=sf-dump-key>shipping_option</span>\" => \"<span class=sf-dump-str>3</span>\"\n        \"<span class=sf-dump-key>shipping_amount</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"\n        \"<span class=sf-dump-key>shipping</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>default</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Local Pickup</span>\"\n              \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"\n            </samp>]\n            <span class=sf-dump-key>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Flat Rate</span>\"\n              \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">20.00</span>\"\n            </samp>]\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>default_shipping_method</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n        \"<span class=sf-dump-key>default_shipping_option</span>\" => \"<span class=sf-dump-str>3</span>\"\n        \"<span class=sf-dump-key>is_available_shipping</span>\" => <span class=sf-dump-const>true</span>\n      </samp>]\n      <span class=sf-dump-key>1</span> => <span class=sf-dump-note>array:27</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Ishtiaq Ahmed</span>\"\n        \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"22 characters\"><EMAIL></span>\"\n        \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"13 characters\">+923147552550</span>\"\n        \"<span class=sf-dump-key>address</span>\" => \"<span class=sf-dump-str title=\"4 characters\">test</span>\"\n        \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"2 characters\">PK</span>\"\n        \"<span class=sf-dump-key>state</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Test</span>\"\n        \"<span class=sf-dump-key>city</span>\" => \"<span class=sf-dump-str title=\"4 characters\">test</span>\"\n        \"<span class=sf-dump-key>zip_code</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>created_order</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1755271956\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1755271956</span></span> {<a class=sf-dump-ref href=#sf-dump-823624046-ref24359 title=\"3 occurrences\">#4359</a>}\n        \"<span class=sf-dump-key>created_order_id</span>\" => <span class=sf-dump-num>50</span>\n        \"<span class=sf-dump-key>is_save_order_shipping_address</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>created_order_address</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>created_order_address_id</span>\" => <span class=sf-dump-num>44</span>\n        \"<span class=sf-dump-key>created_order_product</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1755271956\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1755271956</span></span> {<a class=sf-dump-ref href=#sf-dump-823624046-ref24359 title=\"3 occurrences\">#4359</a>}\n        \"<span class=sf-dump-key>billing_address_same_as_shipping_address</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>billing_address</span>\" => []\n        \"<span class=sf-dump-key>coupon_discount_amount</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>applied_coupon_code</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>is_free_shipping</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>promotion_discount_amount</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>shipping_method</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n        \"<span class=sf-dump-key>shipping_option</span>\" => \"<span class=sf-dump-str>3</span>\"\n        \"<span class=sf-dump-key>shipping_amount</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"\n        \"<span class=sf-dump-key>shipping</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>default</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Local Pickup</span>\"\n              \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"\n            </samp>]\n            <span class=sf-dump-key>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Flat Rate</span>\"\n              \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">20.00</span>\"\n            </samp>]\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>default_shipping_method</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n        \"<span class=sf-dump-key>default_shipping_option</span>\" => \"<span class=sf-dump-str>3</span>\"\n        \"<span class=sf-dump-key>is_available_shipping</span>\" => <span class=sf-dump-const>true</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Ishtiaq Ahmed</span>\"\n    \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"22 characters\"><EMAIL></span>\"\n    \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"13 characters\">+923147552550</span>\"\n    \"<span class=sf-dump-key>address</span>\" => \"<span class=sf-dump-str title=\"4 characters\">test</span>\"\n    \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"2 characters\">PK</span>\"\n    \"<span class=sf-dump-key>state</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Test</span>\"\n    \"<span class=sf-dump-key>city</span>\" => \"<span class=sf-dump-str title=\"4 characters\">test</span>\"\n    \"<span class=sf-dump-key>zip_code</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>shipping_method</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Ecommerce\\Enums\\ShippingMethodEnum\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Ecommerce\\Enums</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ShippingMethodEnum</span></span> {<a class=sf-dump-ref>#4361</a><samp data-depth=3 class=sf-dump-compact>\n      #<span class=sf-dump-protected title=\"Protected property\">value</span>: \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n    </samp>}\n    \"<span class=sf-dump-key>shipping_option</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>shipping_amount</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>selected_payment_method</span>\" => \"<span class=sf-dump-str title=\"8 characters\">hyperpay</span>\"\n  \"<span class=sf-dump-key>order_id</span>\" => <span class=sf-dump-num>50</span>\n  \"<span class=sf-dump-key>hyperpay_checkout_id</span>\" => \"<span class=sf-dump-str title=\"46 characters\">12C5D25827A98F4F6E18579C833A0E1F.uat01-vm-tx02</span>\"\n  \"<span class=sf-dump-key>hyperpay_payment_type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">visa</span>\"\n  \"<span class=sf-dump-key>hyperpay_order_id</span>\" => <span class=sf-dump-num>50</span>\n  \"<span class=sf-dump-key>hyperpay_amount</span>\" => <span class=sf-dump-num>154.0</span>\n  \"<span class=sf-dump-key>hyperpay_currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SAR</span>\"\n  \"<span class=sf-dump-key>hyperpay_customer_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>hyperpay_customer_type</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Botble\\Ecommerce\\Models\\Customer</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-823624046\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://martfury.gc/admin/ecommerce/orders", "action_name": "orders.index", "controller_action": "Botble\\Ecommerce\\Http\\Controllers\\OrderController@index"}, "badge": null}}