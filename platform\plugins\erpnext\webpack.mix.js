const mix = require('laravel-mix')
const path = require('path')

const directory = path.basename(path.resolve(__dirname))
const source = `platform/plugins/${directory}`
const dist = `public/vendor/core/plugins/${directory}`

mix
    .js(`${source}/resources/js/product-import.js`, `${dist}/js/product-import.js`)
    .sass(`${source}/resources/sass/product-import.scss`, `${dist}/css`)

if (mix.inProduction()) {
    mix
        .copy(`${dist}/js/product-import.js`, `${source}/public/js`)
        .copy(`${dist}/css/product-import.css`, `${source}/public/css`)
}
