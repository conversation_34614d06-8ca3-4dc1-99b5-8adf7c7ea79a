<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Tabby Payment Gateway Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration options for the Tabby Pay-in-4 payment gateway integration
    |
    */

    'name' => 'Tabby Pay-in-4',

    'description' => 'Pay in 4 installments with Tabby. No interest, no fees.',

    /*
    |--------------------------------------------------------------------------
    | API Configuration
    |--------------------------------------------------------------------------
    */

    'api' => [
        'sandbox_url' => 'https://api.tabby.ai',
        'live_url' => 'https://api.tabby.ai',
        'timeout' => 30,
        'retry_attempts' => 3,
    ],

    /*
    |--------------------------------------------------------------------------
    | Supported Currencies
    |--------------------------------------------------------------------------
    |
    | List of currencies supported by Tabby
    |
    */

    'supported_currencies' => [
        'AED', // UAE Dirham
        'SAR', // Saudi Riyal
        'KWD', // Kuwaiti Dinar
        'BHD', // Bahraini Dinar
        'QAR', // Qatari Riyal
        'EGP', // Egyptian Pound
    ],

    /*
    |--------------------------------------------------------------------------
    | Payment Limits
    |--------------------------------------------------------------------------
    |
    | Default payment limits (these may vary by merchant and region)
    |
    */

    'limits' => [
        'min_amount' => [
            'AED' => 55,
            'SAR' => 55,
            'KWD' => 15,
            'BHD' => 15,
            'QAR' => 55,
            'EGP' => 200,
        ],
        'max_amount' => [
            'AED' => 10000,
            'SAR' => 10000,
            'KWD' => 2500,
            'BHD' => 2500,
            'QAR' => 10000,
            'EGP' => 50000,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Rejection Messages
    |--------------------------------------------------------------------------
    |
    | User-friendly messages for different rejection reasons
    |
    */

    'rejection_messages' => [
        'not_available' => 'Sorry, Tabby is unable to approve this purchase. Please use an alternative payment method for your order.',
        'order_amount_too_high' => 'This purchase is above your current spending limit with Tabby, try a smaller cart or use another payment method.',
        'order_amount_too_low' => 'The purchase amount is below the minimum amount required to use Tabby, try adding more items or use another payment method.',
        'customer_not_eligible' => 'You are not eligible for Tabby Pay-in-4 at this time. Please use an alternative payment method.',
        'region_not_supported' => 'Tabby is not available in your region. Please use an alternative payment method.',
    ],

    /*
    |--------------------------------------------------------------------------
    | Webhook Configuration
    |--------------------------------------------------------------------------
    */

    'webhook' => [
        'timeout' => 20, // seconds
        'retry_attempts' => 3,
        'retry_delay' => 60, // seconds between retries
    ],

    /*
    |--------------------------------------------------------------------------
    | Session Configuration
    |--------------------------------------------------------------------------
    */

    'session' => [
        'expiry_minutes' => 20, // Default session expiry time
        'grace_period_minutes' => 5, // Additional time before payment expires
    ],

    /*
    |--------------------------------------------------------------------------
    | Logging Configuration
    |--------------------------------------------------------------------------
    */

    'logging' => [
        'enabled' => true,
        'level' => 'info', // debug, info, warning, error
        'channel' => 'daily',
    ],

    /*
    |--------------------------------------------------------------------------
    | Feature Flags
    |--------------------------------------------------------------------------
    */

    'features' => [
        'auto_capture' => true, // Automatically capture authorized payments
        'partial_refunds' => true, // Allow partial refunds
        'webhook_signature_validation' => false, // Validate webhook signatures
        'pre_scoring' => true, // Enable pre-scoring checks
    ],

    /*
    |--------------------------------------------------------------------------
    | UI Configuration
    |--------------------------------------------------------------------------
    */

    'ui' => [
        'show_installment_info' => true,
        'show_eligibility_status' => true,
        'logo_height' => '24px',
        'primary_color' => '#3ECFB0',
    ],
];
