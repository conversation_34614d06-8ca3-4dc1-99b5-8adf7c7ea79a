<?php

namespace Shaqi\ERPNext\Services;

use Botble\Ecommerce\Models\Product;
use Botble\Ecommerce\Models\ProductCategory;
use Botble\Base\Enums\BaseStatusEnum;
use Botble\Ecommerce\Enums\StockStatusEnum;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Shaqi\ERPNext\Services\ERPNextLogger;

class ERPNextProductImportService
{
    protected array $importResults = [
        'total' => 0,
        'imported' => 0,
        'skipped' => 0,
        'errors' => 0,
        'details' => []
    ];

    /**
     * Import products from selected ERPNext Item Groups
     */
    public function importProducts(array $selectedItemGroups): array
    {
        ERPNextLogger::info("Starting product import", [
            'selected_groups' => $selectedItemGroups
        ]);

        try {
            // Fetch products from ERPNext
            $erpNextProducts = ERPNextService::getProductsByItemGroups($selectedItemGroups);
            $this->importResults['total'] = count($erpNextProducts);

            if (empty($erpNextProducts)) {
                ERPNextLogger::warning("No products found in selected Item Groups");
                return $this->importResults;
            }

            // Process each product
            foreach ($erpNextProducts as $erpNextProduct) {
                $this->processProduct($erpNextProduct);
            }

            ERPNextLogger::info("Product import completed", $this->importResults);

        } catch (\Exception $e) {
            ERPNextLogger::error("Product import failed", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            $this->importResults['errors']++;
            $this->importResults['details'][] = [
                'type' => 'error',
                'message' => 'Import failed: ' . $e->getMessage()
            ];
        }

        return $this->importResults;
    }

    /**
     * Process a single product from ERPNext
     */
    protected function processProduct(array $erpNextProduct): void
    {
        try {
            // Check if product already exists by SKU
            $existingProduct = Product::where('sku', $erpNextProduct['item_code'])->first();

            if ($existingProduct) {
                $this->importResults['skipped']++;
                $this->importResults['details'][] = [
                    'type' => 'skipped',
                    'sku' => $erpNextProduct['item_code'],
                    'name' => $erpNextProduct['item_name'],
                    'reason' => 'Product with this SKU already exists'
                ];
                return;
            }

            // Create new product
            DB::transaction(function () use ($erpNextProduct) {
                $product = $this->createProduct($erpNextProduct);
                $this->assignProductCategory($product, $erpNextProduct['item_group']);
                $this->trackImport($product, $erpNextProduct, 'imported');

                $this->importResults['imported']++;
                $this->importResults['details'][] = [
                    'type' => 'imported',
                    'sku' => $erpNextProduct['item_code'],
                    'name' => $erpNextProduct['item_name'],
                    'id' => $product->id
                ];
            });

        } catch (\Exception $e) {
            $this->importResults['errors']++;
            $this->importResults['details'][] = [
                'type' => 'error',
                'sku' => $erpNextProduct['item_code'] ?? 'Unknown',
                'name' => $erpNextProduct['item_name'] ?? 'Unknown',
                'error' => $e->getMessage()
            ];

            ERPNextLogger::error("Failed to process product", [
                'product' => $erpNextProduct,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Create a new product from ERPNext data
     */
    protected function createProduct(array $erpNextProduct): Product
    {
        $product = new Product();

        $product->name = $erpNextProduct['item_name'] ?? $erpNextProduct['name'];
        $product->sku = $erpNextProduct['item_code'];
        $product->description = $erpNextProduct['description'] ?? '';
        $product->price = $erpNextProduct['standard_rate'] ?? 0;
        $product->status = BaseStatusEnum::PUBLISHED;
        $product->stock_status = StockStatusEnum::IN_STOCK;
        $product->quantity = 100; // Default quantity, can be updated later
        $product->with_storehouse_management = false;
        $product->is_featured = false;

        // Handle image if provided
        if (!empty($erpNextProduct['image'])) {
            $product->forceFill(['image' => $this->processProductImage($erpNextProduct['image'])]);
        }

        $product->save();

        ERPNextLogger::info("Created product", [
            'product_id' => $product->id,
            'sku' => $product->sku,
            'name' => $product->name
        ]);

        return $product;
    }

    /**
     * Assign product to category based on ERPNext Item Group
     */
    protected function assignProductCategory(Product $product, string $itemGroupName): void
    {
        // Find or create category based on Item Group
        $category = $this->findOrCreateCategory($itemGroupName);

        if ($category) {
            $product->categories()->attach($category->id);

            ERPNextLogger::info("Assigned product to category", [
                'product_id' => $product->id,
                'category_id' => $category->id,
                'category_name' => $category->name
            ]);
        }
    }

    /**
     * Find existing category or create new one based on Item Group
     */
    protected function findOrCreateCategory(string $itemGroupName): ?ProductCategory
    {
        // First try to find existing category
        $category = ProductCategory::where('name', $itemGroupName)->first();

        if (!$category) {
            // Create new category
            $category = new ProductCategory();
            $category->name = $itemGroupName;
            $category->status = BaseStatusEnum::PUBLISHED;
            $category->order = 0;
            $category->save();

            ERPNextLogger::info("Created new product category", [
                'category_id' => $category->id,
                'name' => $category->name
            ]);
        }

        return $category;
    }

    /**
     * Process product image URL from ERPNext
     */
    protected function processProductImage(string $imageUrl): ?string
    {
        // For now, just store the URL as is
        // In a production environment, you might want to download and store the image locally
        return $imageUrl;
    }

    /**
     * Track import in database
     */
    protected function trackImport(Product $product, array $erpNextProduct, string $status): void
    {
        try {
            DB::table('erpnext_product_imports')->insert([
                'product_id' => $product->id,
                'erpnext_item_code' => $erpNextProduct['item_code'],
                'erpnext_item_name' => $erpNextProduct['item_name'] ?? $erpNextProduct['name'],
                'erpnext_item_group' => $erpNextProduct['item_group'],
                'erpnext_standard_rate' => $erpNextProduct['standard_rate'] ?? null,
                'erpnext_description' => $erpNextProduct['description'] ?? null,
                'import_status' => $status,
                'import_data' => json_encode($erpNextProduct),
                'imported_at' => now(),
                'imported_by' => Auth::check() ? Auth::id() : null,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        } catch (\Exception $e) {
            ERPNextLogger::warning("Failed to track import", [
                'product_id' => $product->id,
                'item_code' => $erpNextProduct['item_code'],
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get import results
     */
    public function getImportResults(): array
    {
        return $this->importResults;
    }
}
