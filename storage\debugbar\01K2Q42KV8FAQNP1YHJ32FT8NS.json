{"__meta": {"id": "01K2Q42KV8FAQNP1YHJ32FT8NS", "datetime": "2025-08-15 15:02:52", "utime": **********.521307, "method": "GET", "uri": "/payment/hyperpay/callback?id=8FC0BD1D066C3A2AA927BD91EBF97169.uat01-vm-tx03&resourcePath=%2Fv1%2Fcheckouts%2F8FC0BD1D066C3A2AA927BD91EBF97169.uat01-vm-tx03%2Fpayment", "ip": "127.0.0.1"}, "messages": {"count": 4, "messages": [{"message": "[15:02:41] LOG.info: HyperPay Callback Received {\n    \"resource_path\": \"\\/v1\\/checkouts\\/8FC0BD1D066C3A2AA927BD91EBF97169.uat01-vm-tx03\\/payment\",\n    \"checkout_id\": \"8FC0BD1D066C3A2AA927BD91EBF97169.uat01-vm-tx03\",\n    \"all_params\": {\n        \"id\": \"8FC0BD1D066C3A2AA927BD91EBF97169.uat01-vm-tx03\",\n        \"resourcePath\": \"\\/v1\\/checkouts\\/8FC0BD1D066C3A2AA927BD91EBF97169.uat01-vm-tx03\\/payment\"\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.050184, "xdebug_link": null, "collector": "log"}, {"message": "[15:02:42] LOG.info: HyperPay Payment Status Check {\n    \"url\": \"https:\\/\\/eu-test.oppwa.com\\/v1\\/checkouts\\/8FC0BD1D066C3A2AA927BD91EBF97169.uat01-vm-tx03\\/payment\",\n    \"checkout_id\": \"8FC0BD1D066C3A2AA927BD91EBF97169.uat01-vm-tx03\",\n    \"status_code\": 400,\n    \"response\": {\n        \"result\": {\n            \"code\": \"200.300.404\",\n            \"description\": \"invalid or missing parameter\",\n            \"parameterErrors\": [\n                {\n                    \"name\": \"entityId\",\n                    \"value\": null,\n                    \"message\": \"invalid or missing parameter\"\n                }\n            ]\n        },\n        \"buildNumber\": \"8aa3728a6ba24ed3c4a6c6e9064ffd726f1bee67@2025-08-11 05:22:09 +0000\",\n        \"timestamp\": \"2025-08-15 15:02:42+0000\",\n        \"ndc\": \"8FC0BD1D066C3A2AA927BD91EBF97169.uat01-vm-tx03\"\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.218429, "xdebug_link": null, "collector": "log"}, {"message": "[15:02:42] LOG.info: HyperPay Processing Payment Hook {\n    \"order_id\": [\n        48\n    ],\n    \"order_ids\": [\n        48\n    ],\n    \"amount\": 154,\n    \"currency\": \"SAR\",\n    \"status\": \"failed\",\n    \"charge_id\": \"8FC0BD1D066C3A2AA927BD91EBF97169.uat01-vm-tx03\",\n    \"customer_id\": null,\n    \"customer_type\": \"Botble\\\\Ecommerce\\\\Models\\\\Customer\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.218957, "xdebug_link": null, "collector": "log"}, {"message": "[15:02:52] LOG.debug: From: Example <<EMAIL>>\r\nTo: <PERSON><PERSON><PERSON><PERSON>@example.org\r\nSubject: New order(s) at MartFury\r\nMIME-Version: 1.0\r\nDate: Fri, 15 Aug 2025 15:02:52 +0000\r\nMessage-ID: <<EMAIL>>\r\nContent-Type: text/html; charset=utf-8\r\nContent-Transfer-Encoding: quoted-printable\r\n\r\n<!doctype html>\r\n<html lang=\"en\">\r\n\r\n<head>\r\n    <meta charset=\"UTF-8\">\r\n    <title>MartFury</title>\r\n</head>\r\n\r\n<body class=\"bb-bg-body\" dir=\"ltr\" style=\"margin: 0; padding: 0; font-size: 14px; line-height: 171.4285714286%; mso-line-height-rule: exactly; color: #3A4859; width: 100%; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale; -webkit-font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11'; font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11'; font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; background-color: #f6f7f9;\">\r\n\r\n<center>\r\n    <table class=\"bb-main bb-bg-body\" width=\"100%\" cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; background-color: #f6f7f9;\">\r\n        <tbody>\r\n            <tr>\r\n                <td align=\"center\" valign=\"top\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;\">\r\n                    <table class=\"bb-wrap\" cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; max-width: 640px; text-align: left; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\r\n                        <tbody>\r\n                            <tr>\r\n                                <td class=\"bb-p-sm\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 8px;\">\r\n                                    <table cellpadding=\"0\" cellspacing=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\r\n                                        <tbody>\r\n                                            <tr>\r\n                                                <td class=\"bb-py-lg\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-top: 24px; padding-bottom: 24px;\">\r\n                                                    <table cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\r\n                                                        <tbody>\r\n                                                            <tr>\r\n                                                                <td class=\"bb-text-left\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: left;\">\r\n                                                                    <a href=\"https://martfury.gc\" style=\"color: #206bc4; text-decoration: none;\">\r\n                                                                        <img class=\"bb-logo\" src=\"https://martfury.gc/storage/general/logo.png\" alt=\"MartFury\" style=\"line-height: 100%; outline: none; text-decoration: none; vertical-align: baseline; font-size: 0; border: 0 none; max-height: 40px;\">\r\n                                                                    </a>\r\n                                                                </td>\r\n                                                                <td class=\"bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right;\">\r\n                                                                    2025-08-15 15:02:49\r\n                                                                </td>\r\n                                                            </tr>\r\n                                                        </tbody>\r\n                                                    </table>\r\n                                                </td>\r\n                                            </tr>\r\n                                        </tbody>\r\n                                    </table>\r\n\r\n\r\n<div class=\"bb-main-content\">\r\n    <table class=\"bb-box\" cellpadding=\"0\" cellspacing=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; background: #ffffff; border-radius: 4px; -webkit-box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05); box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05); border: 1px solid #dce0e5; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\r\n        <tbody>\r\n            <tr>\r\n                <td class=\"bb-content bb-pb-0\" align=\"center\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; padding-bottom: 0;\">\r\n                    <table class=\"bb-icon bb-icon-lg bb-bg-blue\" cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 0; border-radius: 50%; background: #edeef0; line-height: 100%; font-weight: 300; border-collapse: separate; text-align: center; width: 72px; height: 72px; font-size: 48px; background-color: #206bc4; color: #ffffff; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\r\n                        <tbody>\r\n                            <tr>\r\n                                <td valign=\"middle\" align=\"center\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;\">\r\n                                    <img src=\"https://martfury.gc/vendor/core/core/base/images/email-icons/shopping-cart.png\" class=\"bb-va-middle\" width=\"40\" height=\"40\" alt=\"Icon\" style=\"border: 0 none; line-height: 100%; outline: none; text-decoration: none; font-size: 0; vertical-align: middle; display: block; width: 40px; height: 40px;\">\r\n                                </td>\r\n                            </tr>\r\n                        </tbody>\r\n                    </table>\r\n                    <h1 class=\"bb-text-center bb-m-0 bb-mt-md\" style=\"font-weight: 600; color: #232b42; font-size: 28px; line-height: 130%; text-align: center; margin: 0; margin-top: 16px;\">Order successfully!</h1>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td class=\"bb-content\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px;\">\r\n                    <div>Dear GoPro,</div>\r\n                    <div>You got a new order on MartFury!</div>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td class=\"bb-content bb-pt-0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; padding-top: 0;\">\r\n                    <table class=\"bb-row bb-mb-md\" cellpadding=\"0\" cellspacing=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; table-layout: fixed; margin-bottom: 16px; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\r\n                        <tbody>\r\n                            <tr>\r\n                                <td class=\"bb-bb-col\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;\">\r\n                                    <h4 class=\"bb-m-0\" style=\"font-weight: 600; color: #232b42; font-size: 16px; margin: 0;\">Customer Information</h4>\r\n                                    <div>Name: <strong style=\"font-weight: 600;\">Ishtiaq Ahmed</strong>\r\n</div>\r\n                                                                        <div>Phone: <strong style=\"font-weight: 600;\">+923147552550</strong>\r\n</div>\r\n                                                                                                                                                <div>Address: <strong style=\"font-weight: 600;\">test, test, Test, PK</strong>\r\n</div>\r\n                                                                    </td>\r\n                            </tr>\r\n                        </tbody>\r\n                    </table>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td class=\"bb-content bb-pt-0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; padding-top: 0;\">\r\n                    <h4 style=\"font-weight: 600; margin: 0 0 0.5em; color: #232b42; font-size: 16px;\">Here's what you ordered:</h4>\r\n                    <a class=\"button button-blue\" href=\"https://martfury.gc/orders/tracking?order_id=%2310000047&amp;email=rajaishtiaq6%40gmail.com\" style=\"color: #206bc4; text-decoration: none;\">View order</a>\r\n    or <a href=\"https://martfury.gc\" style=\"color: #206bc4; text-decoration: none;\">Go to our shop</a>\r\n\r\n    <br>\r\n\r\n<table class=\"bb-table\" cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\r\n    <thead>\r\n        <tr>\r\n            <th colspan=\"2\" style=\"text-transform: uppercase; font-weight: 600; color: #667382; font-size: 12px; padding: 0 0 4px 0; padding-left: 0;\"></th>\r\n            <th style=\"text-transform: uppercase; font-weight: 600; color: #667382; font-size: 12px; padding: 0 0 4px 0;\">Quantity</th>\r\n            <th class=\"bb-text-right\" style=\"text-align: right; text-transform: uppercase; font-weight: 600; color: #667382; font-size: 12px; padding: 0 0 4px 0; padding-right: 0;\">Price</th>\r\n        </tr>\r\n    </thead>\r\n\r\n    <tbody>\r\n                <tr>\r\n            <td class=\"bb-pr-0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-right: 0; padding: 4px 0; padding-left: 0;\">\r\n                <a href=\"\" style=\"color: #206bc4; text-decoration: none;\">\r\n                    <img src=\"https://martfury.gc/storage/products/3-150x150.jpg\" class=\" bb-rounded\" width=\"64\" height=\"64\" alt=\"\" style=\"line-height: 100%; outline: none; text-decoration: none; vertical-align: baseline; font-size: 0; border: 0 none; border-radius: 4px;\">\r\n                </a>\r\n            </td>\r\n            <td class=\"bb-pl-md bb-w-100p\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; width: 100%; padding-left: 16px !important; padding: 4px 0;\">\r\n                <strong style=\"font-weight: 600;\">Beat Headphone</strong><br>\r\n                                    <span class=\"bb-text-muted\" style=\"color: #667382;\">(Color: Green, Size: L)</span>\r\n                \r\n                            </td>\r\n            <td class=\"bb-text-center\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: center; padding: 4px 0;\">x 7</td>\r\n            <td class=\"bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; padding: 4px 0; padding-right: 0;\">SAR20.00</td>\r\n        </tr>\r\n    \r\n                                    <tr>\r\n                    <td colspan=\"2\" class=\"bb-border-top bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; border-top: 1px solid #dce0e5; padding: 4px 0; padding-left: 0;\">Subtotal</td>\r\n                    <td colspan=\"2\" class=\"bb-border-top bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; border-top: 1px solid #dce0e5; padding: 4px 0; padding-right: 0;\">SAR140.00</td>\r\n                </tr>\r\n            \r\n            \r\n                            <tr>\r\n                    <td colspan=\"2\" class=\"bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; padding: 4px 0; padding-left: 0;\">Tax</td>\r\n                    <td colspan=\"2\" class=\"bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; padding: 4px 0; padding-right: 0;\">SAR14.00</td>\r\n                </tr>\r\n            \r\n            \r\n                        <tr>\r\n                <td colspan=\"2\" class=\"bb-text-right bb-font-strong bb-h3 bb-m-0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; color: #232b42; font-size: 20px; line-height: 130%; text-align: right; font-weight: 600; margin: 0; padding: 4px 0; padding-left: 0;\">Total</td>\r\n                <td colspan=\"2\" class=\"bb-font-strong bb-h3 bb-m-0 bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; color: #232b42; font-size: 20px; line-height: 130%; text-align: right; font-weight: 600; margin: 0; padding: 4px 0; padding-right: 0;\">SAR154.00</td>\r\n            </tr>\r\n            </tbody>\r\n</table>\r\n\r\n\r\n                                    </td>\r\n            </tr>\r\n            <tr>\r\n                <td class=\"bb-content bb-border-top\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; border-top: 1px solid #dce0e5;\">\r\n                    <table class=\"bb-row bb-mb-md\" cellpadding=\"0\" cellspacing=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; table-layout: fixed; margin-bottom: 16px; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\r\n                        <tbody>\r\n                            <tr>\r\n                                <td class=\"bb-bb-col\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;\">\r\n                                    <h4 class=\"bb-m-0\" style=\"font-weight: 600; color: #232b42; font-size: 16px; margin: 0;\">Order number</h4>\r\n                                    <div>#10000047</div>\r\n                                </td>\r\n                                <td class=\"bb-col-spacer\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top; width: 24px;\"></td>\r\n                                <td class=\"bb-col\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top;\">\r\n                                    <h4 class=\"bb-mb-0\" style=\"font-weight: 600; margin: 0 0 0.5em; color: #232b42; font-size: 16px; margin-bottom: 0;\">Order date</h4>\r\n                                    <div>2025-08-15 15:00:08</div>\r\n                                </td>\r\n                            </tr>\r\n                        </tbody>\r\n                    </table>\r\n                    <table class=\"bb-row\" cellpadding=\"0\" cellspacing=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; table-layout: fixed; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\r\n                        <tbody>\r\n                            <tr>\r\n                                <td class=\"bb-col\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top;\">\r\n                                                                        <h4 class=\"bb-m-0\" style=\"font-weight: 600; color: #232b42; font-size: 16px; margin: 0;\">Shipping Method</h4>\r\n                                    <div>\r\n                                        Local Pickup\r\n                                    </div>\r\n                                                                    </td>\r\n\r\n                                <td class=\"bb-col-spacer\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top; width: 24px;\"></td>\r\n                                <td class=\"bb-col\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top;\">\r\n                                    <h4 class=\"bb-m-0\" style=\"font-weight: 600; color: #232b42; font-size: 16px; margin: 0;\">Payment Method</h4>\r\n                                    <div>\r\n                                        HyperPay\r\n                                    </div>\r\n                                </td>\r\n                            </tr>\r\n                        </tbody>\r\n                    </table>\r\n                </td>\r\n            </tr>\r\n        </tbody>\r\n    </table>\r\n</div>\r\n\r\n<table cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\r\n    <tbody>\r\n        <tr>\r\n            <td class=\"bb-py-xl\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-top: 48px; padding-bottom: 48px;\">\r\n                <table class=\"bb-text-center bb-text-muted\" cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; color: #667382; text-align: center; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\r\n                    <tbody>\r\n                    \r\n                    <tr>\r\n                        <td class=\"bb-px-lg\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-right: 24px; padding-left: 24px;\">\r\n                            © 2025 MartFury. All Rights Reserved.\r\n                        </td>\r\n                    </tr>\r\n\r\n                                            <tr>\r\n                            <td class=\"bb-pt-md\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-top: 16px;\">\r\n                                If you have any questions, feel free to message us at <a href=\"mailto:<EMAIL>\" style=\"color: #206bc4; text-decoration: none;\"><EMAIL></a>.\r\n                            </td>\r\n                        </tr>\r\n                                        </tbody>\r\n                </table>\r\n            </td>\r\n        </tr>\r\n    </tbody>\r\n</table>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table>\r\n</center>\r\n</body>\r\n\r\n</html>", "message_html": null, "is_string": false, "label": "debug", "time": **********.371338, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 9, "start": 1755270160.357346, "end": **********.521331, "duration": 12.163985013961792, "duration_str": "12.16s", "measures": [{"label": "Booting", "start": 1755270160.357346, "relative_start": 0, "end": **********.000578, "relative_end": **********.000578, "duration": 0.****************, "duration_str": "643ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.000589, "relative_start": 0.****************, "end": **********.521333, "relative_end": 1.9073486328125e-06, "duration": 11.**************, "duration_str": "11.52s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.01751, "relative_start": 0.****************, "end": **********.028787, "relative_end": **********.028787, "duration": 0.011276960372924805, "duration_str": "11.28ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: plugins/marketplace::emails.partials.order-detail", "start": **********.509929, "relative_start": 9.***************, "end": **********.509929, "relative_end": **********.509929, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::emails.partials.order-detail", "start": **********.511714, "relative_start": 9.***************, "end": **********.511714, "relative_end": **********.511714, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::emails.partials.order-detail", "start": **********.542429, "relative_start": 9.185082912445068, "end": **********.542429, "relative_end": **********.542429, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::emails.partials.order-delivery-notes", "start": **********.548419, "relative_start": 9.191072940826416, "end": **********.548419, "relative_end": **********.548419, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "mail: New order(s) at MartFury", "start": **********.089842, "relative_start": 11.7324960231781, "end": **********.405995, "relative_end": **********.405995, "duration": 0.31615281105041504, "duration_str": "316ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "mail", "group": null}, {"label": "Preparing Response", "start": **********.517144, "relative_start": 12.15979790687561, "end": **********.517779, "relative_end": **********.517779, "duration": 0.0006351470947265625, "duration_str": "635μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 73530152, "peak_usage_str": "70MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.21.0", "PHP Version": "8.3.17", "Environment": "local", "Debug Mode": "Enabled", "URL": "martfury.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 4, "nb_templates": 4, "templates": [{"name": "plugins/marketplace::emails.partials.order-detail", "param_count": null, "params": [], "start": **********.509895, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/marketplace/resources/views/emails/partials/order-detail.blade.phpplugins/marketplace::emails.partials.order-detail", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fmarketplace%2Fresources%2Fviews%2Femails%2Fpartials%2Forder-detail.blade.php&line=1", "ajax": false, "filename": "order-detail.blade.php", "line": "?"}}, {"name": "plugins/ecommerce::emails.partials.order-detail", "param_count": null, "params": [], "start": **********.51167, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/ecommerce/resources/views/emails/partials/order-detail.blade.phpplugins/ecommerce::emails.partials.order-detail", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Femails%2Fpartials%2Forder-detail.blade.php&line=1", "ajax": false, "filename": "order-detail.blade.php", "line": "?"}}, {"name": "plugins/ecommerce::emails.partials.order-detail", "param_count": null, "params": [], "start": **********.542403, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/ecommerce/resources/views/emails/partials/order-detail.blade.phpplugins/ecommerce::emails.partials.order-detail", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Femails%2Fpartials%2Forder-detail.blade.php&line=1", "ajax": false, "filename": "order-detail.blade.php", "line": "?"}}, {"name": "plugins/ecommerce::emails.partials.order-delivery-notes", "param_count": null, "params": [], "start": **********.548394, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/ecommerce/resources/views/emails/partials/order-delivery-notes.blade.phpplugins/ecommerce::emails.partials.order-delivery-notes", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Femails%2Fpartials%2Forder-delivery-notes.blade.php&line=1", "ajax": false, "filename": "order-delivery-notes.blade.php", "line": "?"}}]}, "queries": {"count": 68, "nb_statements": 68, "nb_visible_statements": 68, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.16584999999999997, "accumulated_duration_str": "166ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Footprints/TrackingFilter.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Footprints\\TrackingFilter.php", "line": 45}], "start": **********.044661, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 0, "width_percent": 0.265}, {"sql": "select * from `ec_orders` where `id` in (48)", "type": "query", "params": [], "bindings": [48], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 309}, {"index": 20, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 22, "namespace": null, "name": "platform/plugins/hyperpay/src/Http/Controllers/HyperPayController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\hyperpay\\src\\Http\\Controllers\\HyperPayController.php", "line": 76}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.2193708, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 0.265, "width_percent": 0.235}, {"sql": "select * from `ec_currencies` where `is_default` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/CurrencySupport.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\CurrencySupport.php", "line": 74}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 311}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 24, "namespace": null, "name": "platform/plugins/hyperpay/src/Http/Controllers/HyperPayController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\hyperpay\\src\\Http\\Controllers\\HyperPayController.php", "line": 76}], "start": **********.222026, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 0.5, "width_percent": 0.398}, {"sql": "select * from `payments` where `charge_id` = '8FC0BD1D066C3A2AA927BD91EBF97169.uat01-vm-tx03' and `order_id` in (48) limit 1", "type": "query", "params": [], "bindings": ["8FC0BD1D066C3A2AA927BD91EBF97169.uat01-vm-tx03", 48], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/payment/src/Supports/PaymentHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\payment\\src\\Supports\\PaymentHelper.php", "line": 39}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 318}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 24, "namespace": null, "name": "platform/plugins/hyperpay/src/Http/Controllers/HyperPayController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\hyperpay\\src\\Http\\Controllers\\HyperPayController.php", "line": 76}], "start": **********.226882, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 0.898, "width_percent": 0.916}, {"sql": "insert into `payments` (`amount`, `payment_fee`, `currency`, `charge_id`, `order_id`, `customer_id`, `customer_type`, `payment_channel`, `status`, `updated_at`, `created_at`) values ('154.00', 0, 'SAR', '8FC0BD1D066C3A2AA927BD91EBF97169.uat01-vm-tx03', 48, null, 'Botble\\\\Ecommerce\\\\Models\\\\Customer', 'hyperpay', 'failed', '2025-08-15 15:02:42', '2025-08-15 15:02:42')", "type": "query", "params": [], "bindings": ["154.00", 0, "SAR", "8FC0BD1D066C3A2AA927BD91EBF97169.uat01-vm-tx03", 48, null, "Botble\\Ecommerce\\Models\\Customer", {"value": "hyperpay", "label": "HyperPay"}, {"value": "failed", "label": "Failed"}, "2025-08-15 15:02:42", "2025-08-15 15:02:42"], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "platform/plugins/payment/src/Supports/PaymentHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\payment\\src\\Supports\\PaymentHelper.php", "line": 60}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 318}, {"index": 23, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 25, "namespace": null, "name": "platform/plugins/hyperpay/src/Http/Controllers/HyperPayController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\hyperpay\\src\\Http\\Controllers\\HyperPayController.php", "line": 76}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.238354, "duration": 0.00509, "duration_str": "5.09ms", "memory": 0, "memory_str": null, "filename": "PaymentHelper.php:60", "source": {"index": 18, "namespace": null, "name": "platform/plugins/payment/src/Supports/PaymentHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\payment\\src\\Supports\\PaymentHelper.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fpayment%2Fsrc%2FSupports%2FPaymentHelper.php&line=60", "ajax": false, "filename": "PaymentHelper.php", "line": "60"}, "connection": "martfury", "explain": null, "start_percent": 1.815, "width_percent": 3.069}, {"sql": "select * from `ec_orders` where `id` in (48)", "type": "query", "params": [], "bindings": [48], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 73}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 321}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 24, "namespace": null, "name": "platform/plugins/hyperpay/src/Http/Controllers/HyperPayController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\hyperpay\\src\\Http\\Controllers\\HyperPayController.php", "line": 76}], "start": **********.255908, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 4.884, "width_percent": 0.344}, {"sql": "select * from `payments` where `charge_id` = '8FC0BD1D066C3A2AA927BD91EBF97169.uat01-vm-tx03' and `order_id` in (48)", "type": "query", "params": [], "bindings": ["8FC0BD1D066C3A2AA927BD91EBF97169.uat01-vm-tx03", 48], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 83}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 321}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 24, "namespace": null, "name": "platform/plugins/hyperpay/src/Http/Controllers/HyperPayController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\hyperpay\\src\\Http\\Controllers\\HyperPayController.php", "line": 76}], "start": **********.257994, "duration": 0.00421, "duration_str": "4.21ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 5.228, "width_percent": 2.538}, {"sql": "update `ec_orders` set `payment_id` = 85, `ec_orders`.`updated_at` = '2025-08-15 15:02:42' where `id` = 48", "type": "query", "params": [], "bindings": [85, "2025-08-15 15:02:42", 48], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 90}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 321}, {"index": 20, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 22, "namespace": null, "name": "platform/plugins/hyperpay/src/Http/Controllers/HyperPayController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\hyperpay\\src\\Http\\Controllers\\HyperPayController.php", "line": 76}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.2660701, "duration": 0.00483, "duration_str": "4.83ms", "memory": 0, "memory_str": null, "filename": "OrderHelper.php:90", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FOrderHelper.php&line=90", "ajax": false, "filename": "OrderHelper.php", "line": "90"}, "connection": "martfury", "explain": null, "start_percent": 7.766, "width_percent": 2.912}, {"sql": "select * from `ec_orders` where `ec_orders`.`id` = 48 limit 1", "type": "query", "params": [], "bindings": [48], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 110}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 63}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesModels.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesModels.php", "line": 97}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php", "line": 62}], "start": **********.455762, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 10.678, "width_percent": 0.663}, {"sql": "select exists(select * from `ec_invoices` where `ec_invoices`.`reference_id` = 48 and `ec_invoices`.`reference_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [48], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 31}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/GenerateInvoiceListener.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\GenerateInvoiceListener.php", "line": 16}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}], "start": **********.4636052, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "InvoiceHelper.php:31", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FInvoiceHelper.php&line=31", "ajax": false, "filename": "InvoiceHelper.php", "line": "31"}, "connection": "martfury", "explain": null, "start_percent": 11.342, "width_percent": 0.585}, {"sql": "select * from `ec_order_addresses` where `ec_order_addresses`.`order_id` = 48 and `ec_order_addresses`.`order_id` is not null and `type` = 'shipping_address' limit 1", "type": "query", "params": [], "bindings": [48, "shipping_address"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 35}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/GenerateInvoiceListener.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\GenerateInvoiceListener.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.466529, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 11.926, "width_percent": 0.434}, {"sql": "select * from `ec_order_tax_information` where `ec_order_tax_information`.`order_id` = 48 and `ec_order_tax_information`.`order_id` is not null limit 1", "type": "query", "params": [], "bindings": [48], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 41}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/GenerateInvoiceListener.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\GenerateInvoiceListener.php", "line": 16}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.469147, "duration": 0.0055, "duration_str": "5.5ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 12.361, "width_percent": 3.316}, {"sql": "select exists(select * from `countries`) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 597}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Traits/LocationTrait.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Traits\\LocationTrait.php", "line": 74}, {"index": 19, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Traits/LocationTrait.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Traits\\LocationTrait.php", "line": 94}, {"index": 27, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.476695, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "EcommerceHelper.php:597", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 597}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FEcommerceHelper.php&line=597", "ajax": false, "filename": "EcommerceHelper.php", "line": "597"}, "connection": "martfury", "explain": null, "start_percent": 15.677, "width_percent": 0.187}, {"sql": "select * from `payments` where `payments`.`id` = 85 limit 1", "type": "query", "params": [], "bindings": [85], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 76}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/GenerateInvoiceListener.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\GenerateInvoiceListener.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.48075, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 15.864, "width_percent": 0.428}, {"sql": "select max(`id`) as aggregate from `ec_invoices`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Invoice.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Invoice.php", "line": 84}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Invoice.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Invoice.php", "line": 58}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 86}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/GenerateInvoiceListener.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\GenerateInvoiceListener.php", "line": 16}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.487751, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "Invoice.php:84", "source": {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Invoice.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Invoice.php", "line": 84}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FInvoice.php&line=84", "ajax": false, "filename": "Invoice.php", "line": "84"}, "connection": "martfury", "explain": null, "start_percent": 16.292, "width_percent": 0.513}, {"sql": "select exists(select * from `ec_invoices` where `code` = 'INV-40') as `exists`", "type": "query", "params": [], "bindings": ["INV-40"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Invoice.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Invoice.php", "line": 89}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Invoice.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Invoice.php", "line": 58}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 86}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/GenerateInvoiceListener.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\GenerateInvoiceListener.php", "line": 16}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.489389, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "Invoice.php:89", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Invoice.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Invoice.php", "line": 89}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FInvoice.php&line=89", "ajax": false, "filename": "Invoice.php", "line": "89"}, "connection": "martfury", "explain": null, "start_percent": 16.804, "width_percent": 0.398}, {"sql": "insert into `ec_invoices` (`reference_id`, `reference_type`, `company_name`, `company_logo`, `customer_name`, `customer_email`, `customer_phone`, `customer_address`, `customer_tax_id`, `payment_id`, `status`, `paid_at`, `tax_amount`, `shipping_amount`, `payment_fee`, `discount_amount`, `sub_total`, `amount`, `shipping_method`, `shipping_option`, `coupon_code`, `discount_description`, `description`, `created_at`, `code`, `updated_at`) values (48, 'Botble\\\\Ecommerce\\\\Models\\\\Order', '', null, 'Ishtiaq Ahmed', '<EMAIL>', '+923147552550', 'test, test, Test, PK', null, 85, 'failed', null, '14.00', '0.00', '0.00', '0.00', '140.00', '154.00', 'default', '3', null, null, null, '2025-08-15 15:00:08', 'INV-40', '2025-08-15 15:02:44')", "type": "query", "params": [], "bindings": [48, "Botble\\Ecommerce\\Models\\Order", "", null, "<PERSON><PERSON><PERSON><PERSON>", "<EMAIL>", "+923147552550", "test, test, Test, PK", null, 85, {"value": "failed", "label": "Failed"}, null, "14.00", "0.00", "0.00", "0.00", "140.00", "154.00", {"value": "default", "label": "<PERSON><PERSON><PERSON>"}, "3", null, null, null, "2025-08-15 15:00:08", "INV-40", "2025-08-15 15:02:44"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 86}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/GenerateInvoiceListener.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\GenerateInvoiceListener.php", "line": 16}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}], "start": **********.491218, "duration": 0.00366, "duration_str": "3.66ms", "memory": 0, "memory_str": null, "filename": "InvoiceHelper.php:86", "source": {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FInvoiceHelper.php&line=86", "ajax": false, "filename": "InvoiceHelper.php", "line": "86"}, "connection": "martfury", "explain": null, "start_percent": 17.202, "width_percent": 2.207}, {"sql": "select * from `ec_order_product` where `ec_order_product`.`order_id` = 48 and `ec_order_product`.`order_id` is not null", "type": "query", "params": [], "bindings": [48], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 88}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/GenerateInvoiceListener.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\GenerateInvoiceListener.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.497432, "duration": 0.00357, "duration_str": "3.57ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 19.409, "width_percent": 2.153}, {"sql": "select * from `ec_products` where `ec_products`.`id` in (28) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 27, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 28, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 88}, {"index": 30, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/GenerateInvoiceListener.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\GenerateInvoiceListener.php", "line": 16}], "start": **********.5038738, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 21.562, "width_percent": 0.344}, {"sql": "insert into `ec_invoice_items` (`reference_id`, `reference_type`, `name`, `description`, `image`, `qty`, `price`, `sub_total`, `tax_amount`, `discount_amount`, `amount`, `options`, `invoice_id`, `updated_at`, `created_at`) values (28, 'Botble\\\\Ecommerce\\\\Models\\\\Product', 'Beat Headphone', null, 'products/3.jpg', 7, '20.00', 140, '14.00', 0, 154, '{\\\"image\\\":\\\"products\\\\/3.jpg\\\",\\\"attributes\\\":\\\"(Color: Green, Size: L)\\\",\\\"taxRate\\\":10,\\\"taxClasses\\\":{\\\"VAT\\\":10},\\\"options\\\":[],\\\"extras\\\":[],\\\"sku\\\":\\\"SW-154-A0\\\",\\\"weight\\\":629}', 40, '2025-08-15 15:02:44', '2025-08-15 15:02:44')", "type": "query", "params": [], "bindings": [28, "Botble\\Ecommerce\\Models\\Product", "Beat Headphone", null, "products/3.jpg", 7, "20.00", 140, "14.00", 0, 154, "{\"image\":\"products\\/3.jpg\",\"attributes\":\"(Color: Green, Size: L)\",\"taxRate\":10,\"taxClasses\":{\"VAT\":10},\"options\":[],\"extras\":[],\"sku\":\"SW-154-A0\",\"weight\":629}", 40, "2025-08-15 15:02:44", "2025-08-15 15:02:44"], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 89}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/GenerateInvoiceListener.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\GenerateInvoiceListener.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}], "start": **********.510521, "duration": 0.0050999999999999995, "duration_str": "5.1ms", "memory": 0, "memory_str": null, "filename": "InvoiceHelper.php:89", "source": {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 89}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FInvoiceHelper.php&line=89", "ajax": false, "filename": "InvoiceHelper.php", "line": "89"}, "connection": "martfury", "explain": null, "start_percent": 21.905, "width_percent": 3.075}, {"sql": "select * from `payments` where `payments`.`id` = 85 limit 1", "type": "query", "params": [], "bindings": [85], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 191}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 125}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 146}], "start": **********.6300719, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 24.98, "width_percent": 0.326}, {"sql": "select * from `ec_invoice_items` where `ec_invoice_items`.`invoice_id` in (40)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 232}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 125}, {"index": 26, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 146}, {"index": 28, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 959}], "start": **********.632577, "duration": 0.00394, "duration_str": "3.94ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 25.306, "width_percent": 2.376}, {"sql": "select * from `ec_orders` where `ec_orders`.`id` in (48)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 232}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 125}, {"index": 26, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 146}, {"index": 28, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 959}], "start": **********.638823, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 27.682, "width_percent": 0.531}, {"sql": "select * from `languages` where `lang_is_default` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 940}, {"index": 19, "namespace": null, "name": "platform/plugins/language/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\language\\src\\Providers\\HookServiceProvider.php", "line": 89}, {"index": 23, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 25, "namespace": null, "name": "vendor/botble/platform/base/src/Supports/Language.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Supports\\Language.php", "line": 497}], "start": **********.665106, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 28.212, "width_percent": 0.639}, {"sql": "select `lang_locale`, `lang_code`, `lang_name`, `lang_flag`, `lang_is_rtl` from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\language\\src\\Providers\\HookServiceProvider.php", "line": 118}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "vendor/botble/platform/base/src/Supports/Language.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Supports\\Language.php", "line": 499}], "start": **********.6704972, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 28.851, "width_percent": 0.265}, {"sql": "select * from `ec_order_addresses` where `ec_order_addresses`.`order_id` = 48 and `ec_order_addresses`.`order_id` is not null and `type` = 'shipping_address' limit 1", "type": "query", "params": [], "bindings": [48, "shipping_address"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 345}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 125}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 146}], "start": **********.6743448, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 29.117, "width_percent": 0.259}, {"sql": "select * from `mp_stores` where `mp_stores`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\HookServiceProvider.php", "line": 253}, {"index": 27, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 29, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 125}], "start": **********.678479, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 29.376, "width_percent": 0.525}, {"sql": "select * from `ec_currencies` order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/CurrencySupport.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\CurrencySupport.php", "line": 107}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/CurrencySupport.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\CurrencySupport.php", "line": 41}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/helpers/currencies.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\helpers\\currencies.php", "line": 141}, {"index": 21, "namespace": null, "name": "f1d4fe725dee349e7b0bf732bc8c5346ff5bf0bb", "file": "f1d4fe725dee349e7b0bf732bc8c5346ff5bf0bb", "line": 150}], "start": 1755270166.9074461, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 29.901, "width_percent": 0.295}, {"sql": "select * from `ec_order_addresses` where `ec_order_addresses`.`order_id` = 48 and `ec_order_addresses`.`order_id` is not null and `type` = 'shipping_address' limit 1", "type": "query", "params": [], "bindings": [48, "shipping_address"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/OrderCreatedNotification.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\OrderCreatedNotification.php", "line": 18}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 508}, {"index": 30, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 321}], "start": **********.255744, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 30.196, "width_percent": 0.326}, {"sql": "select * from `ec_order_product` where `ec_order_product`.`order_id` = 48 and `ec_order_product`.`order_id` is not null", "type": "query", "params": [], "bindings": [48], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/OrderCreatedNotification.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\OrderCreatedNotification.php", "line": 19}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 508}, {"index": 29, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 321}], "start": **********.2592402, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 30.522, "width_percent": 0.38}, {"sql": "select * from `ec_products` where `ec_products`.`id` in (28) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 27, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 28, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/OrderCreatedNotification.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\OrderCreatedNotification.php", "line": 19}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 508}], "start": **********.261817, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 30.901, "width_percent": 0.344}, {"sql": "insert into `admin_notifications` (`title`, `action_label`, `action_url`, `description`, `permission`, `updated_at`, `created_at`) values ('New order #10000047', 'View', '/admin/ecommerce/orders/edit/48', '<PERSON><PERSON><PERSON><PERSON> ordered 1 product(s)', '', '2025-08-15 15:02:49', '2025-08-15 15:02:49')", "type": "query", "params": [], "bindings": ["New order #10000047", "View", "/admin/ecommerce/orders/edit/48", "<PERSON><PERSON><PERSON><PERSON> ordered 1 product(s)", "", "2025-08-15 15:02:49", "2025-08-15 15:02:49"], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "vendor/botble/platform/base/src/Listeners/AdminNotificationListener.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Listeners\\AdminNotificationListener.php", "line": 22}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 508}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 508}, {"index": 30, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 321}, {"index": 34, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}], "start": **********.2968462, "duration": 0.0055899999999999995, "duration_str": "5.59ms", "memory": 0, "memory_str": null, "filename": "AdminNotificationListener.php:22", "source": {"index": 18, "namespace": null, "name": "vendor/botble/platform/base/src/Listeners/AdminNotificationListener.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Listeners\\AdminNotificationListener.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FListeners%2FAdminNotificationListener.php&line=22", "ajax": false, "filename": "AdminNotificationListener.php", "line": "22"}, "connection": "martfury", "explain": null, "start_percent": 31.245, "width_percent": 3.371}, {"sql": "select * from `ec_customers` where `ec_customers`.`id` = 0 limit 1", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/AbandonedCartService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\AbandonedCartService.php", "line": 58}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/MarkCartAsRecovered.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\MarkCartAsRecovered.php", "line": 17}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 508}], "start": **********.3051581, "duration": 0.00437, "duration_str": "4.37ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 34.616, "width_percent": 2.635}, {"sql": "select * from `ec_abandoned_carts` where `is_recovered` = 0 and (`customer_id` is null) order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/AbandonedCartService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\AbandonedCartService.php", "line": 71}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/MarkCartAsRecovered.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\MarkCartAsRecovered.php", "line": 17}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 508}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 321}], "start": **********.311924, "duration": 0.0024500000000000004, "duration_str": "2.45ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 37.251, "width_percent": 1.477}, {"sql": "update `ec_orders` set `is_finished` = 1, `ec_orders`.`updated_at` = '2025-08-15 15:02:49' where `id` = 48", "type": "query", "params": [], "bindings": [1, "2025-08-15 15:02:49", 48], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 114}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 321}, {"index": 20, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 22, "namespace": null, "name": "platform/plugins/hyperpay/src/Http/Controllers/HyperPayController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\hyperpay\\src\\Http\\Controllers\\HyperPayController.php", "line": 76}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.316948, "duration": 0.00401, "duration_str": "4.01ms", "memory": 0, "memory_str": null, "filename": "OrderHelper.php:114", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 114}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FOrderHelper.php&line=114", "ajax": false, "filename": "OrderHelper.php", "line": "114"}, "connection": "martfury", "explain": null, "start_percent": 38.728, "width_percent": 2.418}, {"sql": "select * from `ec_products` where `ec_products`.`id` = 28 and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published')) limit 1", "type": "query", "params": [], "bindings": [28, 1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 209}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 116}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 321}, {"index": 25, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}], "start": **********.3259401, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 41.146, "width_percent": 0.482}, {"sql": "update `ec_products` set `quantity` = 4, `ec_products`.`updated_at` = '2025-08-15 15:02:49' where `id` = 28", "type": "query", "params": [], "bindings": [4, "2025-08-15 15:02:49", 28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 218}, {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 116}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 321}, {"index": 21, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 23, "namespace": null, "name": "platform/plugins/hyperpay/src/Http/Controllers/HyperPayController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\hyperpay\\src\\Http\\Controllers\\HyperPayController.php", "line": 76}], "start": **********.3306031, "duration": 0.00433, "duration_str": "4.33ms", "memory": 0, "memory_str": null, "filename": "OrderHelper.php:218", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 218}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FOrderHelper.php&line=218", "ajax": false, "filename": "OrderHelper.php", "line": "218"}, "connection": "martfury", "explain": null, "start_percent": 41.628, "width_percent": 2.611}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`product_id` = 28 and `ec_product_variations`.`product_id` is not null limit 1", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 430}, {"index": 30, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 149}], "start": **********.33868, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 44.239, "width_percent": 0.283}, {"sql": "select * from `ec_products` where `ec_products`.`id` = 3 and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published')) limit 1", "type": "query", "params": [], "bindings": [3, 1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 430}, {"index": 30, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 149}], "start": **********.341972, "duration": 0.00415, "duration_str": "4.15ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 44.522, "width_percent": 2.502}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 3 and `ec_product_variations`.`configurable_product_id` is not null and `ec_product_variations`.`is_default` = 1 limit 1", "type": "query", "params": [], "bindings": [3, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 149}, {"index": 30, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 218}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 116}], "start": **********.347484, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 47.024, "width_percent": 0.5}, {"sql": "update `ec_products` set `quantity` = 4, `stock_status` = 'in_stock', `ec_products`.`updated_at` = '2025-08-15 15:02:49' where `id` = 3", "type": "query", "params": [], "bindings": [4, {"value": "in_stock", "label": "In stock"}, "2025-08-15 15:02:49", 3], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/UpdateDefaultProductService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\UpdateDefaultProductService.php", "line": 37}, {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 150}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 218}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 116}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 321}], "start": **********.366727, "duration": 0.00418, "duration_str": "4.18ms", "memory": 0, "memory_str": null, "filename": "UpdateDefaultProductService.php:37", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/UpdateDefaultProductService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\UpdateDefaultProductService.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FProducts%2FUpdateDefaultProductService.php&line=37", "ajax": false, "filename": "UpdateDefaultProductService.php", "line": "37"}, "connection": "martfury", "explain": null, "start_percent": 47.525, "width_percent": 2.52}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 3 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 21}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 508}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 156}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/UpdateDefaultProductService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\UpdateDefaultProductService.php", "line": 37}, {"index": 28, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 150}], "start": **********.376321, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "UpdateProductStockStatus.php:21", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FListeners%2FUpdateProductStockStatus.php&line=21", "ajax": false, "filename": "UpdateProductStockStatus.php", "line": "21"}, "connection": "martfury", "explain": null, "start_percent": 50.045, "width_percent": 0.32}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 3 and `ec_product_variations`.`configurable_product_id` is not null", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 39}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 22}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 508}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 156}], "start": **********.377717, "duration": 0.0036, "duration_str": "3.6ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 50.365, "width_percent": 2.171}, {"sql": "select * from `ec_products` where `ec_products`.`id` in (28, 29) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 39}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 22}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 508}], "start": **********.383054, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 52.535, "width_percent": 0.675}, {"sql": "update `ec_products` set `quantity` = 20, `stock_status` = 'in_stock', `ec_products`.`updated_at` = '2025-08-15 15:02:49' where `id` = 3", "type": "query", "params": [], "bindings": [20, {"value": "in_stock", "label": "In stock"}, "2025-08-15 15:02:49", 3], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 85}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 22}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 508}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 156}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/UpdateDefaultProductService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\UpdateDefaultProductService.php", "line": 37}], "start": **********.395919, "duration": 0.00439, "duration_str": "4.39ms", "memory": 0, "memory_str": null, "filename": "UpdateProductStockStatus.php:85", "source": {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 85}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FListeners%2FUpdateProductStockStatus.php&line=85", "ajax": false, "filename": "UpdateProductStockStatus.php", "line": "85"}, "connection": "martfury", "explain": null, "start_percent": 53.211, "width_percent": 2.647}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 3 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 159}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/UpdateDefaultProductService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\UpdateDefaultProductService.php", "line": 37}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 150}, {"index": 29, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 218}, {"index": 30, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 116}], "start": **********.4053, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Product.php:159", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 159}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=159", "ajax": false, "filename": "Product.php", "line": "159"}, "connection": "martfury", "explain": null, "start_percent": 55.858, "width_percent": 0.259}, {"sql": "select `product_id` from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 3 and `ec_product_variations`.`configurable_product_id` is not null", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 161}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/UpdateDefaultProductService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\UpdateDefaultProductService.php", "line": 37}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 150}, {"index": 32, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 218}, {"index": 33, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 116}], "start": **********.406667, "duration": 0.00361, "duration_str": "3.61ms", "memory": 0, "memory_str": null, "filename": "Product.php:161", "source": {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 161}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=161", "ajax": false, "filename": "Product.php", "line": "161"}, "connection": "martfury", "explain": null, "start_percent": 56.117, "width_percent": 2.177}, {"sql": "update `ec_products` set `name` = 'Beat Headphone', `minimum_order_quantity` = 0, `maximum_order_quantity` = 0, `ec_products`.`updated_at` = '2025-08-15 15:02:49' where `id` in (28, 29) and `is_variation` = 1 and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": ["Beat Headphone", 0, 0, "2025-08-15 15:02:49", 28, 29, 1, 1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 163}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/UpdateDefaultProductService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\UpdateDefaultProductService.php", "line": 37}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 150}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 218}, {"index": 28, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 116}], "start": **********.413035, "duration": 0.0046, "duration_str": "4.6ms", "memory": 0, "memory_str": null, "filename": "Product.php:163", "source": {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 163}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=163", "ajax": false, "filename": "Product.php", "line": "163"}, "connection": "martfury", "explain": null, "start_percent": 58.294, "width_percent": 2.774}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 3 and `ec_product_variations`.`configurable_product_id` is not null", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 39}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 34}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 17}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 508}], "start": **********.465228, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 61.067, "width_percent": 0.217}, {"sql": "select * from `ec_products` where `ec_products`.`id` in (28, 29) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 39}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 34}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 17}], "start": **********.46734, "duration": 0.00392, "duration_str": "3.92ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 61.284, "width_percent": 2.364}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 3 and `ec_product_variations`.`configurable_product_id` is not null", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 39}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 34}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 17}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 508}], "start": **********.482054, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 63.648, "width_percent": 0.308}, {"sql": "select * from `ec_products` where `ec_products`.`id` in (28, 29) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 39}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 34}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 17}], "start": **********.484641, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 63.955, "width_percent": 0.32}, {"sql": "select * from `mp_stores` where `mp_stores`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 709}, {"index": 24, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 737}, {"index": 25, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 693}], "start": **********.493268, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 64.275, "width_percent": 0.277}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_type` = 'Botble\\\\Marketplace\\\\Models\\\\Store' and `slugs`.`reference_id` = 1 and `slugs`.`reference_id` is not null limit 1", "type": "query", "params": [], "bindings": ["Botble\\Marketplace\\Models\\Store", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "vendor/botble/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 118}, {"index": 29, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 25}, {"index": 30, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 712}], "start": **********.497801, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 64.552, "width_percent": 0.217}, {"sql": "select * from `ec_order_addresses` where `ec_order_addresses`.`order_id` = 48 and `ec_order_addresses`.`order_id` is not null and `type` = 'shipping_address' limit 1", "type": "query", "params": [], "bindings": [48, "shipping_address"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 716}, {"index": 24, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 737}, {"index": 25, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 693}], "start": **********.505166, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 64.769, "width_percent": 0.229}, {"sql": "select * from `ec_shipping_rules` where `ec_shipping_rules`.`id` = '3' limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 474}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Order.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Order.php", "line": 94}, {"index": 27, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 28, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 722}], "start": **********.527637, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 64.998, "width_percent": 0.513}, {"sql": "select * from `payments` where `payments`.`id` = 85 limit 1", "type": "query", "params": [], "bindings": [85], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 723}, {"index": 24, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 737}, {"index": 25, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 693}], "start": **********.530298, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 65.511, "width_percent": 0.169}, {"sql": "insert into `ec_order_histories` (`action`, `description`, `order_id`, `updated_at`, `created_at`) values ('send_order_confirmation_email', 'The email confirmation was sent to customer', 48, '2025-08-15 15:02:49', '2025-08-15 15:02:49')", "type": "query", "params": [], "bindings": [{"value": "send_order_confirmation_email", "label": "send_order_confirmation_email"}, "The email confirmation was sent to customer", 48, "2025-08-15 15:02:49", "2025-08-15 15:02:49"], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 742}, {"index": 19, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 693}, {"index": 23, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 26, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 321}, {"index": 30, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}], "start": **********.534249, "duration": 0.004070000000000001, "duration_str": "4.07ms", "memory": 0, "memory_str": null, "filename": "OrderSupportServiceProvider.php:742", "source": {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 742}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FProviders%2FOrderSupportServiceProvider.php&line=742", "ajax": false, "filename": "OrderSupportServiceProvider.php", "line": "742"}, "connection": "martfury", "explain": null, "start_percent": 65.68, "width_percent": 2.454}, {"sql": "select * from `ec_shipping_rules` where `ec_shipping_rules`.`id` = '3' limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 474}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Order.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Order.php", "line": 94}, {"index": 27, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 28, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 281}], "start": **********.546419, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 68.134, "width_percent": 0.229}, {"sql": "select * from `ec_shipments` where `ec_shipments`.`order_id` = 48 and `ec_shipments`.`order_id` is not null limit 1", "type": "query", "params": [], "bindings": [48], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "plugins/ecommerce::emails.partials.order-delivery-notes", "file": "D:\\laragon\\www\\martfury\\platform/plugins/ecommerce/resources/views/emails/partials/order-delivery-notes.blade.php", "line": 1}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}], "start": **********.549666, "duration": 0.00359, "duration_str": "3.59ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 68.363, "width_percent": 2.165}, {"sql": "select * from `ec_flash_sales` where date(`end_date`) >= '2025-08-15' and `status` = 'published' order by `created_at` desc", "type": "query", "params": [], "bindings": ["2025-08-15", "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/FlashSaleRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\FlashSaleRepository.php", "line": 35}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/FlashSaleSupport.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\FlashSaleSupport.php", "line": 52}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/FlashSaleSupport.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\FlashSaleSupport.php", "line": 22}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Concerns/ProductPrices.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Concerns\\ProductPrices.php", "line": 46}], "start": **********.567842, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 70.528, "width_percent": 0.247}, {"sql": "select * from `ec_customers` where `id` = '2' limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 74}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 216}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 187}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}], "start": **********.57161, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 70.775, "width_percent": 0.199}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`product_id` = 28 and `ec_product_variations`.`product_id` is not null limit 1", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 430}, {"index": 30, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Concerns/ProductPrices.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Concerns\\ProductPrices.php", "line": 58}], "start": **********.57459, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 70.974, "width_percent": 0.223}, {"sql": "select * from `ec_products` where `ec_products`.`id` = 3 and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published')) limit 1", "type": "query", "params": [], "bindings": [3, 1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 430}, {"index": 30, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Concerns/ProductPrices.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Concerns\\ProductPrices.php", "line": 58}], "start": **********.576905, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 71.197, "width_percent": 0.675}, {"sql": "select * from `ec_discounts` where `type` = 'promotion' and `start_date` <= '2025-08-15 15:02:49' and (`end_date` is null or `end_date` >= '2025-08-15 15:02:49') and (`target` in ('all-orders', 'amount-minimum-order') or (`target` in ('customer', 'group-products', 'products-by-category', 'specific-product', 'product-variant') and `product_quantity` = 1))", "type": "query", "params": [], "bindings": ["promotion", "2025-08-15 15:02:49", "2025-08-15 15:02:49", "all-orders", "amount-minimum-order", "customer", "group-products", "products-by-category", "specific-product", "product-variant", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/DiscountRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\DiscountRepository.php", "line": 50}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/DiscountSupport.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\DiscountSupport.php", "line": 116}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/DiscountSupport.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\DiscountSupport.php", "line": 42}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Concerns/ProductPrices.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Concerns\\ProductPrices.php", "line": 58}], "start": **********.581333, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 71.872, "width_percent": 0.452}, {"sql": "insert into `email_logs` (`from`, `to`, `cc`, `bcc`, `subject`, `html_body`, `text_body`, `raw_body`, `debug_info`, `updated_at`, `created_at`) values ('\\\"Example\\\" <<EMAIL>>', 'g<PERSON><PERSON><PERSON>@example.org', '', '', 'New order(s) at MartFury', '<!doctype html>\\n<html lang=\\\"en\\\">\\n\\n<head>\\n    <meta charset=\\\"UTF-8\\\">\\n    <title>MartFury</title>\\n</head>\\n\\n<body class=\\\"bb-bg-body\\\" dir=\\\"ltr\\\" style=\\\"margin: 0; padding: 0; font-size: 14px; line-height: 171.4285714286%; mso-line-height-rule: exactly; color: #3A4859; width: 100%; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale; -webkit-font-feature-settings: \\'cv02\\', \\'cv03\\', \\'cv04\\', \\'cv11\\'; font-feature-settings: \\'cv02\\', \\'cv03\\', \\'cv04\\', \\'cv11\\'; font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; background-color: #f6f7f9;\\\">\\n\\n<center>\\n    <table class=\\\"bb-main bb-bg-body\\\" width=\\\"100%\\\" cellspacing=\\\"0\\\" cellpadding=\\\"0\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; background-color: #f6f7f9;\\\">\\n        <tbody>\\n            <tr>\\n                <td align=\\\"center\\\" valign=\\\"top\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;\\\">\\n                    <table class=\\\"bb-wrap\\\" cellspacing=\\\"0\\\" cellpadding=\\\"0\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; max-width: 640px; text-align: left; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\\\">\\n                        <tbody>\\n                            <tr>\\n                                <td class=\\\"bb-p-sm\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 8px;\\\">\\n                                    <table cellpadding=\\\"0\\\" cellspacing=\\\"0\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\\\">\\n                                        <tbody>\\n                                            <tr>\\n                                                <td class=\\\"bb-py-lg\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-top: 24px; padding-bottom: 24px;\\\">\\n                                                    <table cellspacing=\\\"0\\\" cellpadding=\\\"0\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\\\">\\n                                                        <tbody>\\n                                                            <tr>\\n                                                                <td class=\\\"bb-text-left\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: left;\\\">\\n                                                                    <a href=\\\"https://martfury.gc\\\" style=\\\"color: #206bc4; text-decoration: none;\\\">\\n                                                                        <img class=\\\"bb-logo\\\" src=\\\"https://martfury.gc/storage/general/logo.png\\\" alt=\\\"MartFury\\\" style=\\\"line-height: 100%; outline: none; text-decoration: none; vertical-align: baseline; font-size: 0; border: 0 none; max-height: 40px;\\\">\\n                                                                    </a>\\n                                                                </td>\\n                                                                <td class=\\\"bb-text-right\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right;\\\">\\n                                                                    2025-08-15 15:02:49\\n                                                                </td>\\n                                                            </tr>\\n                                                        </tbody>\\n                                                    </table>\\n                                                </td>\\n                                            </tr>\\n                                        </tbody>\\n                                    </table>\\n\\n\\n<div class=\\\"bb-main-content\\\">\\n    <table class=\\\"bb-box\\\" cellpadding=\\\"0\\\" cellspacing=\\\"0\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; background: #ffffff; border-radius: 4px; -webkit-box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05); box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05); border: 1px solid #dce0e5; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\\\">\\n        <tbody>\\n            <tr>\\n                <td class=\\\"bb-content bb-pb-0\\\" align=\\\"center\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; padding-bottom: 0;\\\">\\n                    <table class=\\\"bb-icon bb-icon-lg bb-bg-blue\\\" cellspacing=\\\"0\\\" cellpadding=\\\"0\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 0; border-radius: 50%; background: #edeef0; line-height: 100%; font-weight: 300; border-collapse: separate; text-align: center; width: 72px; height: 72px; font-size: 48px; background-color: #206bc4; color: #ffffff; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\\\">\\n                        <tbody>\\n                            <tr>\\n                                <td valign=\\\"middle\\\" align=\\\"center\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;\\\">\\n                                    <img src=\\\"https://martfury.gc/vendor/core/core/base/images/email-icons/shopping-cart.png\\\" class=\\\"bb-va-middle\\\" width=\\\"40\\\" height=\\\"40\\\" alt=\\\"Icon\\\" style=\\\"border: 0 none; line-height: 100%; outline: none; text-decoration: none; font-size: 0; vertical-align: middle; display: block; width: 40px; height: 40px;\\\">\\n                                </td>\\n                            </tr>\\n                        </tbody>\\n                    </table>\\n                    <h1 class=\\\"bb-text-center bb-m-0 bb-mt-md\\\" style=\\\"font-weight: 600; color: #232b42; font-size: 28px; line-height: 130%; text-align: center; margin: 0; margin-top: 16px;\\\">Order successfully!</h1>\\n                </td>\\n            </tr>\\n            <tr>\\n                <td class=\\\"bb-content\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px;\\\">\\n                    <div>Dear GoPro,</div>\\n                    <div>You got a new order on MartFury!</div>\\n                </td>\\n            </tr>\\n            <tr>\\n                <td class=\\\"bb-content bb-pt-0\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; padding-top: 0;\\\">\\n                    <table class=\\\"bb-row bb-mb-md\\\" cellpadding=\\\"0\\\" cellspacing=\\\"0\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; table-layout: fixed; margin-bottom: 16px; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\\\">\\n                        <tbody>\\n                            <tr>\\n                                <td class=\\\"bb-bb-col\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;\\\">\\n                                    <h4 class=\\\"bb-m-0\\\" style=\\\"font-weight: 600; color: #232b42; font-size: 16px; margin: 0;\\\">Customer Information</h4>\\n                                    <div>Name: <strong style=\\\"font-weight: 600;\\\">Ishtiaq Ahmed</strong>\\n</div>\\n                                                                        <div>Phone: <strong style=\\\"font-weight: 600;\\\">+923147552550</strong>\\n</div>\\n                                                                                                                                                <div>Address: <strong style=\\\"font-weight: 600;\\\">test, test, Test, PK</strong>\\n</div>\\n                                                                    </td>\\n                            </tr>\\n                        </tbody>\\n                    </table>\\n                </td>\\n            </tr>\\n            <tr>\\n                <td class=\\\"bb-content bb-pt-0\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; padding-top: 0;\\\">\\n                    <h4 style=\\\"font-weight: 600; margin: 0 0 0.5em; color: #232b42; font-size: 16px;\\\">Here\\'s what you ordered:</h4>\\n                    <a class=\\\"button button-blue\\\" href=\\\"https://martfury.gc/orders/tracking?order_id=%2310000047&amp;email=rajaishtiaq6%40gmail.com\\\" style=\\\"color: #206bc4; text-decoration: none;\\\">View order</a>\\n    or <a href=\\\"https://martfury.gc\\\" style=\\\"color: #206bc4; text-decoration: none;\\\">Go to our shop</a>\\n\\n    <br>\\n\\n<table class=\\\"bb-table\\\" cellspacing=\\\"0\\\" cellpadding=\\\"0\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\\\">\\n    <thead>\\n        <tr>\\n            <th colspan=\\\"2\\\" style=\\\"text-transform: uppercase; font-weight: 600; color: #667382; font-size: 12px; padding: 0 0 4px 0; padding-left: 0;\\\"></th>\\n            <th style=\\\"text-transform: uppercase; font-weight: 600; color: #667382; font-size: 12px; padding: 0 0 4px 0;\\\">Quantity</th>\\n            <th class=\\\"bb-text-right\\\" style=\\\"text-align: right; text-transform: uppercase; font-weight: 600; color: #667382; font-size: 12px; padding: 0 0 4px 0; padding-right: 0;\\\">Price</th>\\n        </tr>\\n    </thead>\\n\\n    <tbody>\\n                <tr>\\n            <td class=\\\"bb-pr-0\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-right: 0; padding: 4px 0; padding-left: 0;\\\">\\n                <a href=\\\"\\\" style=\\\"color: #206bc4; text-decoration: none;\\\">\\n                    <img src=\\\"https://martfury.gc/storage/products/3-150x150.jpg\\\" class=\\\" bb-rounded\\\" width=\\\"64\\\" height=\\\"64\\\" alt=\\\"\\\" style=\\\"line-height: 100%; outline: none; text-decoration: none; vertical-align: baseline; font-size: 0; border: 0 none; border-radius: 4px;\\\">\\n                </a>\\n            </td>\\n            <td class=\\\"bb-pl-md bb-w-100p\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; width: 100%; padding-left: 16px !important; padding: 4px 0;\\\">\\n                <strong style=\\\"font-weight: 600;\\\">Beat Headphone</strong><br>\\n                                    <span class=\\\"bb-text-muted\\\" style=\\\"color: #667382;\\\">(Color: Green, Size: L)</span>\\n                \\n                            </td>\\n            <td class=\\\"bb-text-center\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: center; padding: 4px 0;\\\">x 7</td>\\n            <td class=\\\"bb-text-right\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; padding: 4px 0; padding-right: 0;\\\">SAR20.00</td>\\n        </tr>\\n    \\n                                    <tr>\\n                    <td colspan=\\\"2\\\" class=\\\"bb-border-top bb-text-right\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; border-top: 1px solid #dce0e5; padding: 4px 0; padding-left: 0;\\\">Subtotal</td>\\n                    <td colspan=\\\"2\\\" class=\\\"bb-border-top bb-text-right\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; border-top: 1px solid #dce0e5; padding: 4px 0; padding-right: 0;\\\">SAR140.00</td>\\n                </tr>\\n            \\n            \\n                            <tr>\\n                    <td colspan=\\\"2\\\" class=\\\"bb-text-right\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; padding: 4px 0; padding-left: 0;\\\">Tax</td>\\n                    <td colspan=\\\"2\\\" class=\\\"bb-text-right\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; padding: 4px 0; padding-right: 0;\\\">SAR14.00</td>\\n                </tr>\\n            \\n            \\n                        <tr>\\n                <td colspan=\\\"2\\\" class=\\\"bb-text-right bb-font-strong bb-h3 bb-m-0\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; color: #232b42; font-size: 20px; line-height: 130%; text-align: right; font-weight: 600; margin: 0; padding: 4px 0; padding-left: 0;\\\">Total</td>\\n                <td colspan=\\\"2\\\" class=\\\"bb-font-strong bb-h3 bb-m-0 bb-text-right\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; color: #232b42; font-size: 20px; line-height: 130%; text-align: right; font-weight: 600; margin: 0; padding: 4px 0; padding-right: 0;\\\">SAR154.00</td>\\n            </tr>\\n            </tbody>\\n</table>\\n\\n\\n                                    </td>\\n            </tr>\\n            <tr>\\n                <td class=\\\"bb-content bb-border-top\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; border-top: 1px solid #dce0e5;\\\">\\n                    <table class=\\\"bb-row bb-mb-md\\\" cellpadding=\\\"0\\\" cellspacing=\\\"0\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; table-layout: fixed; margin-bottom: 16px; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\\\">\\n                        <tbody>\\n                            <tr>\\n                                <td class=\\\"bb-bb-col\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;\\\">\\n                                    <h4 class=\\\"bb-m-0\\\" style=\\\"font-weight: 600; color: #232b42; font-size: 16px; margin: 0;\\\">Order number</h4>\\n                                    <div>#10000047</div>\\n                                </td>\\n                                <td class=\\\"bb-col-spacer\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top; width: 24px;\\\"></td>\\n                                <td class=\\\"bb-col\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top;\\\">\\n                                    <h4 class=\\\"bb-mb-0\\\" style=\\\"font-weight: 600; margin: 0 0 0.5em; color: #232b42; font-size: 16px; margin-bottom: 0;\\\">Order date</h4>\\n                                    <div>2025-08-15 15:00:08</div>\\n                                </td>\\n                            </tr>\\n                        </tbody>\\n                    </table>\\n                    <table class=\\\"bb-row\\\" cellpadding=\\\"0\\\" cellspacing=\\\"0\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; table-layout: fixed; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\\\">\\n                        <tbody>\\n                            <tr>\\n                                <td class=\\\"bb-col\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top;\\\">\\n                                                                        <h4 class=\\\"bb-m-0\\\" style=\\\"font-weight: 600; color: #232b42; font-size: 16px; margin: 0;\\\">Shipping Method</h4>\\n                                    <div>\\n                                        Local Pickup\\n                                    </div>\\n                                                                    </td>\\n\\n                                <td class=\\\"bb-col-spacer\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top; width: 24px;\\\"></td>\\n                                <td class=\\\"bb-col\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top;\\\">\\n                                    <h4 class=\\\"bb-m-0\\\" style=\\\"font-weight: 600; color: #232b42; font-size: 16px; margin: 0;\\\">Payment Method</h4>\\n                                    <div>\\n                                        HyperPay\\n                                    </div>\\n                                </td>\\n                            </tr>\\n                        </tbody>\\n                    </table>\\n                </td>\\n            </tr>\\n        </tbody>\\n    </table>\\n</div>\\n\\n<table cellspacing=\\\"0\\\" cellpadding=\\\"0\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\\\">\\n    <tbody>\\n        <tr>\\n            <td class=\\\"bb-py-xl\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-top: 48px; padding-bottom: 48px;\\\">\\n                <table class=\\\"bb-text-center bb-text-muted\\\" cellspacing=\\\"0\\\" cellpadding=\\\"0\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; color: #667382; text-align: center; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\\\">\\n                    <tbody>\\n                    \\n                    <tr>\\n                        <td class=\\\"bb-px-lg\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-right: 24px; padding-left: 24px;\\\">\\n                            © 2025 MartFury. All Rights Reserved.\\n                        </td>\\n                    </tr>\\n\\n                                            <tr>\\n                            <td class=\\\"bb-pt-md\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-top: 16px;\\\">\\n                                If you have any questions, feel free to message us at <a href=\\\"mailto:<EMAIL>\\\" style=\\\"color: #206bc4; text-decoration: none;\\\"><EMAIL></a>.\\n                            </td>\\n                        </tr>\\n                                        </tbody>\\n                </table>\\n            </td>\\n        </tr>\\n    </tbody>\\n</table>\\n</td>\\n</tr>\\n</tbody>\\n</table>\\n</td>\\n</tr>\\n</tbody>\\n</table>\\n</center>\\n</body>\\n\\n</html>', null, 'From: Example <<EMAIL>>\\r\\nTo: <EMAIL>\\r\\nSubject: New order(s) at MartFury\\r\\nMessage-ID: <<EMAIL>>\\r\\nMIME-Version: 1.0\\r\\nDate: Fri, 15 Aug 2025 15:02:52 +0000\\r\\nContent-Type: text/html; charset=utf-8\\r\\nContent-Transfer-Encoding: quoted-printable\\r\\n\\r\\n<!doctype html>\\r\\n<html lang=3D\\\"en\\\">\\r\\n\\r\\n<head>\\r\\n    <meta charset=3D\\\"UTF=\\r\\n-8\\\">\\r\\n    <title>MartFury</title>\\r\\n</head>\\r\\n\\r\\n<body class=3D\\\"bb-bg-body=\\r\\n\\\" dir=3D\\\"ltr\\\" style=3D\\\"margin: 0; padding: 0; font-size: 14px; line-height:=\\r\\n 171.4285714286%; mso-line-height-rule: exactly; color: #3A4859; width: 100=\\r\\n%; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale;=\\r\\n -webkit-font-feature-settings: \\'cv02\\', \\'cv03\\', \\'cv04\\', \\'cv11\\'; font-featur=\\r\\ne-settings: \\'cv02\\', \\'cv03\\', \\'cv04\\', \\'cv11\\'; font-family: Inter, -apple-syst=\\r\\nem, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sa=\\r\\nns-serif; background-color: #f6f7f9;\\\">\\r\\n\\r\\n<center>\\r\\n    <table class=3D\\\"=\\r\\nbb-main bb-bg-body\\\" width=3D\\\"100%\\\" cellspacing=3D\\\"0\\\" cellpadding=3D\\\"0\\\" styl=\\r\\ne=3D\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, =\\r\\nSegoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; wi=\\r\\ndth: 100%; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; back=\\r\\nground-color: #f6f7f9;\\\">\\r\\n        <tbody>\\r\\n            <tr>\\r\\n           =\\r\\n     <td align=3D\\\"center\\\" valign=3D\\\"top\\\" style=3D\\\"font-family: Inter, -appl=\\r\\ne-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Ne=\\r\\nue, sans-serif;\\\">\\r\\n                    <table class=3D\\\"bb-wrap\\\" cellspacin=\\r\\ng=3D\\\"0\\\" cellpadding=3D\\\"0\\\" style=3D\\\"font-family: Inter, -apple-system, Blink=\\r\\nMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;=\\r\\n border-collapse: collapse; width: 100%; max-width: 640px; text-align: left=\\r\\n; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\\\">\\r\\n               =\\r\\n         <tbody>\\r\\n                            <tr>\\r\\n                     =\\r\\n           <td class=3D\\\"bb-p-sm\\\" style=3D\\\"font-family: Inter, -apple-system=\\r\\n, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans=\\r\\n-serif; padding: 8px;\\\">\\r\\n                                    <table cellpa=\\r\\ndding=3D\\\"0\\\" cellspacing=3D\\\"0\\\" style=3D\\\"font-family: Inter, -apple-system, B=\\r\\nlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-se=\\r\\nrif; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -pr=\\r\\nemailer-cellspacing: 0;\\\">\\r\\n                                        <tbody>=\\r\\n\\r\\n                                            <tr>\\r\\n                     =\\r\\n                           <td class=3D\\\"bb-py-lg\\\" style=3D\\\"font-family: Int=\\r\\ner, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Hel=\\r\\nvetica Neue, sans-serif; padding-top: 24px; padding-bottom: 24px;\\\">\\r\\n     =\\r\\n                                               <table cellspacing=3D\\\"0\\\" cel=\\r\\nlpadding=3D\\\"0\\\" style=3D\\\"font-family: Inter, -apple-system, BlinkMacSystemFo=\\r\\nnt, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-col=\\r\\nlapse: collapse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspa=\\r\\ncing: 0;\\\">\\r\\n                                                        <tbody=\\r\\n>\\r\\n                                                            <tr>\\r\\n    =\\r\\n                                                            <td class=3D\\\"bb=\\r\\n-text-left\\\" style=3D\\\"font-family: Inter, -apple-system, BlinkMacSystemFont,=\\r\\n San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: l=\\r\\neft;\\\">\\r\\n                                                                  =\\r\\n  <a href=3D\\\"https://martfury.gc\\\" style=3D\\\"color: #206bc4; text-decoration:=\\r\\n none;\\\">\\r\\n                                                                =\\r\\n        <img class=3D\\\"bb-logo\\\" src=3D\\\"https://martfury.gc/storage/general/l=\\r\\nogo.png\\\" alt=3D\\\"MartFury\\\" style=3D\\\"line-height: 100%; outline: none; text-d=\\r\\necoration: none; vertical-align: baseline; font-size: 0; border: 0 none; ma=\\r\\nx-height: 40px;\\\">\\r\\n                                                       =\\r\\n             </a>\\r\\n                                                       =\\r\\n         </td>\\r\\n                                                          =\\r\\n      <td class=3D\\\"bb-text-right\\\" style=3D\\\"font-family: Inter, -apple-syste=\\r\\nm, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, san=\\r\\ns-serif; text-align: right;\\\">\\r\\n                                           =\\r\\n                         2025-08-15 15:02:49\\r\\n                            =\\r\\n                                    </td>\\r\\n                               =\\r\\n                             </tr>\\r\\n                                      =\\r\\n                  </tbody>\\r\\n                                              =\\r\\n      </table>\\r\\n                                                </td>\\r\\n  =\\r\\n                                          </tr>\\r\\n                         =\\r\\n               </tbody>\\r\\n                                    </table>\\r\\n=\\r\\n\\r\\n\\r\\n<div class=3D\\\"bb-main-content\\\">\\r\\n    <table class=3D\\\"bb-box\\\" cellpad=\\r\\nding=3D\\\"0\\\" cellspacing=3D\\\"0\\\" style=3D\\\"font-family: Inter, -apple-system, Bl=\\r\\ninkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-ser=\\r\\nif; border-collapse: collapse; width: 100%; background: #ffffff; border-rad=\\r\\nius: 4px; -webkit-box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05); box-shadow: 0 =\\r\\n1px 4px rgba(0, 0, 0, 0.05); border: 1px solid #dce0e5; -premailer-cellpadd=\\r\\ning: 0; -premailer-cellspacing: 0;\\\">\\r\\n        <tbody>\\r\\n            <tr>=\\r\\n\\r\\n                <td class=3D\\\"bb-content bb-pb-0\\\" align=3D\\\"center\\\" style=\\r\\n=3D\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, S=\\r\\negoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; padding-bo=\\r\\nttom: 0;\\\">\\r\\n                    <table class=3D\\\"bb-icon bb-icon-lg bb-bg-b=\\r\\nlue\\\" cellspacing=3D\\\"0\\\" cellpadding=3D\\\"0\\\" style=3D\\\"font-family: Inter, -appl=\\r\\ne-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Ne=\\r\\nue, sans-serif; padding: 0; border-radius: 50%; background: #edeef0; line-h=\\r\\neight: 100%; font-weight: 300; border-collapse: separate; text-align: cente=\\r\\nr; width: 72px; height: 72px; font-size: 48px; background-color: #206bc4; c=\\r\\nolor: #ffffff; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\\\">\\r\\n  =\\r\\n                      <tbody>\\r\\n                            <tr>\\r\\n        =\\r\\n                        <td valign=3D\\\"middle\\\" align=3D\\\"center\\\" style=3D\\\"fon=\\r\\nt-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI=\\r\\n, Roboto, Helvetica Neue, sans-serif;\\\">\\r\\n                                 =\\r\\n   <img src=3D\\\"https://martfury.gc/vendor/core/core/base/images/email-icons=\\r\\n/shopping-cart.png\\\" class=3D\\\"bb-va-middle\\\" width=3D\\\"40\\\" height=3D\\\"40\\\" alt=\\r\\n=3D\\\"Icon\\\" style=3D\\\"border: 0 none; line-height: 100%; outline: none; text-d=\\r\\necoration: none; font-size: 0; vertical-align: middle; display: block; widt=\\r\\nh: 40px; height: 40px;\\\">\\r\\n                                </td>\\r\\n        =\\r\\n                    </tr>\\r\\n                        </tbody>\\r\\n            =\\r\\n        </table>\\r\\n                    <h1 class=3D\\\"bb-text-center bb-m-0 b=\\r\\nb-mt-md\\\" style=3D\\\"font-weight: 600; color: #232b42; font-size: 28px; line-h=\\r\\neight: 130%; text-align: center; margin: 0; margin-top: 16px;\\\">Order succes=\\r\\nsfully!</h1>\\r\\n                </td>\\r\\n            </tr>\\r\\n            <tr>=\\r\\n\\r\\n                <td class=3D\\\"bb-content\\\" style=3D\\\"font-family: Inter, -a=\\r\\npple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica=\\r\\n Neue, sans-serif; padding: 40px 48px;\\\">\\r\\n                    <div>Dear Go=\\r\\nPro,</div>\\r\\n                    <div>You got a new order on MartFury!</div=\\r\\n>\\r\\n                </td>\\r\\n            </tr>\\r\\n            <tr>\\r\\n        =\\r\\n        <td class=3D\\\"bb-content bb-pt-0\\\" style=3D\\\"font-family: Inter, -appl=\\r\\ne-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Ne=\\r\\nue, sans-serif; padding: 40px 48px; padding-top: 0;\\\">\\r\\n                   =\\r\\n <table class=3D\\\"bb-row bb-mb-md\\\" cellpadding=3D\\\"0\\\" cellspacing=3D\\\"0\\\" style=\\r\\n=3D\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, S=\\r\\negoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; wid=\\r\\nth: 100%; table-layout: fixed; margin-bottom: 16px; -premailer-cellpadding:=\\r\\n 0; -premailer-cellspacing: 0;\\\">\\r\\n                        <tbody>\\r\\n      =\\r\\n                      <tr>\\r\\n                                <td class=3D\\\"b=\\r\\nb-bb-col\\\" style=3D\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, S=\\r\\nan Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;\\\">\\r\\n           =\\r\\n                         <h4 class=3D\\\"bb-m-0\\\" style=3D\\\"font-weight: 600; co=\\r\\nlor: #232b42; font-size: 16px; margin: 0;\\\">Customer Information</h4>\\r\\n    =\\r\\n                                <div>Name: <strong style=3D\\\"font-weight: 60=\\r\\n0;\\\">Ishtiaq Ahmed</strong>\\r\\n</div>\\r\\n                                     =\\r\\n                                   <div>Phone: <strong style=3D\\\"font-weight=\\r\\n: 600;\\\">+923147552550</strong>\\r\\n</div>\\r\\n                                 =\\r\\n                                                                           =\\r\\n                                    <div>Address: <strong style=3D\\\"font-wei=\\r\\nght: 600;\\\">test, test, Test, PK</strong>\\r\\n</div>\\r\\n                       =\\r\\n                                             </td>\\r\\n                      =\\r\\n      </tr>\\r\\n                        </tbody>\\r\\n                    </tabl=\\r\\ne>\\r\\n                </td>\\r\\n            </tr>\\r\\n            <tr>\\r\\n       =\\r\\n         <td class=3D\\\"bb-content bb-pt-0\\\" style=3D\\\"font-family: Inter, -app=\\r\\nle-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica N=\\r\\neue, sans-serif; padding: 40px 48px; padding-top: 0;\\\">\\r\\n                  =\\r\\n  <h4 style=3D\\\"font-weight: 600; margin: 0 0 0.5em; color: #232b42; font-si=\\r\\nze: 16px;\\\">Here\\'s what you ordered:</h4>\\r\\n                    <a class=3D\\\"=\\r\\nbutton button-blue\\\" href=3D\\\"https://martfury.gc/orders/tracking?order_id=3D=\\r\\n%2310000047&amp;email=3Drajaishtiaq6%40gmail.com\\\" style=3D\\\"color: #206bc4; =\\r\\ntext-decoration: none;\\\">View order</a>\\r\\n    or <a href=3D\\\"https://martfury=\\r\\n.gc\\\" style=3D\\\"color: #206bc4; text-decoration: none;\\\">Go to our shop</a>\\r\\n=\\r\\n\\r\\n    <br>\\r\\n\\r\\n<table class=3D\\\"bb-table\\\" cellspacing=3D\\\"0\\\" cellpadding=3D=\\r\\n\\\"0\\\" style=3D\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Fra=\\r\\nncisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: coll=\\r\\napse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\\\">=\\r\\n\\r\\n    <thead>\\r\\n        <tr>\\r\\n            <th colspan=3D\\\"2\\\" style=3D\\\"text=\\r\\n-transform: uppercase; font-weight: 600; color: #667382; font-size: 12px; p=\\r\\nadding: 0 0 4px 0; padding-left: 0;\\\"></th>\\r\\n            <th style=3D\\\"text-=\\r\\ntransform: uppercase; font-weight: 600; color: #667382; font-size: 12px; pa=\\r\\ndding: 0 0 4px 0;\\\">Quantity</th>\\r\\n            <th class=3D\\\"bb-text-right\\\" =\\r\\nstyle=3D\\\"text-align: right; text-transform: uppercase; font-weight: 600; co=\\r\\nlor: #667382; font-size: 12px; padding: 0 0 4px 0; padding-right: 0;\\\">Price=\\r\\n</th>\\r\\n        </tr>\\r\\n    </thead>\\r\\n\\r\\n    <tbody>\\r\\n                <tr=\\r\\n>\\r\\n            <td class=3D\\\"bb-pr-0\\\" style=3D\\\"font-family: Inter, -apple-s=\\r\\nystem, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue,=\\r\\n sans-serif; padding-right: 0; padding: 4px 0; padding-left: 0;\\\">\\r\\n       =\\r\\n         <a href=3D\\\"\\\" style=3D\\\"color: #206bc4; text-decoration: none;\\\">\\r\\n =\\r\\n                   <img src=3D\\\"https://martfury.gc/storage/products/3-150x1=\\r\\n50.jpg\\\" class=3D\\\" bb-rounded\\\" width=3D\\\"64\\\" height=3D\\\"64\\\" alt=3D\\\"\\\" style=3D\\\"=\\r\\nline-height: 100%; outline: none; text-decoration: none; vertical-align: ba=\\r\\nseline; font-size: 0; border: 0 none; border-radius: 4px;\\\">\\r\\n             =\\r\\n   </a>\\r\\n            </td>\\r\\n            <td class=3D\\\"bb-pl-md bb-w-100p\\\" =\\r\\nstyle=3D\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francis=\\r\\nco, Segoe UI, Roboto, Helvetica Neue, sans-serif; width: 100%; padding-left=\\r\\n: 16px !important; padding: 4px 0;\\\">\\r\\n                <strong style=3D\\\"fon=\\r\\nt-weight: 600;\\\">Beat Headphone</strong><br>\\r\\n                             =\\r\\n       <span class=3D\\\"bb-text-muted\\\" style=3D\\\"color: #667382;\\\">(Color: Gree=\\r\\nn, Size: L)</span>\\r\\n               =20\\r\\n                            </td>=\\r\\n\\r\\n            <td class=3D\\\"bb-text-center\\\" style=3D\\\"font-family: Inter, -a=\\r\\npple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica=\\r\\n Neue, sans-serif; text-align: center; padding: 4px 0;\\\">x 7</td>\\r\\n        =\\r\\n    <td class=3D\\\"bb-text-right\\\" style=3D\\\"font-family: Inter, -apple-system,=\\r\\n BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-=\\r\\nserif; text-align: right; padding: 4px 0; padding-right: 0;\\\">SAR20.00</td>=\\r\\n\\r\\n        </tr>\\r\\n   =20\\r\\n                                    <tr>\\r\\n      =\\r\\n              <td colspan=3D\\\"2\\\" class=3D\\\"bb-border-top bb-text-right\\\" style=\\r\\n=3D\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, S=\\r\\negoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; border-top:=\\r\\n 1px solid #dce0e5; padding: 4px 0; padding-left: 0;\\\">Subtotal</td>\\r\\n     =\\r\\n               <td colspan=3D\\\"2\\\" class=3D\\\"bb-border-top bb-text-right\\\" styl=\\r\\ne=3D\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, =\\r\\nSegoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; border-top=\\r\\n: 1px solid #dce0e5; padding: 4px 0; padding-right: 0;\\\">SAR140.00</td>\\r\\n  =\\r\\n              </tr>\\r\\n           =20\\r\\n           =20\\r\\n                       =\\r\\n     <tr>\\r\\n                    <td colspan=3D\\\"2\\\" class=3D\\\"bb-text-right\\\" s=\\r\\ntyle=3D\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisc=\\r\\no, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; padding=\\r\\n: 4px 0; padding-left: 0;\\\">Tax</td>\\r\\n                    <td colspan=3D\\\"2\\\"=\\r\\n class=3D\\\"bb-text-right\\\" style=3D\\\"font-family: Inter, -apple-system, BlinkM=\\r\\nacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; =\\r\\ntext-align: right; padding: 4px 0; padding-right: 0;\\\">SAR14.00</td>\\r\\n     =\\r\\n           </tr>\\r\\n           =20\\r\\n           =20\\r\\n                        <t=\\r\\nr>\\r\\n                <td colspan=3D\\\"2\\\" class=3D\\\"bb-text-right bb-font-stron=\\r\\ng bb-h3 bb-m-0\\\" style=3D\\\"font-family: Inter, -apple-system, BlinkMacSystemF=\\r\\nont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; color: #2=\\r\\n32b42; font-size: 20px; line-height: 130%; text-align: right; font-weight: =\\r\\n600; margin: 0; padding: 4px 0; padding-left: 0;\\\">Total</td>\\r\\n            =\\r\\n    <td colspan=3D\\\"2\\\" class=3D\\\"bb-font-strong bb-h3 bb-m-0 bb-text-right\\\" s=\\r\\ntyle=3D\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisc=\\r\\no, Segoe UI, Roboto, Helvetica Neue, sans-serif; color: #232b42; font-size:=\\r\\n 20px; line-height: 130%; text-align: right; font-weight: 600; margin: 0; p=\\r\\nadding: 4px 0; padding-right: 0;\\\">SAR154.00</td>\\r\\n            </tr>\\r\\n    =\\r\\n        </tbody>\\r\\n</table>\\r\\n\\r\\n\\r\\n                                    </t=\\r\\nd>\\r\\n            </tr>\\r\\n            <tr>\\r\\n                <td class=3D\\\"bb=\\r\\n-content bb-border-top\\\" style=3D\\\"font-family: Inter, -apple-system, BlinkMa=\\r\\ncSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; p=\\r\\nadding: 40px 48px; border-top: 1px solid #dce0e5;\\\">\\r\\n                    <=\\r\\ntable class=3D\\\"bb-row bb-mb-md\\\" cellpadding=3D\\\"0\\\" cellspacing=3D\\\"0\\\" style=\\r\\n=3D\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, S=\\r\\negoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; wid=\\r\\nth: 100%; table-layout: fixed; margin-bottom: 16px; -premailer-cellpadding:=\\r\\n 0; -premailer-cellspacing: 0;\\\">\\r\\n                        <tbody>\\r\\n      =\\r\\n                      <tr>\\r\\n                                <td class=3D\\\"b=\\r\\nb-bb-col\\\" style=3D\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, S=\\r\\nan Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;\\\">\\r\\n           =\\r\\n                         <h4 class=3D\\\"bb-m-0\\\" style=3D\\\"font-weight: 600; co=\\r\\nlor: #232b42; font-size: 16px; margin: 0;\\\">Order number</h4>\\r\\n            =\\r\\n                        <div>#10000047</div>\\r\\n                            =\\r\\n    </td>\\r\\n                                <td class=3D\\\"bb-col-spacer\\\" sty=\\r\\nle=3D\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco,=\\r\\n Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top; width: =\\r\\n24px;\\\"></td>\\r\\n                                <td class=3D\\\"bb-col\\\" style=\\r\\n=3D\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, S=\\r\\negoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top;\\\">\\r\\n     =\\r\\n                               <h4 class=3D\\\"bb-mb-0\\\" style=3D\\\"font-weight: =\\r\\n600; margin: 0 0 0.5em; color: #232b42; font-size: 16px; margin-bottom: 0;\\\"=\\r\\n>Order date</h4>\\r\\n                                    <div>2025-08-15 15:0=\\r\\n0:08</div>\\r\\n                                </td>\\r\\n                      =\\r\\n      </tr>\\r\\n                        </tbody>\\r\\n                    </tabl=\\r\\ne>\\r\\n                    <table class=3D\\\"bb-row\\\" cellpadding=3D\\\"0\\\" cellspac=\\r\\ning=3D\\\"0\\\" style=3D\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, S=\\r\\nan Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse=\\r\\n: collapse; width: 100%; table-layout: fixed; -premailer-cellpadding: 0; -p=\\r\\nremailer-cellspacing: 0;\\\">\\r\\n                        <tbody>\\r\\n            =\\r\\n                <tr>\\r\\n                                <td class=3D\\\"bb-col\\\"=\\r\\n style=3D\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Franci=\\r\\nsco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top;\\\">=\\r\\n\\r\\n                                                                        =\\r\\n<h4 class=3D\\\"bb-m-0\\\" style=3D\\\"font-weight: 600; color: #232b42; font-size: =\\r\\n16px; margin: 0;\\\">Shipping Method</h4>\\r\\n                                  =\\r\\n  <div>\\r\\n                                        Local Pickup\\r\\n          =\\r\\n                          </div>\\r\\n                                        =\\r\\n                            </td>\\r\\n\\r\\n                                <td =\\r\\nclass=3D\\\"bb-col-spacer\\\" style=3D\\\"font-family: Inter, -apple-system, BlinkMa=\\r\\ncSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; v=\\r\\nertical-align: top; width: 24px;\\\"></td>\\r\\n                                <=\\r\\ntd class=3D\\\"bb-col\\\" style=3D\\\"font-family: Inter, -apple-system, BlinkMacSys=\\r\\ntemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; verti=\\r\\ncal-align: top;\\\">\\r\\n                                    <h4 class=3D\\\"bb-m-0=\\r\\n\\\" style=3D\\\"font-weight: 600; color: #232b42; font-size: 16px; margin: 0;\\\">P=\\r\\nayment Method</h4>\\r\\n                                    <div>\\r\\n          =\\r\\n                              HyperPay\\r\\n                                  =\\r\\n  </div>\\r\\n                                </td>\\r\\n                        =\\r\\n    </tr>\\r\\n                        </tbody>\\r\\n                    </table>=\\r\\n\\r\\n                </td>\\r\\n            </tr>\\r\\n        </tbody>\\r\\n    </tab=\\r\\nle>\\r\\n</div>\\r\\n\\r\\n<table cellspacing=3D\\\"0\\\" cellpadding=3D\\\"0\\\" style=3D\\\"font-=\\r\\nfamily: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, =\\r\\nRoboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%;=\\r\\n -premailer-cellpadding: 0; -premailer-cellspacing: 0;\\\">\\r\\n    <tbody>\\r\\n  =\\r\\n      <tr>\\r\\n            <td class=3D\\\"bb-py-xl\\\" style=3D\\\"font-family: Inter=\\r\\n, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helve=\\r\\ntica Neue, sans-serif; padding-top: 48px; padding-bottom: 48px;\\\">\\r\\n       =\\r\\n         <table class=3D\\\"bb-text-center bb-text-muted\\\" cellspacing=3D\\\"0\\\" ce=\\r\\nllpadding=3D\\\"0\\\" style=3D\\\"font-family: Inter, -apple-system, BlinkMacSystemF=\\r\\nont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-co=\\r\\nllapse: collapse; width: 100%; color: #667382; text-align: center; -premail=\\r\\ner-cellpadding: 0; -premailer-cellspacing: 0;\\\">\\r\\n                    <tbod=\\r\\ny>\\r\\n                   =20\\r\\n                    <tr>\\r\\n                    =\\r\\n    <td class=3D\\\"bb-px-lg\\\" style=3D\\\"font-family: Inter, -apple-system, Blin=\\r\\nkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif=\\r\\n; padding-right: 24px; padding-left: 24px;\\\">\\r\\n                            =\\r\\n=C2=A9 2025 MartFury. All Rights Reserved.\\r\\n                        </td>=\\r\\n\\r\\n                    </tr>\\r\\n\\r\\n                                         =\\r\\n   <tr>\\r\\n                            <td class=3D\\\"bb-pt-md\\\" style=3D\\\"font-=\\r\\nfamily: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, =\\r\\nRoboto, Helvetica Neue, sans-serif; padding-top: 16px;\\\">\\r\\n                =\\r\\n                If you have any questions, feel free to message us at <a hr=\\r\\nef=3D\\\"mailto:<EMAIL>\\\" style=3D\\\"color: #206bc4; text-decoration: no=\\r\\nne;\\\"><EMAIL></a>.\\r\\n                            </td>\\r\\n          =\\r\\n              </tr>\\r\\n                                        </tbody>\\r\\n  =\\r\\n              </table>\\r\\n            </td>\\r\\n        </tr>\\r\\n    </tbody>=\\r\\n\\r\\n</table>\\r\\n</td>\\r\\n</tr>\\r\\n</tbody>\\r\\n</table>\\r\\n</td>\\r\\n</tr>\\r\\n</tbody=\\r\\n>\\r\\n</table>\\r\\n</center>\\r\\n</body>\\r\\n\\r\\n</html>', '', '2025-08-15 15:02:52', '2025-08-15 15:02:52')", "type": "query", "params": [], "bindings": ["\"Example\" <<EMAIL>>", "<EMAIL>", "", "", "New order(s) at MartFury", "<!doctype html>\n<html lang=\"en\">\n\n<head>\n    <meta charset=\"UTF-8\">\n    <title>MartFury</title>\n</head>\n\n<body class=\"bb-bg-body\" dir=\"ltr\" style=\"margin: 0; padding: 0; font-size: 14px; line-height: 171.4285714286%; mso-line-height-rule: exactly; color: #3A4859; width: 100%; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale; -webkit-font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11'; font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11'; font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; background-color: #f6f7f9;\">\n\n<center>\n    <table class=\"bb-main bb-bg-body\" width=\"100%\" cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; background-color: #f6f7f9;\">\n        <tbody>\n            <tr>\n                <td align=\"center\" valign=\"top\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;\">\n                    <table class=\"bb-wrap\" cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; max-width: 640px; text-align: left; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n                        <tbody>\n                            <tr>\n                                <td class=\"bb-p-sm\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 8px;\">\n                                    <table cellpadding=\"0\" cellspacing=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n                                        <tbody>\n                                            <tr>\n                                                <td class=\"bb-py-lg\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-top: 24px; padding-bottom: 24px;\">\n                                                    <table cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n                                                        <tbody>\n                                                            <tr>\n                                                                <td class=\"bb-text-left\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: left;\">\n                                                                    <a href=\"https://martfury.gc\" style=\"color: #206bc4; text-decoration: none;\">\n                                                                        <img class=\"bb-logo\" src=\"https://martfury.gc/storage/general/logo.png\" alt=\"MartFury\" style=\"line-height: 100%; outline: none; text-decoration: none; vertical-align: baseline; font-size: 0; border: 0 none; max-height: 40px;\">\n                                                                    </a>\n                                                                </td>\n                                                                <td class=\"bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right;\">\n                                                                    2025-08-15 15:02:49\n                                                                </td>\n                                                            </tr>\n                                                        </tbody>\n                                                    </table>\n                                                </td>\n                                            </tr>\n                                        </tbody>\n                                    </table>\n\n\n<div class=\"bb-main-content\">\n    <table class=\"bb-box\" cellpadding=\"0\" cellspacing=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; background: #ffffff; border-radius: 4px; -webkit-box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05); box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05); border: 1px solid #dce0e5; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n        <tbody>\n            <tr>\n                <td class=\"bb-content bb-pb-0\" align=\"center\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; padding-bottom: 0;\">\n                    <table class=\"bb-icon bb-icon-lg bb-bg-blue\" cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 0; border-radius: 50%; background: #edeef0; line-height: 100%; font-weight: 300; border-collapse: separate; text-align: center; width: 72px; height: 72px; font-size: 48px; background-color: #206bc4; color: #ffffff; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n                        <tbody>\n                            <tr>\n                                <td valign=\"middle\" align=\"center\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;\">\n                                    <img src=\"https://martfury.gc/vendor/core/core/base/images/email-icons/shopping-cart.png\" class=\"bb-va-middle\" width=\"40\" height=\"40\" alt=\"Icon\" style=\"border: 0 none; line-height: 100%; outline: none; text-decoration: none; font-size: 0; vertical-align: middle; display: block; width: 40px; height: 40px;\">\n                                </td>\n                            </tr>\n                        </tbody>\n                    </table>\n                    <h1 class=\"bb-text-center bb-m-0 bb-mt-md\" style=\"font-weight: 600; color: #232b42; font-size: 28px; line-height: 130%; text-align: center; margin: 0; margin-top: 16px;\">Order successfully!</h1>\n                </td>\n            </tr>\n            <tr>\n                <td class=\"bb-content\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px;\">\n                    <div>Dear GoPro,</div>\n                    <div>You got a new order on MartFury!</div>\n                </td>\n            </tr>\n            <tr>\n                <td class=\"bb-content bb-pt-0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; padding-top: 0;\">\n                    <table class=\"bb-row bb-mb-md\" cellpadding=\"0\" cellspacing=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; table-layout: fixed; margin-bottom: 16px; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n                        <tbody>\n                            <tr>\n                                <td class=\"bb-bb-col\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;\">\n                                    <h4 class=\"bb-m-0\" style=\"font-weight: 600; color: #232b42; font-size: 16px; margin: 0;\">Customer Information</h4>\n                                    <div>Name: <strong style=\"font-weight: 600;\">Ishtiaq Ahmed</strong>\n</div>\n                                                                        <div>Phone: <strong style=\"font-weight: 600;\">+923147552550</strong>\n</div>\n                                                                                                                                                <div>Address: <strong style=\"font-weight: 600;\">test, test, Test, PK</strong>\n</div>\n                                                                    </td>\n                            </tr>\n                        </tbody>\n                    </table>\n                </td>\n            </tr>\n            <tr>\n                <td class=\"bb-content bb-pt-0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; padding-top: 0;\">\n                    <h4 style=\"font-weight: 600; margin: 0 0 0.5em; color: #232b42; font-size: 16px;\">Here's what you ordered:</h4>\n                    <a class=\"button button-blue\" href=\"https://martfury.gc/orders/tracking?order_id=%2310000047&amp;email=rajaishtiaq6%40gmail.com\" style=\"color: #206bc4; text-decoration: none;\">View order</a>\n    or <a href=\"https://martfury.gc\" style=\"color: #206bc4; text-decoration: none;\">Go to our shop</a>\n\n    <br>\n\n<table class=\"bb-table\" cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n    <thead>\n        <tr>\n            <th colspan=\"2\" style=\"text-transform: uppercase; font-weight: 600; color: #667382; font-size: 12px; padding: 0 0 4px 0; padding-left: 0;\"></th>\n            <th style=\"text-transform: uppercase; font-weight: 600; color: #667382; font-size: 12px; padding: 0 0 4px 0;\">Quantity</th>\n            <th class=\"bb-text-right\" style=\"text-align: right; text-transform: uppercase; font-weight: 600; color: #667382; font-size: 12px; padding: 0 0 4px 0; padding-right: 0;\">Price</th>\n        </tr>\n    </thead>\n\n    <tbody>\n                <tr>\n            <td class=\"bb-pr-0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-right: 0; padding: 4px 0; padding-left: 0;\">\n                <a href=\"\" style=\"color: #206bc4; text-decoration: none;\">\n                    <img src=\"https://martfury.gc/storage/products/3-150x150.jpg\" class=\" bb-rounded\" width=\"64\" height=\"64\" alt=\"\" style=\"line-height: 100%; outline: none; text-decoration: none; vertical-align: baseline; font-size: 0; border: 0 none; border-radius: 4px;\">\n                </a>\n            </td>\n            <td class=\"bb-pl-md bb-w-100p\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; width: 100%; padding-left: 16px !important; padding: 4px 0;\">\n                <strong style=\"font-weight: 600;\">Beat Headphone</strong><br>\n                                    <span class=\"bb-text-muted\" style=\"color: #667382;\">(Color: Green, Size: L)</span>\n                \n                            </td>\n            <td class=\"bb-text-center\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: center; padding: 4px 0;\">x 7</td>\n            <td class=\"bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; padding: 4px 0; padding-right: 0;\">SAR20.00</td>\n        </tr>\n    \n                                    <tr>\n                    <td colspan=\"2\" class=\"bb-border-top bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; border-top: 1px solid #dce0e5; padding: 4px 0; padding-left: 0;\">Subtotal</td>\n                    <td colspan=\"2\" class=\"bb-border-top bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; border-top: 1px solid #dce0e5; padding: 4px 0; padding-right: 0;\">SAR140.00</td>\n                </tr>\n            \n            \n                            <tr>\n                    <td colspan=\"2\" class=\"bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; padding: 4px 0; padding-left: 0;\">Tax</td>\n                    <td colspan=\"2\" class=\"bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; padding: 4px 0; padding-right: 0;\">SAR14.00</td>\n                </tr>\n            \n            \n                        <tr>\n                <td colspan=\"2\" class=\"bb-text-right bb-font-strong bb-h3 bb-m-0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; color: #232b42; font-size: 20px; line-height: 130%; text-align: right; font-weight: 600; margin: 0; padding: 4px 0; padding-left: 0;\">Total</td>\n                <td colspan=\"2\" class=\"bb-font-strong bb-h3 bb-m-0 bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; color: #232b42; font-size: 20px; line-height: 130%; text-align: right; font-weight: 600; margin: 0; padding: 4px 0; padding-right: 0;\">SAR154.00</td>\n            </tr>\n            </tbody>\n</table>\n\n\n                                    </td>\n            </tr>\n            <tr>\n                <td class=\"bb-content bb-border-top\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; border-top: 1px solid #dce0e5;\">\n                    <table class=\"bb-row bb-mb-md\" cellpadding=\"0\" cellspacing=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; table-layout: fixed; margin-bottom: 16px; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n                        <tbody>\n                            <tr>\n                                <td class=\"bb-bb-col\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;\">\n                                    <h4 class=\"bb-m-0\" style=\"font-weight: 600; color: #232b42; font-size: 16px; margin: 0;\">Order number</h4>\n                                    <div>#10000047</div>\n                                </td>\n                                <td class=\"bb-col-spacer\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top; width: 24px;\"></td>\n                                <td class=\"bb-col\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top;\">\n                                    <h4 class=\"bb-mb-0\" style=\"font-weight: 600; margin: 0 0 0.5em; color: #232b42; font-size: 16px; margin-bottom: 0;\">Order date</h4>\n                                    <div>2025-08-15 15:00:08</div>\n                                </td>\n                            </tr>\n                        </tbody>\n                    </table>\n                    <table class=\"bb-row\" cellpadding=\"0\" cellspacing=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; table-layout: fixed; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n                        <tbody>\n                            <tr>\n                                <td class=\"bb-col\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top;\">\n                                                                        <h4 class=\"bb-m-0\" style=\"font-weight: 600; color: #232b42; font-size: 16px; margin: 0;\">Shipping Method</h4>\n                                    <div>\n                                        Local Pickup\n                                    </div>\n                                                                    </td>\n\n                                <td class=\"bb-col-spacer\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top; width: 24px;\"></td>\n                                <td class=\"bb-col\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top;\">\n                                    <h4 class=\"bb-m-0\" style=\"font-weight: 600; color: #232b42; font-size: 16px; margin: 0;\">Payment Method</h4>\n                                    <div>\n                                        HyperPay\n                                    </div>\n                                </td>\n                            </tr>\n                        </tbody>\n                    </table>\n                </td>\n            </tr>\n        </tbody>\n    </table>\n</div>\n\n<table cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n    <tbody>\n        <tr>\n            <td class=\"bb-py-xl\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-top: 48px; padding-bottom: 48px;\">\n                <table class=\"bb-text-center bb-text-muted\" cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; color: #667382; text-align: center; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n                    <tbody>\n                    \n                    <tr>\n                        <td class=\"bb-px-lg\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-right: 24px; padding-left: 24px;\">\n                            © 2025 MartFury. All Rights Reserved.\n                        </td>\n                    </tr>\n\n                                            <tr>\n                            <td class=\"bb-pt-md\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-top: 16px;\">\n                                If you have any questions, feel free to message us at <a href=\"mailto:<EMAIL>\" style=\"color: #206bc4; text-decoration: none;\"><EMAIL></a>.\n                            </td>\n                        </tr>\n                                        </tbody>\n                </table>\n            </td>\n        </tr>\n    </tbody>\n</table>\n</td>\n</tr>\n</tbody>\n</table>\n</td>\n</tr>\n</tbody>\n</table>\n</center>\n</body>\n\n</html>", null, "From: Example <<EMAIL>>\r\nTo: g<PERSON><PERSON><PERSON>@example.org\r\nSubject: New order(s) at MartFury\r\nMessage-ID: <<EMAIL>>\r\nMIME-Version: 1.0\r\nDate: Fri, 15 Aug 2025 15:02:52 +0000\r\nContent-Type: text/html; charset=utf-8\r\nContent-Transfer-Encoding: quoted-printable\r\n\r\n<!doctype html>\r\n<html lang=3D\"en\">\r\n\r\n<head>\r\n    <meta charset=3D\"UTF=\r\n-8\">\r\n    <title>MartFury</title>\r\n</head>\r\n\r\n<body class=3D\"bb-bg-body=\r\n\" dir=3D\"ltr\" style=3D\"margin: 0; padding: 0; font-size: 14px; line-height:=\r\n 171.4285714286%; mso-line-height-rule: exactly; color: #3A4859; width: 100=\r\n%; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale;=\r\n -webkit-font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11'; font-featur=\r\ne-settings: 'cv02', 'cv03', 'cv04', 'cv11'; font-family: Inter, -apple-syst=\r\nem, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sa=\r\nns-serif; background-color: #f6f7f9;\">\r\n\r\n<center>\r\n    <table class=3D\"=\r\nbb-main bb-bg-body\" width=3D\"100%\" cellspacing=3D\"0\" cellpadding=3D\"0\" styl=\r\ne=3D\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, =\r\nSegoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; wi=\r\ndth: 100%; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; back=\r\nground-color: #f6f7f9;\">\r\n        <tbody>\r\n            <tr>\r\n           =\r\n     <td align=3D\"center\" valign=3D\"top\" style=3D\"font-family: Inter, -appl=\r\ne-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Ne=\r\nue, sans-serif;\">\r\n                    <table class=3D\"bb-wrap\" cellspacin=\r\ng=3D\"0\" cellpadding=3D\"0\" style=3D\"font-family: Inter, -apple-system, Blink=\r\nMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;=\r\n border-collapse: collapse; width: 100%; max-width: 640px; text-align: left=\r\n; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\r\n               =\r\n         <tbody>\r\n                            <tr>\r\n                     =\r\n           <td class=3D\"bb-p-sm\" style=3D\"font-family: Inter, -apple-system=\r\n, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans=\r\n-serif; padding: 8px;\">\r\n                                    <table cellpa=\r\ndding=3D\"0\" cellspacing=3D\"0\" style=3D\"font-family: Inter, -apple-system, B=\r\nlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-se=\r\nrif; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -pr=\r\nemailer-cellspacing: 0;\">\r\n                                        <tbody>=\r\n\r\n                                            <tr>\r\n                     =\r\n                           <td class=3D\"bb-py-lg\" style=3D\"font-family: Int=\r\ner, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Hel=\r\nvetica Neue, sans-serif; padding-top: 24px; padding-bottom: 24px;\">\r\n     =\r\n                                               <table cellspacing=3D\"0\" cel=\r\nlpadding=3D\"0\" style=3D\"font-family: Inter, -apple-system, BlinkMacSystemFo=\r\nnt, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-col=\r\nlapse: collapse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspa=\r\ncing: 0;\">\r\n                                                        <tbody=\r\n>\r\n                                                            <tr>\r\n    =\r\n                                                            <td class=3D\"bb=\r\n-text-left\" style=3D\"font-family: Inter, -apple-system, BlinkMacSystemFont,=\r\n San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: l=\r\neft;\">\r\n                                                                  =\r\n  <a href=3D\"https://martfury.gc\" style=3D\"color: #206bc4; text-decoration:=\r\n none;\">\r\n                                                                =\r\n        <img class=3D\"bb-logo\" src=3D\"https://martfury.gc/storage/general/l=\r\nogo.png\" alt=3D\"MartFury\" style=3D\"line-height: 100%; outline: none; text-d=\r\necoration: none; vertical-align: baseline; font-size: 0; border: 0 none; ma=\r\nx-height: 40px;\">\r\n                                                       =\r\n             </a>\r\n                                                       =\r\n         </td>\r\n                                                          =\r\n      <td class=3D\"bb-text-right\" style=3D\"font-family: Inter, -apple-syste=\r\nm, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, san=\r\ns-serif; text-align: right;\">\r\n                                           =\r\n                         2025-08-15 15:02:49\r\n                            =\r\n                                    </td>\r\n                               =\r\n                             </tr>\r\n                                      =\r\n                  </tbody>\r\n                                              =\r\n      </table>\r\n                                                </td>\r\n  =\r\n                                          </tr>\r\n                         =\r\n               </tbody>\r\n                                    </table>\r\n=\r\n\r\n\r\n<div class=3D\"bb-main-content\">\r\n    <table class=3D\"bb-box\" cellpad=\r\nding=3D\"0\" cellspacing=3D\"0\" style=3D\"font-family: Inter, -apple-system, Bl=\r\ninkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-ser=\r\nif; border-collapse: collapse; width: 100%; background: #ffffff; border-rad=\r\nius: 4px; -webkit-box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05); box-shadow: 0 =\r\n1px 4px rgba(0, 0, 0, 0.05); border: 1px solid #dce0e5; -premailer-cellpadd=\r\ning: 0; -premailer-cellspacing: 0;\">\r\n        <tbody>\r\n            <tr>=\r\n\r\n                <td class=3D\"bb-content bb-pb-0\" align=3D\"center\" style=\r\n=3D\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, S=\r\negoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; padding-bo=\r\nttom: 0;\">\r\n                    <table class=3D\"bb-icon bb-icon-lg bb-bg-b=\r\nlue\" cellspacing=3D\"0\" cellpadding=3D\"0\" style=3D\"font-family: Inter, -appl=\r\ne-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Ne=\r\nue, sans-serif; padding: 0; border-radius: 50%; background: #edeef0; line-h=\r\neight: 100%; font-weight: 300; border-collapse: separate; text-align: cente=\r\nr; width: 72px; height: 72px; font-size: 48px; background-color: #206bc4; c=\r\nolor: #ffffff; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\r\n  =\r\n                      <tbody>\r\n                            <tr>\r\n        =\r\n                        <td valign=3D\"middle\" align=3D\"center\" style=3D\"fon=\r\nt-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI=\r\n, Roboto, Helvetica Neue, sans-serif;\">\r\n                                 =\r\n   <img src=3D\"https://martfury.gc/vendor/core/core/base/images/email-icons=\r\n/shopping-cart.png\" class=3D\"bb-va-middle\" width=3D\"40\" height=3D\"40\" alt=\r\n=3D\"Icon\" style=3D\"border: 0 none; line-height: 100%; outline: none; text-d=\r\necoration: none; font-size: 0; vertical-align: middle; display: block; widt=\r\nh: 40px; height: 40px;\">\r\n                                </td>\r\n        =\r\n                    </tr>\r\n                        </tbody>\r\n            =\r\n        </table>\r\n                    <h1 class=3D\"bb-text-center bb-m-0 b=\r\nb-mt-md\" style=3D\"font-weight: 600; color: #232b42; font-size: 28px; line-h=\r\neight: 130%; text-align: center; margin: 0; margin-top: 16px;\">Order succes=\r\nsfully!</h1>\r\n                </td>\r\n            </tr>\r\n            <tr>=\r\n\r\n                <td class=3D\"bb-content\" style=3D\"font-family: Inter, -a=\r\npple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica=\r\n Neue, sans-serif; padding: 40px 48px;\">\r\n                    <div>Dear Go=\r\nPro,</div>\r\n                    <div>You got a new order on MartFury!</div=\r\n>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n        =\r\n        <td class=3D\"bb-content bb-pt-0\" style=3D\"font-family: Inter, -appl=\r\ne-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Ne=\r\nue, sans-serif; padding: 40px 48px; padding-top: 0;\">\r\n                   =\r\n <table class=3D\"bb-row bb-mb-md\" cellpadding=3D\"0\" cellspacing=3D\"0\" style=\r\n=3D\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, S=\r\negoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; wid=\r\nth: 100%; table-layout: fixed; margin-bottom: 16px; -premailer-cellpadding:=\r\n 0; -premailer-cellspacing: 0;\">\r\n                        <tbody>\r\n      =\r\n                      <tr>\r\n                                <td class=3D\"b=\r\nb-bb-col\" style=3D\"font-family: Inter, -apple-system, BlinkMacSystemFont, S=\r\nan Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;\">\r\n           =\r\n                         <h4 class=3D\"bb-m-0\" style=3D\"font-weight: 600; co=\r\nlor: #232b42; font-size: 16px; margin: 0;\">Customer Information</h4>\r\n    =\r\n                                <div>Name: <strong style=3D\"font-weight: 60=\r\n0;\">Ishtiaq Ahmed</strong>\r\n</div>\r\n                                     =\r\n                                   <div>Phone: <strong style=3D\"font-weight=\r\n: 600;\">+923147552550</strong>\r\n</div>\r\n                                 =\r\n                                                                           =\r\n                                    <div>Address: <strong style=3D\"font-wei=\r\nght: 600;\">test, test, Test, PK</strong>\r\n</div>\r\n                       =\r\n                                             </td>\r\n                      =\r\n      </tr>\r\n                        </tbody>\r\n                    </tabl=\r\ne>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n       =\r\n         <td class=3D\"bb-content bb-pt-0\" style=3D\"font-family: Inter, -app=\r\nle-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica N=\r\neue, sans-serif; padding: 40px 48px; padding-top: 0;\">\r\n                  =\r\n  <h4 style=3D\"font-weight: 600; margin: 0 0 0.5em; color: #232b42; font-si=\r\nze: 16px;\">Here's what you ordered:</h4>\r\n                    <a class=3D\"=\r\nbutton button-blue\" href=3D\"https://martfury.gc/orders/tracking?order_id=3D=\r\n%2310000047&amp;email=3Drajaishtiaq6%40gmail.com\" style=3D\"color: #206bc4; =\r\ntext-decoration: none;\">View order</a>\r\n    or <a href=3D\"https://martfury=\r\n.gc\" style=3D\"color: #206bc4; text-decoration: none;\">Go to our shop</a>\r\n=\r\n\r\n    <br>\r\n\r\n<table class=3D\"bb-table\" cellspacing=3D\"0\" cellpadding=3D=\r\n\"0\" style=3D\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Fra=\r\nncisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: coll=\r\napse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">=\r\n\r\n    <thead>\r\n        <tr>\r\n            <th colspan=3D\"2\" style=3D\"text=\r\n-transform: uppercase; font-weight: 600; color: #667382; font-size: 12px; p=\r\nadding: 0 0 4px 0; padding-left: 0;\"></th>\r\n            <th style=3D\"text-=\r\ntransform: uppercase; font-weight: 600; color: #667382; font-size: 12px; pa=\r\ndding: 0 0 4px 0;\">Quantity</th>\r\n            <th class=3D\"bb-text-right\" =\r\nstyle=3D\"text-align: right; text-transform: uppercase; font-weight: 600; co=\r\nlor: #667382; font-size: 12px; padding: 0 0 4px 0; padding-right: 0;\">Price=\r\n</th>\r\n        </tr>\r\n    </thead>\r\n\r\n    <tbody>\r\n                <tr=\r\n>\r\n            <td class=3D\"bb-pr-0\" style=3D\"font-family: Inter, -apple-s=\r\nystem, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue,=\r\n sans-serif; padding-right: 0; padding: 4px 0; padding-left: 0;\">\r\n       =\r\n         <a href=3D\"\" style=3D\"color: #206bc4; text-decoration: none;\">\r\n =\r\n                   <img src=3D\"https://martfury.gc/storage/products/3-150x1=\r\n50.jpg\" class=3D\" bb-rounded\" width=3D\"64\" height=3D\"64\" alt=3D\"\" style=3D\"=\r\nline-height: 100%; outline: none; text-decoration: none; vertical-align: ba=\r\nseline; font-size: 0; border: 0 none; border-radius: 4px;\">\r\n             =\r\n   </a>\r\n            </td>\r\n            <td class=3D\"bb-pl-md bb-w-100p\" =\r\nstyle=3D\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francis=\r\nco, Segoe UI, Roboto, Helvetica Neue, sans-serif; width: 100%; padding-left=\r\n: 16px !important; padding: 4px 0;\">\r\n                <strong style=3D\"fon=\r\nt-weight: 600;\">Beat Headphone</strong><br>\r\n                             =\r\n       <span class=3D\"bb-text-muted\" style=3D\"color: #667382;\">(Color: Gree=\r\nn, Size: L)</span>\r\n               =20\r\n                            </td>=\r\n\r\n            <td class=3D\"bb-text-center\" style=3D\"font-family: Inter, -a=\r\npple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica=\r\n Neue, sans-serif; text-align: center; padding: 4px 0;\">x 7</td>\r\n        =\r\n    <td class=3D\"bb-text-right\" style=3D\"font-family: Inter, -apple-system,=\r\n BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-=\r\nserif; text-align: right; padding: 4px 0; padding-right: 0;\">SAR20.00</td>=\r\n\r\n        </tr>\r\n   =20\r\n                                    <tr>\r\n      =\r\n              <td colspan=3D\"2\" class=3D\"bb-border-top bb-text-right\" style=\r\n=3D\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, S=\r\negoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; border-top:=\r\n 1px solid #dce0e5; padding: 4px 0; padding-left: 0;\">Subtotal</td>\r\n     =\r\n               <td colspan=3D\"2\" class=3D\"bb-border-top bb-text-right\" styl=\r\ne=3D\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, =\r\nSegoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; border-top=\r\n: 1px solid #dce0e5; padding: 4px 0; padding-right: 0;\">SAR140.00</td>\r\n  =\r\n              </tr>\r\n           =20\r\n           =20\r\n                       =\r\n     <tr>\r\n                    <td colspan=3D\"2\" class=3D\"bb-text-right\" s=\r\ntyle=3D\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisc=\r\no, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; padding=\r\n: 4px 0; padding-left: 0;\">Tax</td>\r\n                    <td colspan=3D\"2\"=\r\n class=3D\"bb-text-right\" style=3D\"font-family: Inter, -apple-system, BlinkM=\r\nacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; =\r\ntext-align: right; padding: 4px 0; padding-right: 0;\">SAR14.00</td>\r\n     =\r\n           </tr>\r\n           =20\r\n           =20\r\n                        <t=\r\nr>\r\n                <td colspan=3D\"2\" class=3D\"bb-text-right bb-font-stron=\r\ng bb-h3 bb-m-0\" style=3D\"font-family: Inter, -apple-system, BlinkMacSystemF=\r\nont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; color: #2=\r\n32b42; font-size: 20px; line-height: 130%; text-align: right; font-weight: =\r\n600; margin: 0; padding: 4px 0; padding-left: 0;\">Total</td>\r\n            =\r\n    <td colspan=3D\"2\" class=3D\"bb-font-strong bb-h3 bb-m-0 bb-text-right\" s=\r\ntyle=3D\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisc=\r\no, Segoe UI, Roboto, Helvetica Neue, sans-serif; color: #232b42; font-size:=\r\n 20px; line-height: 130%; text-align: right; font-weight: 600; margin: 0; p=\r\nadding: 4px 0; padding-right: 0;\">SAR154.00</td>\r\n            </tr>\r\n    =\r\n        </tbody>\r\n</table>\r\n\r\n\r\n                                    </t=\r\nd>\r\n            </tr>\r\n            <tr>\r\n                <td class=3D\"bb=\r\n-content bb-border-top\" style=3D\"font-family: Inter, -apple-system, BlinkMa=\r\ncSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; p=\r\nadding: 40px 48px; border-top: 1px solid #dce0e5;\">\r\n                    <=\r\ntable class=3D\"bb-row bb-mb-md\" cellpadding=3D\"0\" cellspacing=3D\"0\" style=\r\n=3D\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, S=\r\negoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; wid=\r\nth: 100%; table-layout: fixed; margin-bottom: 16px; -premailer-cellpadding:=\r\n 0; -premailer-cellspacing: 0;\">\r\n                        <tbody>\r\n      =\r\n                      <tr>\r\n                                <td class=3D\"b=\r\nb-bb-col\" style=3D\"font-family: Inter, -apple-system, BlinkMacSystemFont, S=\r\nan Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;\">\r\n           =\r\n                         <h4 class=3D\"bb-m-0\" style=3D\"font-weight: 600; co=\r\nlor: #232b42; font-size: 16px; margin: 0;\">Order number</h4>\r\n            =\r\n                        <div>#10000047</div>\r\n                            =\r\n    </td>\r\n                                <td class=3D\"bb-col-spacer\" sty=\r\nle=3D\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco,=\r\n Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top; width: =\r\n24px;\"></td>\r\n                                <td class=3D\"bb-col\" style=\r\n=3D\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, S=\r\negoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top;\">\r\n     =\r\n                               <h4 class=3D\"bb-mb-0\" style=3D\"font-weight: =\r\n600; margin: 0 0 0.5em; color: #232b42; font-size: 16px; margin-bottom: 0;\"=\r\n>Order date</h4>\r\n                                    <div>2025-08-15 15:0=\r\n0:08</div>\r\n                                </td>\r\n                      =\r\n      </tr>\r\n                        </tbody>\r\n                    </tabl=\r\ne>\r\n                    <table class=3D\"bb-row\" cellpadding=3D\"0\" cellspac=\r\ning=3D\"0\" style=3D\"font-family: Inter, -apple-system, BlinkMacSystemFont, S=\r\nan Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse=\r\n: collapse; width: 100%; table-layout: fixed; -premailer-cellpadding: 0; -p=\r\nremailer-cellspacing: 0;\">\r\n                        <tbody>\r\n            =\r\n                <tr>\r\n                                <td class=3D\"bb-col\"=\r\n style=3D\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Franci=\r\nsco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top;\">=\r\n\r\n                                                                        =\r\n<h4 class=3D\"bb-m-0\" style=3D\"font-weight: 600; color: #232b42; font-size: =\r\n16px; margin: 0;\">Shipping Method</h4>\r\n                                  =\r\n  <div>\r\n                                        Local Pickup\r\n          =\r\n                          </div>\r\n                                        =\r\n                            </td>\r\n\r\n                                <td =\r\nclass=3D\"bb-col-spacer\" style=3D\"font-family: Inter, -apple-system, BlinkMa=\r\ncSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; v=\r\nertical-align: top; width: 24px;\"></td>\r\n                                <=\r\ntd class=3D\"bb-col\" style=3D\"font-family: Inter, -apple-system, BlinkMacSys=\r\ntemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; verti=\r\ncal-align: top;\">\r\n                                    <h4 class=3D\"bb-m-0=\r\n\" style=3D\"font-weight: 600; color: #232b42; font-size: 16px; margin: 0;\">P=\r\nayment Method</h4>\r\n                                    <div>\r\n          =\r\n                              HyperPay\r\n                                  =\r\n  </div>\r\n                                </td>\r\n                        =\r\n    </tr>\r\n                        </tbody>\r\n                    </table>=\r\n\r\n                </td>\r\n            </tr>\r\n        </tbody>\r\n    </tab=\r\nle>\r\n</div>\r\n\r\n<table cellspacing=3D\"0\" cellpadding=3D\"0\" style=3D\"font-=\r\nfamily: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, =\r\nRoboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%;=\r\n -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\r\n    <tbody>\r\n  =\r\n      <tr>\r\n            <td class=3D\"bb-py-xl\" style=3D\"font-family: Inter=\r\n, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helve=\r\ntica Neue, sans-serif; padding-top: 48px; padding-bottom: 48px;\">\r\n       =\r\n         <table class=3D\"bb-text-center bb-text-muted\" cellspacing=3D\"0\" ce=\r\nllpadding=3D\"0\" style=3D\"font-family: Inter, -apple-system, BlinkMacSystemF=\r\nont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-co=\r\nllapse: collapse; width: 100%; color: #667382; text-align: center; -premail=\r\ner-cellpadding: 0; -premailer-cellspacing: 0;\">\r\n                    <tbod=\r\ny>\r\n                   =20\r\n                    <tr>\r\n                    =\r\n    <td class=3D\"bb-px-lg\" style=3D\"font-family: Inter, -apple-system, Blin=\r\nkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif=\r\n; padding-right: 24px; padding-left: 24px;\">\r\n                            =\r\n=C2=A9 2025 MartFury. All Rights Reserved.\r\n                        </td>=\r\n\r\n                    </tr>\r\n\r\n                                         =\r\n   <tr>\r\n                            <td class=3D\"bb-pt-md\" style=3D\"font-=\r\nfamily: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, =\r\nRoboto, Helvetica Neue, sans-serif; padding-top: 16px;\">\r\n                =\r\n                If you have any questions, feel free to message us at <a hr=\r\nef=3D\"mailto:<EMAIL>\" style=3D\"color: #206bc4; text-decoration: no=\r\nne;\"><EMAIL></a>.\r\n                            </td>\r\n          =\r\n              </tr>\r\n                                        </tbody>\r\n  =\r\n              </table>\r\n            </td>\r\n        </tr>\r\n    </tbody>=\r\n\r\n</table>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table>\r\n</td>\r\n</tr>\r\n</tbody=\r\n>\r\n</table>\r\n</center>\r\n</body>\r\n\r\n</html>", "", "2025-08-15 15:02:52", "2025-08-15 15:02:52"], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "platform/plugins/fob-email-log/src/Listeners/EmailLogger.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\fob-email-log\\src\\Listeners\\EmailLogger.php", "line": 20}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Mail/Mailer.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailer.php", "line": 617}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Mail/Mailer.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailer.php", "line": 336}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Mail/Mailable.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailable.php", "line": 207}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Mail/Mailable.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailable.php", "line": 200}], "start": **********.4556959, "duration": 0.02782, "duration_str": "27.82ms", "memory": 0, "memory_str": null, "filename": "EmailLogger.php:20", "source": {"index": 18, "namespace": null, "name": "platform/plugins/fob-email-log/src/Listeners/EmailLogger.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\fob-email-log\\src\\Listeners\\EmailLogger.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Ffob-email-log%2Fsrc%2FListeners%2FEmailLogger.php&line=20", "ajax": false, "filename": "EmailLogger.php", "line": "20"}, "connection": "martfury", "explain": null, "start_percent": 72.324, "width_percent": 16.774}, {"sql": "insert into `ec_order_histories` (`action`, `description`, `order_id`, `updated_at`, `created_at`) values ('create_order', 'New order #10000047 from Isht<PERSON><PERSON> Ahmed', 48, '2025-08-15 15:02:52', '2025-08-15 15:02:52')", "type": "query", "params": [], "bindings": [{"value": "create_order", "label": "create_order"}, "New order #10000047 from Isht<PERSON><PERSON> Ahmed", 48, "2025-08-15 15:02:52", "2025-08-15 15:02:52"], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 145}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 321}, {"index": 24, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 26, "namespace": null, "name": "platform/plugins/hyperpay/src/Http/Controllers/HyperPayController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\hyperpay\\src\\Http\\Controllers\\HyperPayController.php", "line": 76}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.487848, "duration": 0.00368, "duration_str": "3.68ms", "memory": 0, "memory_str": null, "filename": "OrderHelper.php:145", "source": {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 145}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FOrderHelper.php&line=145", "ajax": false, "filename": "OrderHelper.php", "line": "145"}, "connection": "martfury", "explain": null, "start_percent": 89.099, "width_percent": 2.219}, {"sql": "select `ec_flash_sales`.*, `ec_flash_sale_products`.`product_id` as `pivot_product_id`, `ec_flash_sale_products`.`flash_sale_id` as `pivot_flash_sale_id`, `ec_flash_sale_products`.`price` as `pivot_price`, `ec_flash_sale_products`.`quantity` as `pivot_quantity`, `ec_flash_sale_products`.`sold` as `pivot_sold` from `ec_flash_sales` inner join `ec_flash_sale_products` on `ec_flash_sales`.`id` = `ec_flash_sale_products`.`flash_sale_id` where `ec_flash_sale_products`.`product_id` = 3 and `status` = 'published' and date(`end_date`) >= '2025-08-15' and `ec_flash_sale_products`.`quantity` > sold order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": [3, "published", "2025-08-15"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 183}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 321}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 24, "namespace": null, "name": "platform/plugins/hyperpay/src/Http/Controllers/HyperPayController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\hyperpay\\src\\Http\\Controllers\\HyperPayController.php", "line": 76}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.499176, "duration": 0.0144, "duration_str": "14.4ms", "memory": 0, "memory_str": null, "filename": "OrderHelper.php:183", "source": {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 183}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FOrderHelper.php&line=183", "ajax": false, "filename": "OrderHelper.php", "line": "183"}, "connection": "martfury", "explain": null, "start_percent": 91.317, "width_percent": 8.683}]}, "models": {"data": {"Botble\\Ecommerce\\Models\\Product": {"retrieved": 11, "updated": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\ProductVariation": {"retrieved": 9, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProductVariation.php&line=1", "ajax": false, "filename": "ProductVariation.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Order": {"retrieved": 4, "updated": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FOrder.php&line=1", "ajax": false, "filename": "Order.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Currency": {"retrieved": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "Botble\\Payment\\Models\\Payment": {"created": 1, "retrieved": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fpayment%2Fsrc%2FModels%2FPayment.php&line=1", "ajax": false, "filename": "Payment.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\OrderAddress": {"retrieved": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FOrderAddress.php&line=1", "ajax": false, "filename": "OrderAddress.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\OrderProduct": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FOrderProduct.php&line=1", "ajax": false, "filename": "OrderProduct.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\InvoiceItem": {"created": 1, "retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FInvoiceItem.php&line=1", "ajax": false, "filename": "InvoiceItem.php", "line": "?"}}, "Botble\\Language\\Models\\Language": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Flanguage%2Fsrc%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "Botble\\Marketplace\\Models\\Store": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FModels%2FStore.php&line=1", "ajax": false, "filename": "Store.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\ShippingRule": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FShippingRule.php&line=1", "ajax": false, "filename": "ShippingRule.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\OrderHistory": {"created": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FOrderHistory.php&line=1", "ajax": false, "filename": "OrderHistory.php", "line": "?"}}, "Botble\\ACL\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Invoice": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FInvoice.php&line=1", "ajax": false, "filename": "Invoice.php", "line": "?"}}, "Botble\\Base\\Models\\AdminNotification": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FAdminNotification.php&line=1", "ajax": false, "filename": "AdminNotification.php", "line": "?"}}, "Botble\\Slug\\Models\\Slug": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fslug%2Fsrc%2FModels%2FSlug.php&line=1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Shipment": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FShipment.php&line=1", "ajax": false, "filename": "Shipment.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Customer": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "FriendsOfBotble\\EmailLog\\Models\\EmailLog": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Ffob-email-log%2Fsrc%2FModels%2FEmailLog.php&line=1", "ajax": false, "filename": "EmailLog.php", "line": "?"}}}, "count": 61, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 50, "created": 7, "updated": 4}}, "symfonymailer_mails": {"count": 1, "mails": [{"to": ["<EMAIL>"], "subject": "New order(s) at MartFury", "headers": "From: Example <<EMAIL>>\r\nTo: g<PERSON><PERSON><PERSON>@example.org\r\nSubject: New order(s) at MartFury\r\n", "body": null, "html": "<!doctype html>\n<html lang=\"en\">\n\n<head>\n    <meta charset=\"UTF-8\">\n    <title>MartFury</title>\n</head>\n\n<body class=\"bb-bg-body\" dir=\"ltr\" style=\"margin: 0; padding: 0; font-size: 14px; line-height: 171.4285714286%; mso-line-height-rule: exactly; color: #3A4859; width: 100%; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale; -webkit-font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11'; font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11'; font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; background-color: #f6f7f9;\">\n\n<center>\n    <table class=\"bb-main bb-bg-body\" width=\"100%\" cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; background-color: #f6f7f9;\">\n        <tbody>\n            <tr>\n                <td align=\"center\" valign=\"top\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;\">\n                    <table class=\"bb-wrap\" cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; max-width: 640px; text-align: left; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n                        <tbody>\n                            <tr>\n                                <td class=\"bb-p-sm\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 8px;\">\n                                    <table cellpadding=\"0\" cellspacing=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n                                        <tbody>\n                                            <tr>\n                                                <td class=\"bb-py-lg\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-top: 24px; padding-bottom: 24px;\">\n                                                    <table cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n                                                        <tbody>\n                                                            <tr>\n                                                                <td class=\"bb-text-left\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: left;\">\n                                                                    <a href=\"https://martfury.gc\" style=\"color: #206bc4; text-decoration: none;\">\n                                                                        <img class=\"bb-logo\" src=\"https://martfury.gc/storage/general/logo.png\" alt=\"MartFury\" style=\"line-height: 100%; outline: none; text-decoration: none; vertical-align: baseline; font-size: 0; border: 0 none; max-height: 40px;\">\n                                                                    </a>\n                                                                </td>\n                                                                <td class=\"bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right;\">\n                                                                    2025-08-15 15:02:49\n                                                                </td>\n                                                            </tr>\n                                                        </tbody>\n                                                    </table>\n                                                </td>\n                                            </tr>\n                                        </tbody>\n                                    </table>\n\n\n<div class=\"bb-main-content\">\n    <table class=\"bb-box\" cellpadding=\"0\" cellspacing=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; background: #ffffff; border-radius: 4px; -webkit-box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05); box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05); border: 1px solid #dce0e5; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n        <tbody>\n            <tr>\n                <td class=\"bb-content bb-pb-0\" align=\"center\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; padding-bottom: 0;\">\n                    <table class=\"bb-icon bb-icon-lg bb-bg-blue\" cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 0; border-radius: 50%; background: #edeef0; line-height: 100%; font-weight: 300; border-collapse: separate; text-align: center; width: 72px; height: 72px; font-size: 48px; background-color: #206bc4; color: #ffffff; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n                        <tbody>\n                            <tr>\n                                <td valign=\"middle\" align=\"center\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;\">\n                                    <img src=\"https://martfury.gc/vendor/core/core/base/images/email-icons/shopping-cart.png\" class=\"bb-va-middle\" width=\"40\" height=\"40\" alt=\"Icon\" style=\"border: 0 none; line-height: 100%; outline: none; text-decoration: none; font-size: 0; vertical-align: middle; display: block; width: 40px; height: 40px;\">\n                                </td>\n                            </tr>\n                        </tbody>\n                    </table>\n                    <h1 class=\"bb-text-center bb-m-0 bb-mt-md\" style=\"font-weight: 600; color: #232b42; font-size: 28px; line-height: 130%; text-align: center; margin: 0; margin-top: 16px;\">Order successfully!</h1>\n                </td>\n            </tr>\n            <tr>\n                <td class=\"bb-content\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px;\">\n                    <div>Dear GoPro,</div>\n                    <div>You got a new order on MartFury!</div>\n                </td>\n            </tr>\n            <tr>\n                <td class=\"bb-content bb-pt-0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; padding-top: 0;\">\n                    <table class=\"bb-row bb-mb-md\" cellpadding=\"0\" cellspacing=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; table-layout: fixed; margin-bottom: 16px; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n                        <tbody>\n                            <tr>\n                                <td class=\"bb-bb-col\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;\">\n                                    <h4 class=\"bb-m-0\" style=\"font-weight: 600; color: #232b42; font-size: 16px; margin: 0;\">Customer Information</h4>\n                                    <div>Name: <strong style=\"font-weight: 600;\">Ishtiaq Ahmed</strong>\n</div>\n                                                                        <div>Phone: <strong style=\"font-weight: 600;\">+923147552550</strong>\n</div>\n                                                                                                                                                <div>Address: <strong style=\"font-weight: 600;\">test, test, Test, PK</strong>\n</div>\n                                                                    </td>\n                            </tr>\n                        </tbody>\n                    </table>\n                </td>\n            </tr>\n            <tr>\n                <td class=\"bb-content bb-pt-0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; padding-top: 0;\">\n                    <h4 style=\"font-weight: 600; margin: 0 0 0.5em; color: #232b42; font-size: 16px;\">Here's what you ordered:</h4>\n                    <a class=\"button button-blue\" href=\"https://martfury.gc/orders/tracking?order_id=%2310000047&amp;email=rajaishtiaq6%40gmail.com\" style=\"color: #206bc4; text-decoration: none;\">View order</a>\n    or <a href=\"https://martfury.gc\" style=\"color: #206bc4; text-decoration: none;\">Go to our shop</a>\n\n    <br>\n\n<table class=\"bb-table\" cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n    <thead>\n        <tr>\n            <th colspan=\"2\" style=\"text-transform: uppercase; font-weight: 600; color: #667382; font-size: 12px; padding: 0 0 4px 0; padding-left: 0;\"></th>\n            <th style=\"text-transform: uppercase; font-weight: 600; color: #667382; font-size: 12px; padding: 0 0 4px 0;\">Quantity</th>\n            <th class=\"bb-text-right\" style=\"text-align: right; text-transform: uppercase; font-weight: 600; color: #667382; font-size: 12px; padding: 0 0 4px 0; padding-right: 0;\">Price</th>\n        </tr>\n    </thead>\n\n    <tbody>\n                <tr>\n            <td class=\"bb-pr-0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-right: 0; padding: 4px 0; padding-left: 0;\">\n                <a href=\"\" style=\"color: #206bc4; text-decoration: none;\">\n                    <img src=\"https://martfury.gc/storage/products/3-150x150.jpg\" class=\" bb-rounded\" width=\"64\" height=\"64\" alt=\"\" style=\"line-height: 100%; outline: none; text-decoration: none; vertical-align: baseline; font-size: 0; border: 0 none; border-radius: 4px;\">\n                </a>\n            </td>\n            <td class=\"bb-pl-md bb-w-100p\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; width: 100%; padding-left: 16px !important; padding: 4px 0;\">\n                <strong style=\"font-weight: 600;\">Beat Headphone</strong><br>\n                                    <span class=\"bb-text-muted\" style=\"color: #667382;\">(Color: Green, Size: L)</span>\n                \n                            </td>\n            <td class=\"bb-text-center\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: center; padding: 4px 0;\">x 7</td>\n            <td class=\"bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; padding: 4px 0; padding-right: 0;\">SAR20.00</td>\n        </tr>\n    \n                                    <tr>\n                    <td colspan=\"2\" class=\"bb-border-top bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; border-top: 1px solid #dce0e5; padding: 4px 0; padding-left: 0;\">Subtotal</td>\n                    <td colspan=\"2\" class=\"bb-border-top bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; border-top: 1px solid #dce0e5; padding: 4px 0; padding-right: 0;\">SAR140.00</td>\n                </tr>\n            \n            \n                            <tr>\n                    <td colspan=\"2\" class=\"bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; padding: 4px 0; padding-left: 0;\">Tax</td>\n                    <td colspan=\"2\" class=\"bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; padding: 4px 0; padding-right: 0;\">SAR14.00</td>\n                </tr>\n            \n            \n                        <tr>\n                <td colspan=\"2\" class=\"bb-text-right bb-font-strong bb-h3 bb-m-0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; color: #232b42; font-size: 20px; line-height: 130%; text-align: right; font-weight: 600; margin: 0; padding: 4px 0; padding-left: 0;\">Total</td>\n                <td colspan=\"2\" class=\"bb-font-strong bb-h3 bb-m-0 bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; color: #232b42; font-size: 20px; line-height: 130%; text-align: right; font-weight: 600; margin: 0; padding: 4px 0; padding-right: 0;\">SAR154.00</td>\n            </tr>\n            </tbody>\n</table>\n\n\n                                    </td>\n            </tr>\n            <tr>\n                <td class=\"bb-content bb-border-top\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; border-top: 1px solid #dce0e5;\">\n                    <table class=\"bb-row bb-mb-md\" cellpadding=\"0\" cellspacing=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; table-layout: fixed; margin-bottom: 16px; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n                        <tbody>\n                            <tr>\n                                <td class=\"bb-bb-col\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;\">\n                                    <h4 class=\"bb-m-0\" style=\"font-weight: 600; color: #232b42; font-size: 16px; margin: 0;\">Order number</h4>\n                                    <div>#10000047</div>\n                                </td>\n                                <td class=\"bb-col-spacer\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top; width: 24px;\"></td>\n                                <td class=\"bb-col\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top;\">\n                                    <h4 class=\"bb-mb-0\" style=\"font-weight: 600; margin: 0 0 0.5em; color: #232b42; font-size: 16px; margin-bottom: 0;\">Order date</h4>\n                                    <div>2025-08-15 15:00:08</div>\n                                </td>\n                            </tr>\n                        </tbody>\n                    </table>\n                    <table class=\"bb-row\" cellpadding=\"0\" cellspacing=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; table-layout: fixed; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n                        <tbody>\n                            <tr>\n                                <td class=\"bb-col\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top;\">\n                                                                        <h4 class=\"bb-m-0\" style=\"font-weight: 600; color: #232b42; font-size: 16px; margin: 0;\">Shipping Method</h4>\n                                    <div>\n                                        Local Pickup\n                                    </div>\n                                                                    </td>\n\n                                <td class=\"bb-col-spacer\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top; width: 24px;\"></td>\n                                <td class=\"bb-col\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top;\">\n                                    <h4 class=\"bb-m-0\" style=\"font-weight: 600; color: #232b42; font-size: 16px; margin: 0;\">Payment Method</h4>\n                                    <div>\n                                        HyperPay\n                                    </div>\n                                </td>\n                            </tr>\n                        </tbody>\n                    </table>\n                </td>\n            </tr>\n        </tbody>\n    </table>\n</div>\n\n<table cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n    <tbody>\n        <tr>\n            <td class=\"bb-py-xl\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-top: 48px; padding-bottom: 48px;\">\n                <table class=\"bb-text-center bb-text-muted\" cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; color: #667382; text-align: center; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n                    <tbody>\n                    \n                    <tr>\n                        <td class=\"bb-px-lg\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-right: 24px; padding-left: 24px;\">\n                            © 2025 MartFury. All Rights Reserved.\n                        </td>\n                    </tr>\n\n                                            <tr>\n                            <td class=\"bb-pt-md\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-top: 16px;\">\n                                If you have any questions, feel free to message us at <a href=\"mailto:<EMAIL>\" style=\"color: #206bc4; text-decoration: none;\"><EMAIL></a>.\n                            </td>\n                        </tr>\n                                        </tbody>\n                </table>\n            </td>\n        </tr>\n    </tbody>\n</table>\n</td>\n</tr>\n</tbody>\n</table>\n</td>\n</tr>\n</tbody>\n</table>\n</center>\n</body>\n\n</html>"}]}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "302 Found", "full_url": "https://martfury.gc/payment/hyperpay/callback?id=8FC0BD1D066C3A2AA927BD91EBF97169.uat01-vm-tx03&reso...", "action_name": "payments.hyperpay.callback", "controller_action": "Botble\\HyperPay\\Http\\Controllers\\HyperPayController@callback", "uri": "GET payment/hyperpay/callback", "controller": "Botble\\HyperPay\\Http\\Controllers\\HyperPayController@callback<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fhyperpay%2Fsrc%2FHttp%2FControllers%2FHyperPayController.php&line=59\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Botble\\HyperPay\\Http\\Controllers", "prefix": "/payment/hyperpay", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fhyperpay%2Fsrc%2FHttp%2FControllers%2FHyperPayController.php&line=59\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/plugins/hyperpay/src/Http/Controllers/HyperPayController.php:59-137</a>", "middleware": "web, core", "duration": "12.17s", "peak_memory": "72MB", "response": "Redirect to https://martfury.gc", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-331137541 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"46 characters\">8FC0BD1D066C3A2AA927BD91EBF97169.uat01-vm-tx03</span>\"\n  \"<span class=sf-dump-key>resourcePath</span>\" => \"<span class=sf-dump-str title=\"68 characters\">/v1/checkouts/8FC0BD1D066C3A2AA927BD91EBF97169.uat01-vm-tx03/payment</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-331137541\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2146915727 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2146915727\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-700826520 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">martfury.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"110 characters\">https://martfury.gc/payment/hyperpay/checkout/8FC0BD1D066C3A2AA927BD91EBF97169.uat01-vm-tx03?payment_type=mada</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3215 characters\">cookie_for_consent=1; botble_footprints_cookie=eyJpdiI6Ilp2Q1lFM2s1MkVpWnI2blVTb2FtZWc9PSIsInZhbHVlIjoiU1N3dm1pVUNyMlJieWRscUU2Rkk3dzFSVmsxSFhpU3Bpc0lSQmRvOEl3M0ZwaW42RkFpdUoxV1NOWW52Q2lOWFh1WUVZUFVpWnBGdHBuc2xVWXV6TmJFRUd6VVljQ25vWWlDaVhBczVpNEpTU0NmbytXczNaTzdDdFNCN3U4anoiLCJtYWMiOiJjZDdlODUyY2JjNzc4MjNmNmEwMTc4YzA3ODFlM2MyNmQ0MzhjMGNkMTA2ZDY3MjYwYjQ0MmMzMjA1NDAzMjhjIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6ImNlT05BQnNiOExZUk94aE9NbHNkYWc9PSIsInZhbHVlIjoiTWVoMVdkQWFROThBdm9VWUV1cWd6TFlLUFR2emErYW5PWWNDUk5ycVBWOURCdzVHT0pRaEpET242bzEzSFh0bWlpT0RZZnoxVUd4U0swOGUyV1lteXU0SDJhdG80U0VhdnVHaFVFVUJ0amhRVFhTOEFMWUpnYmVmamdqQ2NrNERDMW1lbElBeWNYN2NHQTAyZm9PTk5FKzIvT3VjRjU3ZU40bjJVNGI5WmdSTHBoZTZrUnp0RE9MbEg0aytxMUVlNTlJTW9lZnJGVWxDbHI3Q3poR2RDbzVISnB1Y0pCSXRDb0cyYmFCRUVRMkl2RkxUQlpoanhhR2pINW1ZdkZmS091REtuSnRlNjB6ekR2clEweFZlMjh1M0dOanZQdTlpcHdTRlg2YzJxQm92VHRPdG9SekNPSEJVa0VKcjlEZjlJenVONVVwZTJnRjQ4N1JZKzhEd2pnZHJsUkM3eXVaMmhYblhRZmVoQlk0YTNSOFFYUEhqYi9hQzZIWkloa0llWGNaMEFYVFFmVGhnYnZJcnJIUHh0Z2pzREpUb2ptM1BnU25wV3M3eWF2RUl5UVdwU0UzUDdCZkErMXlCREdRSXExTzVVK1kwL0Jmd0laTGZ1VE9sc1BMbG5lVnVLNytLaEs1YjJpTEllK3Qwd3VUbHBEL3JLNlo0VEMwN2UxakRKK2hxUE9QTmI5TDdqeW03WkgwZjVnPT0iLCJtYWMiOiI1ZmIxN2YwMTBlNWZjMTY0MTY0NDMzZDUyZmZjNmQ5ZWZkYjk0NTYxZjg0ZTk4ODAzNTVlYzJhYWJmN2I0NmVhIiwidGFnIjoiIn0%3D; shortcode_cache_suggestion_dismissed=1; widget_cache_suggestion_dismissed=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Imtmd01aTUlkUmlvaEJqSmRGMnJ5aXc9PSIsInZhbHVlIjoiMGVSOUY2VWtsSitnWmpMdXIxbHd1WC9PeGRISXNyNXQwM1dJeXBwUVZweUphTlViRWNWT09zVXpYUVpNWm1nTEJXeDlxQmlZTDUrOUNZdk9POERqSzQ0V0N4NXhGMFlyd1hPa2xKVGZjTjhWSWFQbWpRcmdGT1ViRFEyQ2IrMjUwMXNtOW1hbWM0MU5xR2UwdmkycTUyTlNibmRhdENJeXZnYkVlQlFlSkZYVm9BZk43WXU0RUhtOWt5NFZNVTJlN3BSMDRkaUd2Z0RwNzBqei9RUDZRVVhpWm5idEpKK3dJTWJ2dVg1OVQ0Zz0iLCJtYWMiOiJlZTQ5Mzc4NzBkNjk4ODUxYjJmNzI5ZDljM2E1OGY4NzM4YzUyYzc1MGFhYzgwMGY2NjJmM2JlN2Q5OWUwODJmIiwidGFnIjoiIn0%3D; botble_cookie_newsletter=1; remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpibjFnQzQrTDFHeWZNOTFoK1F4R2c9PSIsInZhbHVlIjoiWlBCZ2pyclRiZU9razRVZjF3YU1JVjl3ZlcrTVZKVW9JVlplS2lQOStvUVdjM0RCeWx3a2YrOEpicWtScGdBcXJtY1hZZ3BJOTdhR2dHdmRPYXB0VlZjcG5Mb1hjYWc3TnNxT1Z3RVJZaEM0VXRsSjMvWHp1VTIzR3gvTmJtbUh1dlhiSFhjZzZqMU1JVUVjRVQwZURXeUt5eGFuclh4TVFlSkdyeFQzMVkvdGhRWVlsenZPSG8rNWRNUVU5RG9KYUh6S1YwWmVwOUtCTWd5ckROdnpkZ3gxeTJ1WWdpR29mMUJ6TUpRUTdHQT0iLCJtYWMiOiI3M2Q5NDM1Y2Q2ZWU1NTMzNzczMjdhYWM3ZmRjNTJhZTFjNThiNTFlOTllZGZhM2RlOTIwNWI0OWI1M2U2MGI0IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ik5DblUyVENmdWhPVE05VmV1OTFEakE9PSIsInZhbHVlIjoiZUJMUmRDYmdaS21ZYXJLRmF4MFp1WVpRZk1VVVJ0S09mUGNhc2RreHBXdkVpcG8yaVdMRVlwejJ3aWE2R3FxbjRwb3JLSWIrTDBuZjRhY1BJQW5SejJsODNmUFpDRDdwcW9Td3hVcEVhYmZScjByMFFrRUd0UWxTblFBaFNpaUMiLCJtYWMiOiI1NjdjNjFjMGEyZDIzMDM0M2VlMzdlNDc0ZTdjOWQ5MTkzY2UxZDE0MGY5YzdhZmI2NTQ3MjQ4NTM0ZjkxMmEyIiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6IlNXVXA3cHNnbmFSN2cwNmg5bFV4NVE9PSIsInZhbHVlIjoic2FYRjRHS05zUlFLV3VGbHFzT3FJUTZqMjJxYVJLaFJyQWRERU1LdFV6NDYrVkJnR2J2bDhodUFEbXZmYmtIL1Q3QWpieHpDa1p0VXVmOWRTb3ljNHJzOWQvWmM2R3BvRnhieWpLNzFDbVB6TzNNMlREOXVoc0hua1FCUWhNNTMiLCJtYWMiOiJiMTViN2UzZGYyY2MwZjMwMDQ5NWU5ZmYyOGZiNzJhY2Y1NmFkZmJkNjM1NDg4MWYxNzEzN2Y2YzdjODZhYTAwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-700826520\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1078672222 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5d4a7afb407de91a3f785dca41f29624d4035861</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"347 characters\">{&quot;footprint&quot;:&quot;5d4a7afb407de91a3f785dca41f29624d4035861&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;martfury.gc&quot;,&quot;landing_page&quot;:&quot;\\/&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:&quot;http:\\/\\/localhost\\/&quot;,&quot;referrer_domain&quot;:&quot;localhost&quot;}</span>\"\n  \"<span class=sf-dump-key>shortcode_cache_suggestion_dismissed</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>widget_cache_suggestion_dismissed</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|mr2hzXolFPMrElNypPbMsV1VAiAKgY80Q2lBNyezSZXf4Bow8DqaNYnj6c6c|$2y$12$faChkoM7UV9t8mCoo6oJFOLYCOlwKR0CpAW2E1NPY0Bj1MIeVQw92</span>\"\n  \"<span class=sf-dump-key>botble_cookie_newsletter</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">2|rEY2fnBoxjVikI7FHOoHpfsUL6HmShbqbiBbMajXCGvqp4DJneBXZNsMDEhJ|$2y$12$mAIYinlq8PEmipS0gyL5O.c3YY4axUf.8ExV94mmLdgzhultxp2KS</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6AiUEtIqMruJngQhfx6V5J3mOZ7KIDVH5jSHUxUL</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IlGMU8ikcYH0PyBAGquP1yLFBc9E8t7YmNqOCnbe</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1078672222\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2034814071 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 15 Aug 2025 15:02:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">https://martfury.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2034814071\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1282885433 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6AiUEtIqMruJngQhfx6V5J3mOZ7KIDVH5jSHUxUL</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>math-captcha</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>first</span>\" => <span class=sf-dump-num>4</span>\n    \"<span class=sf-dump-key>second</span>\" => <span class=sf-dump-num>8</span>\n    \"<span class=sf-dump-key>operand</span>\" => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"110 characters\">https://martfury.gc/payment/hyperpay/checkout/8FC0BD1D066C3A2AA927BD91EBF97169.uat01-vm-tx03?payment_type=mada</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">success_msg</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>cart</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>cart_updated_at</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1755270129\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1755270129</span></span> {<a class=sf-dump-ref href=#sf-dump-1282885433-ref24647 title=\"3 occurrences\">#4647</a><samp data-depth=3 id=sf-dump-1282885433-ref24647 class=sf-dump-compact>\n      #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n      #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n      #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">00000000000012270000000000000000</span>\"\n      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n        <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n      </samp>]\n      #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n      <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Friday, August 15, 2025\n- 00:00:43.396756 from now\nDST Off\">2025-08-15 15:02:09.138999 UTC (+00:00)</span>\n    </samp>}\n  </samp>]\n  \"<span class=sf-dump-key>tracked_start_checkout</span>\" => \"<span class=sf-dump-str title=\"32 characters\">2bbdbd08f9ec9fc36d8b3e7ced0cf369</span>\"\n  \"<span class=sf-dump-key>2162c5409405ee8ecb45d303ef2ad5a8</span>\" => <span class=sf-dump-note>array:15</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>promotion_discount_amount</span>\" => <span class=sf-dump-num>0</span>\n    \"<span class=sf-dump-key>billing_address_same_as_shipping_address</span>\" => <span class=sf-dump-const>true</span>\n    \"<span class=sf-dump-key>billing_address</span>\" => []\n    \"<span class=sf-dump-key>marketplace</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:17</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>billing_address_same_as_shipping_address</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>billing_address</span>\" => []\n        \"<span class=sf-dump-key>created_order</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1755269989\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1755269989</span></span> {<a class=sf-dump-ref href=#sf-dump-1282885433-ref24653 title=\"2 occurrences\">#4653</a><samp data-depth=5 id=sf-dump-1282885433-ref24653 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">000000000000122d0000000000000000</span>\"\n          -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n            <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n          <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Friday, August 15, 2025\n- 00:03:02.876591 from now\nDST Off\">2025-08-15 14:59:49.659652 UTC (+00:00)</span>\n        </samp>}\n        \"<span class=sf-dump-key>created_order_id</span>\" => <span class=sf-dump-num>47</span>\n        \"<span class=sf-dump-key>is_save_order_shipping_address</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>created_order_product</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1755269989\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1755269989</span></span> {<a class=sf-dump-ref href=#sf-dump-1282885433-ref24653 title=\"2 occurrences\">#4653</a>}\n        \"<span class=sf-dump-key>coupon_discount_amount</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>applied_coupon_code</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>is_free_shipping</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>promotion_discount_amount</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>shipping_method</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n        \"<span class=sf-dump-key>shipping_option</span>\" => \"<span class=sf-dump-str>3</span>\"\n        \"<span class=sf-dump-key>shipping_amount</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"\n        \"<span class=sf-dump-key>shipping</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>default</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Local Pickup</span>\"\n              \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"\n            </samp>]\n            <span class=sf-dump-key>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Flat Rate</span>\"\n              \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">20.00</span>\"\n            </samp>]\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>default_shipping_method</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n        \"<span class=sf-dump-key>default_shipping_option</span>\" => \"<span class=sf-dump-str>3</span>\"\n        \"<span class=sf-dump-key>is_available_shipping</span>\" => <span class=sf-dump-const>true</span>\n      </samp>]\n      <span class=sf-dump-key>1</span> => <span class=sf-dump-note>array:27</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Ishtiaq Ahmed</span>\"\n        \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"22 characters\"><EMAIL></span>\"\n        \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"13 characters\">+923147552550</span>\"\n        \"<span class=sf-dump-key>address</span>\" => \"<span class=sf-dump-str title=\"4 characters\">test</span>\"\n        \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"2 characters\">PK</span>\"\n        \"<span class=sf-dump-key>state</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Test</span>\"\n        \"<span class=sf-dump-key>city</span>\" => \"<span class=sf-dump-str title=\"4 characters\">test</span>\"\n        \"<span class=sf-dump-key>zip_code</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>created_order</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1755270129\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1755270129</span></span> {<a class=sf-dump-ref href=#sf-dump-1282885433-ref24647 title=\"3 occurrences\">#4647</a>}\n        \"<span class=sf-dump-key>created_order_id</span>\" => <span class=sf-dump-num>48</span>\n        \"<span class=sf-dump-key>is_save_order_shipping_address</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>created_order_address</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>created_order_address_id</span>\" => <span class=sf-dump-num>42</span>\n        \"<span class=sf-dump-key>created_order_product</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1755270129\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1755270129</span></span> {<a class=sf-dump-ref href=#sf-dump-1282885433-ref24647 title=\"3 occurrences\">#4647</a>}\n        \"<span class=sf-dump-key>billing_address_same_as_shipping_address</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>billing_address</span>\" => []\n        \"<span class=sf-dump-key>coupon_discount_amount</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>applied_coupon_code</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>is_free_shipping</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>promotion_discount_amount</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>shipping_method</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n        \"<span class=sf-dump-key>shipping_option</span>\" => \"<span class=sf-dump-str>3</span>\"\n        \"<span class=sf-dump-key>shipping_amount</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"\n        \"<span class=sf-dump-key>shipping</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>default</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Local Pickup</span>\"\n              \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"\n            </samp>]\n            <span class=sf-dump-key>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Flat Rate</span>\"\n              \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">20.00</span>\"\n            </samp>]\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>default_shipping_method</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n        \"<span class=sf-dump-key>default_shipping_option</span>\" => \"<span class=sf-dump-str>3</span>\"\n        \"<span class=sf-dump-key>is_available_shipping</span>\" => <span class=sf-dump-const>true</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Ishtiaq Ahmed</span>\"\n    \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"22 characters\"><EMAIL></span>\"\n    \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"13 characters\">+923147552550</span>\"\n    \"<span class=sf-dump-key>address</span>\" => \"<span class=sf-dump-str title=\"4 characters\">test</span>\"\n    \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"2 characters\">PK</span>\"\n    \"<span class=sf-dump-key>state</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Test</span>\"\n    \"<span class=sf-dump-key>city</span>\" => \"<span class=sf-dump-str title=\"4 characters\">test</span>\"\n    \"<span class=sf-dump-key>zip_code</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>shipping_method</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Ecommerce\\Enums\\ShippingMethodEnum\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Ecommerce\\Enums</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ShippingMethodEnum</span></span> {<a class=sf-dump-ref>#4654</a><samp data-depth=3 class=sf-dump-compact>\n      #<span class=sf-dump-protected title=\"Protected property\">value</span>: \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n    </samp>}\n    \"<span class=sf-dump-key>shipping_option</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>shipping_amount</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>selected_payment_method</span>\" => \"<span class=sf-dump-str title=\"8 characters\">hyperpay</span>\"\n  \"<span class=sf-dump-key>order_id</span>\" => <span class=sf-dump-num>48</span>\n  \"<span class=sf-dump-key>success_msg</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Payment completed successfully!</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1282885433\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "https://martfury.gc/payment/hyperpay/callback?id=8FC0BD1D066C3A2AA927BD91EBF97169.uat01-vm-tx03&reso...", "action_name": "payments.hyperpay.callback", "controller_action": "Botble\\HyperPay\\Http\\Controllers\\HyperPayController@callback"}, "badge": "302 Found"}}