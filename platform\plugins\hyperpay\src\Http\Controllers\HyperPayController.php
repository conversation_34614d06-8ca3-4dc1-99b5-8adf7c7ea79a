<?php

namespace Bo<PERSON><PERSON>\HyperPay\Http\Controllers;

use Bo<PERSON>ble\Base\Http\Controllers\BaseController;
use <PERSON><PERSON>ble\HyperPay\Services\Gateways\HyperPayPaymentService;
use Bo<PERSON>ble\HyperPay\Supports\HyperPayHelper;
use Bo<PERSON>ble\Payment\Enums\PaymentStatusEnum;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;

class HyperPayController extends BaseController
{
    protected HyperPayPaymentService $hyperPayService;

    public function __construct(HyperPayPaymentService $hyperPayService)
    {
        $this->hyperPayService = $hyperPayService;
    }

    public function checkout(Request $request, string $checkoutId)
    {
        try {
            $scriptUrl = $this->hyperPayService->getCheckoutUrl($checkoutId);
            $shopperResultUrl = $this->hyperPayService->getShopperResultUrl();

            // Get payment type from session or default to visa
            $paymentType = session('hyperpay_payment_type', 'visa');

            // Map payment types to brands for the widget
            $paymentBrands = match($paymentType) {
                'visa' => 'VISA MASTER',
                'mada' => 'MADA',
                'amex' => 'AMEX',
                'applepay' => 'APPLEPAY',
                default => 'VISA MASTER'
            };

            return view('plugins/hyperpay::checkout', [
                'checkoutId' => $checkoutId,
                'scriptUrl' => $scriptUrl,
                'shopperResultUrl' => $shopperResultUrl,
                'paymentBrands' => $paymentBrands,
            ]);
        } catch (Exception $e) {
            Log::error('HyperPay Checkout Error', [
                'checkout_id' => $checkoutId,
                'error' => $e->getMessage()
            ]);

            return view('plugins/hyperpay::checkout', [
                'error' => $e->getMessage()
            ]);
        }
    }

    public function callback(Request $request)
    {
        try {
            $resourcePath = $request->get('resourcePath');
            $checkoutId = $request->get('id');

            if (!$resourcePath || !$checkoutId) {
                throw new Exception('Missing required parameters');
            }

            Log::info('HyperPay Callback Received', [
                'resource_path' => $resourcePath,
                'checkout_id' => $checkoutId,
                'all_params' => $request->all()
            ]);

            // Process the payment result
            $result = $this->hyperPayService->afterMakePayment([
                'resource_path' => $resourcePath,
                'checkout_id' => $checkoutId,
            ]);

            // Get checkout token from session or order
            $orderId = session('hyperpay_order_id');
            $checkoutToken = null;

            // Handle case where orderId might be an array
            if ($orderId) {
                if (is_array($orderId)) {
                    $orderId = reset($orderId); // Get first element
                }

                // Try to find the order and get its token
                $order = \Botble\Ecommerce\Models\Order::find($orderId);
                if ($order) {
                    $checkoutToken = $order->token;
                }
            }

            Log::info('HyperPay Checkout Token Retrieved', [
                'order_id' => $orderId,
                'checkout_token' => $checkoutToken,
                'payment_result' => $result
            ]);

            if ($result) {
                // Payment was processed successfully
                Log::info('HyperPay Payment Success', [
                    'charge_id' => $result,
                    'order_id' => $orderId,
                    'checkout_token' => $checkoutToken
                ]);

                if ($checkoutToken) {
                    return redirect()->route('public.checkout.success', $checkoutToken)
                        ->with('success_msg', trans('plugins/hyperpay::hyperpay.payment_success'));
                } else {
                    Log::warning('HyperPay Success but no checkout token', [
                        'order_id' => $orderId,
                        'charge_id' => $result
                    ]);
                    return redirect()->to('/')
                        ->with('success_msg', trans('plugins/hyperpay::hyperpay.payment_success'));
                }
            } else {
                // Payment failed
                $errorMessage = $this->hyperPayService->getErrorMessage() ?: trans('plugins/hyperpay::hyperpay.payment_failed');

                Log::error('HyperPay Payment Failed', [
                    'order_id' => $orderId,
                    'checkout_token' => $checkoutToken,
                    'error_message' => $errorMessage,
                    'resource_path' => $resourcePath,
                    'checkout_id' => $checkoutId
                ]);

                if ($checkoutToken) {
                    return redirect()->route('public.checkout.information', $checkoutToken)
                        ->with('error_msg', $errorMessage);
                } else {
                    return redirect()->to('/')
                        ->with('error_msg', $errorMessage);
                }
            }
        } catch (Exception $e) {
            Log::error('HyperPay Callback Error', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);

            // Try to get checkout token for error redirect
            $orderId = session('hyperpay_order_id');
            $checkoutToken = null;

            // Handle case where orderId might be an array
            if ($orderId) {
                if (is_array($orderId)) {
                    $orderId = reset($orderId); // Get first element
                }

                $order = \Botble\Ecommerce\Models\Order::find($orderId);
                if ($order) {
                    $checkoutToken = $order->token;
                }
            }

            if ($checkoutToken) {
                return redirect()->route('public.checkout.information', $checkoutToken)
                    ->with('error_msg', trans('plugins/hyperpay::hyperpay.payment_error'));
            } else {
                return redirect()->to('/')
                    ->with('error_msg', trans('plugins/hyperpay::hyperpay.payment_error'));
            }
        }
    }

    public function webhook(Request $request)
    {
        try {
            $webhookSecret = get_payment_setting('webhook_secret', HYPERPAY_PAYMENT_METHOD_NAME);

            // Verify webhook signature if secret is configured
            if ($webhookSecret) {
                $signature = $request->header('X-HyperPay-Signature');
                $payload = $request->getContent();

                $expectedSignature = hash_hmac('sha256', $payload, $webhookSecret);

                if (!hash_equals($expectedSignature, $signature)) {
                    Log::warning('HyperPay Webhook Signature Mismatch', [
                        'expected' => $expectedSignature,
                        'received' => $signature
                    ]);

                    return response('Unauthorized', 401);
                }
            }

            $data = $request->json()->all();

            Log::info('HyperPay Webhook Received', $data);

            // Process webhook data
            if (isset($data['type']) && isset($data['payload'])) {
                $this->processWebhookEvent($data['type'], $data['payload']);
            }

            return response('OK', 200);
        } catch (Exception $e) {
            Log::error('HyperPay Webhook Error', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);

            return response('Error', 500);
        }
    }

    public function status(Request $request, string $checkoutId)
    {
        try {
            $resourcePath = $request->get('resourcePath');

            if (!$resourcePath) {
                return response()->json([
                    'success' => false,
                    'message' => 'Missing resource path'
                ], 400);
            }

            $paymentResult = $this->hyperPayService->getPaymentStatus($resourcePath, $checkoutId);

            $status = 'failed';
            if (isset($paymentResult['result']['code'])) {
                $resultCode = $paymentResult['result']['code'];

                if ($this->hyperPayService->isSuccessfulPayment($resultCode)) {
                    $status = 'success';
                } elseif ($this->hyperPayService->isPendingPayment($resultCode)) {
                    $status = 'pending';
                }
            }

            return response()->json([
                'success' => true,
                'status' => $status,
                'result_code' => $paymentResult['result']['code'] ?? null,
                'result_description' => $paymentResult['result']['description'] ?? null,
                'data' => $paymentResult
            ]);
        } catch (Exception $e) {
            Log::error('HyperPay Status Check Error', [
                'checkout_id' => $checkoutId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    protected function processWebhookEvent(string $eventType, array $payload): void
    {
        switch ($eventType) {
            case 'payment.completed':
                $this->handlePaymentCompleted($payload);
                break;

            case 'payment.failed':
                $this->handlePaymentFailed($payload);
                break;

            case 'payment.pending':
                $this->handlePaymentPending($payload);
                break;

            default:
                Log::info('HyperPay Unhandled Webhook Event', [
                    'type' => $eventType,
                    'payload' => $payload
                ]);
        }
    }

    protected function handlePaymentCompleted(array $payload): void
    {
        // Handle successful payment webhook
        Log::info('HyperPay Payment Completed Webhook', $payload);

        // Update payment status in database if needed
        // This would typically involve finding the payment record and updating its status
    }

    protected function handlePaymentFailed(array $payload): void
    {
        // Handle failed payment webhook
        Log::info('HyperPay Payment Failed Webhook', $payload);

        // Update payment status and possibly restock inventory
    }

    protected function handlePaymentPending(array $payload): void
    {
        // Handle pending payment webhook
        Log::info('HyperPay Payment Pending Webhook', $payload);

        // Update payment status to pending
    }

    /**
     * Debug configuration (only available in debug mode)
     */
    public function debugConfig()
    {
        if (!config('app.debug')) {
            abort(404);
        }

        $config = HyperPayHelper::debugConfiguration();

        return response()->json([
            'configuration' => $config,
            'status' => HyperPayHelper::getConfigurationStatus(),
            'is_available' => $this->hyperPayService->isAvailable(),
        ]);
    }

    /**
     * Test API connection (only available in debug mode)
     */
    public function testApi()
    {
        if (!config('app.debug')) {
            abort(404);
        }

        $result = HyperPayHelper::testApiConnection();

        return response()->json($result);
    }

    /**
     * Test checkout creation (only available in debug mode)
     */
    public function testCheckout(Request $request)
    {
        if (!config('app.debug')) {
            abort(404);
        }

        try {
            // Test with minimal required data
            $testData = [
                'amount' => $request->get('amount', 10.00),
                'currency' => $request->get('currency', 'SAR'),
                'order_id' => 'test_' . time(),
                'payment_type' => $request->get('payment_type', 'visa'),
                'customer_email' => '<EMAIL>',
                'customer_name' => 'Test Customer',
                'billing_country' => 'SA',
            ];

            Log::info('Testing HyperPay Checkout with data:', $testData);

            $result = $this->hyperPayService->execute($testData);

            if ($this->hyperPayService->getErrorMessage()) {
                return response()->json([
                    'success' => false,
                    'error' => $this->hyperPayService->getErrorMessage(),
                    'test_data' => $testData
                ]);
            }

            return response()->json([
                'success' => true,
                'checkout_id' => $result,
                'test_data' => $testData,
                'message' => 'Checkout created successfully!'
            ]);

        } catch (Exception $e) {
            Log::error('Test Checkout Error: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
}
