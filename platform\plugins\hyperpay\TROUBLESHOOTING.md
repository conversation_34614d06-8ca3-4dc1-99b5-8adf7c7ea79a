# HyperPay Troubleshooting Guide

## Common Error: "invalid or missing parameter"

This error usually occurs due to one of the following issues:

### 1. **Check Your Configuration**

First, verify your HyperPay settings:

**Debug URL (only works if APP_DEBUG=true):**
```
https://yourdomain.com/payment/hyperpay/debug/config
```

**Test API Connection:**
```
https://yourdomain.com/payment/hyperpay/debug/test-api
```

### 2. **Common Configuration Issues**

#### **Access Token Format**
- Make sure your access token is in the correct format
- It should look like: `OGFjOWE0Yzc2N2ExNWMwYjAxNjdkYmYzZDRhYjZmYmJ8ekpycmNNSEY2dw==`
- Remove any extra spaces or characters

#### **Entity ID Format**
- Entity IDs should be exactly 32 characters
- Example: `8ac7a4c767a15c0b0167dbf4171c6fbf`
- Make sure you're using the correct entity ID for each payment method

#### **Sandbox vs Live Mode**
- If testing, make sure "Sandbox Mode" is enabled
- Use sandbox credentials for testing
- Use live credentials only for production

### 3. **Step-by-Step Debugging**

#### **Step 1: Verify Basic Configuration**
1. Go to Admin Panel → Payments → Payment Methods
2. Find HyperPay section
3. Check that these fields are filled:
   - ✅ Access Token (not empty)
   - ✅ Visa/Mastercard Entity ID (32 characters)
   - ✅ Sandbox Mode (enabled for testing)

#### **Step 2: Test with Minimal Configuration**
1. **Only fill these required fields:**
   - Access Token
   - Visa/Mastercard Entity ID
   - Enable Sandbox Mode
2. **Leave these empty for now:**
   - Mada Entity ID
   - American Express Entity ID
   - Apple Pay Entity ID
   - Webhook Secret

#### **Step 3: Check Logs**
Look at your Laravel logs (`storage/logs/laravel.log`) for detailed error information:

```bash
tail -f storage/logs/laravel.log
```

Look for entries like:
- `HyperPay Checkout Request`
- `HyperPay Checkout Response`
- `HyperPay API Error`

### 4. **Common Parameter Issues**

#### **Amount Validation**
- Amount must be greater than 0
- Amount is automatically formatted to 2 decimal places
- Example: `10.50` not `10.5`

#### **Currency Issues**
- Make sure currency is supported: SAR, AED, USD, EUR
- Currency is automatically converted to uppercase

#### **Entity ID Mismatch**
- Visa/Mastercard payments use `visa_entity_id`
- Mada payments use `mada_entity_id`
- Make sure the entity ID matches the payment method selected

### 5. **API Response Codes**

#### **Success Codes**
- `000.200.100` - Checkout created successfully

#### **Common Error Codes**
- `800.400.100` - Invalid or missing parameter
- `800.400.200` - Invalid payment data
- `800.500.100` - System error
- `800.600.100` - Temporary system error

### 6. **Quick Fix Checklist**

**✅ Configuration Checklist:**
- [ ] Access token is correct and complete
- [ ] Entity ID is exactly 32 characters
- [ ] Sandbox mode is enabled for testing
- [ ] Currency is set to SAR (or supported currency)
- [ ] Plugin is activated
- [ ] Payment plugin is activated

**✅ Testing Checklist:**
- [ ] Test with only Visa/Mastercard first
- [ ] Use a simple amount like 10.00
- [ ] Check Laravel logs for detailed errors
- [ ] Try the debug URLs (if APP_DEBUG=true)

### 7. **Manual API Test**

You can test your credentials manually using curl:

```bash
curl -X POST https://eu-test.oppwa.com/v1/checkouts \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "entityId=YOUR_ENTITY_ID" \
  -d "amount=10.00" \
  -d "currency=SAR" \
  -d "paymentType=DB" \
  -d "merchantTransactionId=test_123"
```

Replace:
- `YOUR_ACCESS_TOKEN` with your actual access token
- `YOUR_ENTITY_ID` with your actual entity ID

### 8. **Contact Information**

If you're still having issues:

1. **Check HyperPay Documentation:** https://hyperpay.docs.oppwa.com/
2. **Contact HyperPay Support:** For API-related issues
3. **Check Laravel Logs:** For detailed error information

### 9. **Common Solutions**

#### **Solution 1: Reset Configuration**
1. Clear all HyperPay settings
2. Add only Access Token and Visa Entity ID
3. Enable Sandbox Mode
4. Test with a simple payment

#### **Solution 2: Verify Credentials**
1. Log into your HyperPay merchant portal
2. Verify your access token is active
3. Check that entity IDs are correct
4. Ensure your account is properly configured

#### **Solution 3: Check Network**
1. Ensure your server can reach HyperPay APIs
2. Check firewall settings
3. Verify SSL certificates are working

Remember: Always test in sandbox mode first before going live!
