# HyperPay Payment Gateway Plugin for Botble CMS

A comprehensive HyperPay payment gateway integration for Botble CMS that supports multiple payment methods including Visa, Mastercard, Mada, American Express, and Apple Pay.

## Features

- ✅ **Multiple Payment Methods**: Visa, Mastercard, Mada, American Express, Apple Pay
- ✅ **3D Secure Authentication**: Enhanced security with 3DS support
- ✅ **Sandbox & Live Modes**: Test in sandbox before going live
- ✅ **Webhook Support**: Real-time payment notifications
- ✅ **Multi-Currency**: Support for SAR, AED, USD, EUR
- ✅ **Responsive Design**: Mobile-friendly payment forms
- ✅ **Error Handling**: Comprehensive error handling and logging
- ✅ **Security**: SSL encryption and signature validation

## Installation

1. **Copy Plugin Files**
   ```bash
   # The plugin files should be placed in:
   platform/plugins/hyperpay/
   ```

2. **Activate the Plugin**
   - Go to Admin Panel → Plugins
   - Find "HyperPay Payment Gateway" and click Activate

3. **Configure Payment Settings**
   - Go to Admin Panel → Payments → Payment Methods
   - Find HyperPay section and configure:
     - Access Token (from HyperPay merchant portal)
     - Entity IDs for each payment method
     - Sandbox mode (enable for testing)
     - 3D Secure settings
     - Webhook secret (optional but recommended)

## Configuration

### Required Settings

1. **Access Token**: Your HyperPay Bearer token from the merchant portal
2. **Visa/Mastercard Entity ID**: Entity ID for Visa and Mastercard payments
3. **Sandbox Mode**: Enable for testing, disable for live transactions

### Optional Settings

1. **Mada Entity ID**: For Saudi local payment method
2. **American Express Entity ID**: For Amex payments
3. **Apple Pay Entity ID**: For Apple Pay payments
4. **Webhook Secret**: For webhook signature verification
5. **3D Secure**: Enable/disable 3D Secure authentication

### HyperPay Account Setup

1. Sign up for a HyperPay merchant account at [hyperpay.com](https://www.hyperpay.com)
2. Complete the onboarding process
3. Obtain your access token from the merchant portal
4. Get entity IDs for each payment method you want to support
5. Configure webhook URLs (optional):
   - Webhook URL: `https://yourdomain.com/payment/hyperpay/webhook`

## Testing

### Running Tests

```bash
# Run the integration tests
php artisan test platform/plugins/hyperpay/tests/HyperPayIntegrationTest.php
```

### Manual Testing

1. **Enable Sandbox Mode** in the plugin settings
2. **Configure Test Credentials** from HyperPay test environment
3. **Test Payment Flow**:
   - Add items to cart
   - Proceed to checkout
   - Select HyperPay as payment method
   - Choose payment type (Visa, Mada, etc.)
   - Complete payment with test card numbers

### Test Card Numbers

Use these test card numbers in sandbox mode:

- **Visa**: ****************
- **Mastercard**: ****************
- **Mada**: ****************
- **American Express**: ***************

**Test Details**:
- Expiry: Any future date
- CVV: Any 3-4 digits
- Name: Any name

## Usage

### Frontend Integration

The plugin automatically integrates with Botble's checkout process:

1. **Payment Method Selection**: HyperPay appears as a payment option
2. **Payment Type Selection**: Users can choose between available payment types
3. **Secure Payment**: Users are redirected to HyperPay's secure payment form
4. **Payment Completion**: Users are redirected back with payment status

### Webhook Handling

The plugin automatically handles webhook notifications for:
- Payment completion
- Payment failure
- Payment pending status

### Error Handling

The plugin includes comprehensive error handling for:
- Configuration errors
- API communication errors
- Payment processing errors
- Webhook validation errors

## API Documentation

### HyperPay API References

- [Parameters Documentation](https://hyperpay.docs.oppwa.com/reference/parameters)
- [3D Secure Parameters](https://hyperpay.docs.oppwa.com/tutorials/threeDSecure/Parameters)
- [Widget Integration](https://hyperpay.docs.oppwa.com/integrations/widget)

### Plugin Classes

#### Main Classes

- `HyperPayPaymentService`: Main payment processing service
- `HyperPayPaymentAbstract`: Abstract base class with common functionality
- `HyperPayHelper`: Utility functions and helpers
- `HyperPayController`: Handles callbacks and webhooks

#### Key Methods

```php
// Create payment checkout
$service = new HyperPayPaymentService();
$checkoutId = $service->makePayment($paymentData);

// Process payment result
$result = $service->afterMakePayment($callbackData);

// Check payment status
$status = $service->getPaymentStatus($resourcePath, $checkoutId);
```

## Troubleshooting

### Common Issues

1. **"Access token not configured"**
   - Ensure access token is properly set in plugin settings
   - Verify token is valid and not expired

2. **"Entity ID missing"**
   - Configure entity IDs for payment methods you want to support
   - Ensure entity IDs match your HyperPay account configuration

3. **"Payment widget not loading"**
   - Check browser console for JavaScript errors
   - Verify HyperPay script URL is accessible
   - Ensure checkout ID is valid

4. **"Webhook signature validation failed"**
   - Verify webhook secret is correctly configured
   - Check webhook URL is accessible from HyperPay servers

### Debug Mode

Enable debug logging by adding to your `.env`:

```env
LOG_LEVEL=debug
```

Check logs in `storage/logs/laravel.log` for detailed error information.

## Security Considerations

1. **Always use HTTPS** in production
2. **Configure webhook secret** for signature validation
3. **Enable 3D Secure** for enhanced security
4. **Regularly update** access tokens and credentials
5. **Monitor transactions** for suspicious activity

## Support

For technical support:

1. **Plugin Issues**: Check the troubleshooting section above
2. **HyperPay API Issues**: Contact HyperPay support
3. **Botble CMS Issues**: Contact Botble support

## License

This plugin is licensed under the MIT License. See the LICENSE file for details.

## Changelog

### Version 1.0.0
- Initial release
- Support for Visa, Mastercard, Mada, American Express, Apple Pay
- 3D Secure authentication
- Webhook support
- Multi-currency support
- Comprehensive error handling
