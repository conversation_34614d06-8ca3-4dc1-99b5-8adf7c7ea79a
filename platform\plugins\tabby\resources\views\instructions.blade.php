<div class="payment-instructions">
    <h4>{{ __('Tabby Pay-in-4 Setup Instructions') }}</h4>
    
    <div class="alert alert-info">
        <h5>{{ __('Getting Started') }}</h5>
        <ol>
            <li>{{ __('Register for a Tabby merchant account at') }} <a href="https://merchant.tabby.ai/" target="_blank">https://merchant.tabby.ai/</a></li>
            <li>{{ __('Complete your application to obtain access to Tabby Merchant Dashboard') }}</li>
            <li>{{ __('Retrieve your API keys and merchant code from the dashboard') }}</li>
            <li>{{ __('Enter your credentials in the form above') }}</li>
            <li>{{ __('Test your integration thoroughly using sandbox mode') }}</li>
            <li>{{ __('Request live API keys after successful testing') }}</li>
        </ol>
    </div>

    <div class="alert alert-warning">
        <h5>{{ __('Important Notes') }}</h5>
        <ul>
            <li>{{ __('Start with sandbox environment for testing') }}</li>
            <li>{{ __('Tabby supports AED, SAR, KWD, BHD, QAR, and EGP currencies') }}</li>
            <li>{{ __('Webhook URL will be automatically configured at: :url', ['url' => route('payments.tabby.webhook')]) }}</li>
            <li>{{ __('Ensure your site has SSL certificate for production use') }}</li>
        </ul>
    </div>

    <div class="alert alert-success">
        <h5>{{ __('Features') }}</h5>
        <ul>
            <li>{{ __('Pay-in-4 installments with no interest or fees') }}</li>
            <li>{{ __('Real-time eligibility checking') }}</li>
            <li>{{ __('Automatic payment capture and refund support') }}</li>
            <li>{{ __('Webhook notifications for payment status updates') }}</li>
            <li>{{ __('Mobile-optimized checkout experience') }}</li>
        </ul>
    </div>

    <div class="mt-3">
        <p><strong>{{ __('Need Help?') }}</strong></p>
        <p>{{ __('Contact Tabby support at') }} <a href="mailto:<EMAIL>"><EMAIL></a> {{ __('for integration assistance.') }}</p>
        <p>{{ __('Documentation:') }} <a href="https://docs.tabby.ai/" target="_blank">https://docs.tabby.ai/</a></p>
    </div>
</div>
