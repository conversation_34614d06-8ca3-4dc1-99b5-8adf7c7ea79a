<?php

namespace Bo<PERSON><PERSON>\Tabby\Http\Controllers;

use Bo<PERSON>ble\Base\Facades\BaseHelper;
use Bo<PERSON>ble\Base\Http\Controllers\BaseController;
use Botble\Base\Http\Responses\BaseHttpResponse;
use Botble\Ecommerce\Models\Order;
use Botble\Payment\Enums\PaymentStatusEnum;
use Botble\Payment\Models\Payment;
use Botble\Payment\Supports\PaymentHelper;
use Botble\Tabby\Services\Gateways\TabbyPaymentService;
use Botble\Tabby\Services\TabbyWebhookService;
use Exception;
use Illuminate\Http\Request;

class TabbyController extends BaseController
{
    protected TabbyPaymentService $tabbyPaymentService;

    public function __construct(TabbyPaymentService $tabbyPaymentService)
    {
        $this->tabbyPaymentService = $tabbyPaymentService;
    }

    public function callback(
        string $token,
        Request $request,
        BaseHttpResponse $response
    ): BaseHttpResponse {
        $paymentId = $request->input('payment_id');
        $type = $request->input('type', 'success');

        // Handle cancel and failure cases
        if ($type === 'cancel') {
            return $response
                ->setNextUrl(PaymentHelper::getCancelURL($token))
                ->withInput()
                ->setMessage(__('You aborted the payment. Please retry or choose another payment method.'));
        }

        if ($type === 'failure') {
            return $response
                ->setNextUrl(PaymentHelper::getCancelURL($token) . '&error_message=' . __('Payment failed'))
                ->withInput()
                ->setMessage(__('Sorry, Tabby is unable to approve this purchase. Please use an alternative payment method for your order.'));
        }

        // For success case - if we reach here, payment is successful
        if (!$paymentId) {
            return $response
                ->setNextUrl(PaymentHelper::getCancelURL($token))
                ->withInput()
                ->setMessage(__('Payment ID is missing!'));
        }

        // Process the successful payment
        try {
            // Get order ID from token
            $orderId = $this->getOrderIdFromToken($token);

            do_action(PAYMENT_ACTION_PAYMENT_PROCESSED, [
                'charge_id' => $paymentId,
                'payment_channel' => TABBY_PAYMENT_METHOD_NAME,
                'status' => PaymentStatusEnum::COMPLETED,
                'order_id' => $orderId,
            ]);

            return $response
                ->setNextUrl(PaymentHelper::getRedirectURL($token) . '?charge_id=' . $paymentId)
                ->setMessage(__('Payment completed successfully!'));
        } catch (Exception $exception) {
            BaseHelper::logError($exception);

            return $response
                ->setNextUrl(PaymentHelper::getCancelURL($token) . '&error_message=' . $exception->getMessage())
                ->withInput()
                ->setMessage($exception->getMessage());
        }
    }

    public function webhook(Request $request)
    {
        try {
            // Validate webhook signature if configured
            $authHeader = get_payment_setting('webhook_auth_header', TABBY_PAYMENT_METHOD_NAME);
            if ($authHeader && !$this->tabbyPaymentService->validateWebhookSignature($request, $authHeader)) {
                return response('Unauthorized', 401);
            }

            $paymentId = $request->input('id');
            $status = strtolower($request->input('status', ''));

            if (!$paymentId) {
                return response('Payment ID missing', 400);
            }

            // Handle different webhook events
            switch ($status) {
                case 'authorized':
                    $this->handleAuthorizedWebhook($request);
                    break;

                case 'closed':
                    $this->handleClosedWebhook($request);
                    break;

                case 'rejected':
                    $this->handleRejectedWebhook($request);
                    break;

                case 'expired':
                    $this->handleExpiredWebhook($request);
                    break;

                default:
                    // Log unknown status but don't fail
                    BaseHelper::logError("Unknown Tabby webhook status: {$status}");
            }

            return response('OK', 200);
        } catch (Exception $exception) {
            BaseHelper::logError($exception);
            return response('Internal Server Error', 500);
        }
    }

    protected function processAuthorizedPayment(array $paymentData, Request $request): void
    {
        $paymentId = $paymentData['id'];
        $amount = (float) $paymentData['amount'];
        $currency = $paymentData['currency'];
        $orderId = $paymentData['meta']['order_id'] ?? null;
        $customerId = $paymentData['meta']['customer'] ?? null;

        // Find order by reference ID if order ID is not in meta
        if (!$orderId && class_exists(Order::class)) {
            $referenceId = $paymentData['order']['reference_id'] ?? null;
            if ($referenceId) {
                $order = Order::query()->where('code', $referenceId)->first();
                $orderId = $order?->id;
            }
        }

        do_action(PAYMENT_ACTION_PAYMENT_PROCESSED, [
            'amount' => $amount,
            'currency' => $currency,
            'charge_id' => $paymentId,
            'payment_channel' => TABBY_PAYMENT_METHOD_NAME,
            'status' => PaymentStatusEnum::COMPLETED,
            'order_id' => $orderId,
            'customer_id' => $customerId,
            'customer_type' => $request->input('customer_type'),
        ]);

        // Auto-capture the payment
        try {
            $this->tabbyPaymentService->capturePayment($paymentId, $amount, $orderId);
        } catch (Exception $exception) {
            BaseHelper::logError($exception);
            // Don't fail the webhook if capture fails - it can be retried later
        }
    }

    protected function getOrderIdFromToken(string $token): ?int
    {
        // Try to get order from session data
        $sessionData = \Botble\Ecommerce\Facades\OrderHelper::getOrderSessionData($token);
        if ($sessionData && isset($sessionData['created_order_id'])) {
            return $sessionData['created_order_id'];
        }

        // Fallback: try to find order by token
        $order = Order::query()->where('token', $token)->first();
        return $order?->id;
    }

    protected function processBasicPayment(string $paymentId, string $token, Request $request): void
    {
        // Basic payment processing when we can't get detailed payment data
        do_action(PAYMENT_ACTION_PAYMENT_PROCESSED, [
            'charge_id' => $paymentId,
            'payment_channel' => TABBY_PAYMENT_METHOD_NAME,
            'status' => PaymentStatusEnum::COMPLETED,
        ]);
    }

    protected function handleAuthorizedWebhook(Request $request): void
    {
        $paymentId = $request->input('id');
        $amount = (float) $request->input('amount', 0);
        $currency = $request->input('currency', '');
        $orderId = $request->input('meta.order_id');

        // Check if payment already exists
        $payment = Payment::query()
            ->where('charge_id', $paymentId)
            ->first();

        if (!$payment) {
            // Process new authorized payment
            $this->processAuthorizedPayment($request->all(), $request);
        }
    }

    protected function handleClosedWebhook(Request $request): void
    {
        $paymentId = $request->input('id');

        // Update payment status to completed
        $payment = Payment::query()
            ->where('charge_id', $paymentId)
            ->first();

        if ($payment && $payment->status !== PaymentStatusEnum::COMPLETED) {
            $payment->status = PaymentStatusEnum::COMPLETED;
            $payment->save();

            do_action(PAYMENT_ACTION_PAYMENT_PROCESSED, [
                'charge_id' => $paymentId,
                'order_id' => $payment->order_id,
                'status' => PaymentStatusEnum::COMPLETED,
                'payment_channel' => TABBY_PAYMENT_METHOD_NAME,
            ]);
        }
    }

    protected function handleRejectedWebhook(Request $request): void
    {
        $paymentId = $request->input('id');

        // Update payment status to failed
        $payment = Payment::query()
            ->where('charge_id', $paymentId)
            ->first();

        if ($payment) {
            $payment->status = PaymentStatusEnum::FAILED;
            $payment->save();
        }
    }

    protected function handleExpiredWebhook(Request $request): void
    {
        $paymentId = $request->input('id');

        // Update payment status to failed
        $payment = Payment::query()
            ->where('charge_id', $paymentId)
            ->first();

        if ($payment) {
            $payment->status = PaymentStatusEnum::FAILED;
            $payment->save();
        }
    }

    /**
     * Setup webhook
     */
    public function setupWebhook(Request $request)
    {
        $webhookService = new TabbyWebhookService();
        $result = $webhookService->setupWebhook();

        return response()->json($result);
    }

    /**
     * Remove webhook
     */
    public function removeWebhook(Request $request)
    {
        $webhookService = new TabbyWebhookService();
        $result = $webhookService->removeWebhook();

        return response()->json($result);
    }

    /**
     * Get webhook status
     */
    public function getWebhookStatus(Request $request)
    {
        $webhookService = new TabbyWebhookService();
        $result = $webhookService->getWebhookStatus();

        return response()->json($result);
    }
}
