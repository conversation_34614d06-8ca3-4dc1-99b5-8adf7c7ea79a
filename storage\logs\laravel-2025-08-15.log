[2025-08-15 15:02:09] local.INFO: HyperPay makePayment called with data: {"amount":154.0,"currency":"SAR","order_id":[48],"payment_type":"mada","data_keys":["amount","payment_fee","shipping_amount","shipping_method","tax_amount","discount_amount","currency","order_id","description","customer_id","customer_type","return_url","cancel_url","callback_url","products","orders","address","checkout_token","payment_type"],"address_data":{"name":"<PERSON><PERSON><PERSON><PERSON>","email":"<EMAIL>","phone":"+923147552550","country":"PK","state":"Test","city":"test","address":"test","zip_code":null}} 
[2025-08-15 15:02:09] local.INFO: HyperPay Checkout Request {"url":"https://eu-test.oppwa.com/v1/checkouts","entity_id":"8ac7a4c79483092601948366b9d1011b","amount":"154.00","currency":"SAR","payment_type":"mada","data":{"entityId":"8ac7a4c79483092601948366b9d1011b","amount":"154.00","currency":"SAR","paymentType":"DB","merchantTransactionId":"48","shopperResultUrl":"https://martfury.gc/payment/hyperpay/callback","customer.email":"<EMAIL>","customer.givenName":"Ishtiaq","customer.surname":"Ahmed","billing.street1":"test","billing.city":"test","billing.state":"test","billing.country":"PK","billing.postcode":"11111"}} 
[2025-08-15 15:02:11] local.INFO: HyperPay Checkout Response {"status_code":200,"response":{"result":{"code":"000.200.100","description":"successfully created checkout"},"buildNumber":"8aa3728a6ba24ed3c4a6c6e9064ffd726f1bee67@2025-08-11 05:22:09 +0000","timestamp":"2025-08-15 15:02:11+0000","ndc":"8FC0BD1D066C3A2AA927BD91EBF97169.uat01-vm-tx03","id":"8FC0BD1D066C3A2AA927BD91EBF97169.uat01-vm-tx03"}} 
[2025-08-15 15:02:41] local.INFO: HyperPay Callback Received {"resource_path":"/v1/checkouts/8FC0BD1D066C3A2AA927BD91EBF97169.uat01-vm-tx03/payment","checkout_id":"8FC0BD1D066C3A2AA927BD91EBF97169.uat01-vm-tx03","all_params":{"id":"8FC0BD1D066C3A2AA927BD91EBF97169.uat01-vm-tx03","resourcePath":"/v1/checkouts/8FC0BD1D066C3A2AA927BD91EBF97169.uat01-vm-tx03/payment"}} 
[2025-08-15 15:02:42] local.INFO: HyperPay Payment Status Check {"url":"https://eu-test.oppwa.com/v1/checkouts/8FC0BD1D066C3A2AA927BD91EBF97169.uat01-vm-tx03/payment","checkout_id":"8FC0BD1D066C3A2AA927BD91EBF97169.uat01-vm-tx03","status_code":400,"response":{"result":{"code":"200.300.404","description":"invalid or missing parameter","parameterErrors":[{"name":"entityId","value":null,"message":"invalid or missing parameter"}]},"buildNumber":"8aa3728a6ba24ed3c4a6c6e9064ffd726f1bee67@2025-08-11 05:22:09 +0000","timestamp":"2025-08-15 15:02:42+0000","ndc":"8FC0BD1D066C3A2AA927BD91EBF97169.uat01-vm-tx03"}} 
[2025-08-15 15:02:42] local.INFO: HyperPay Processing Payment Hook {"order_id":[48],"order_ids":[48],"amount":154.0,"currency":"SAR","status":"failed","charge_id":"8FC0BD1D066C3A2AA927BD91EBF97169.uat01-vm-tx03","customer_id":null,"customer_type":"Botble\\Ecommerce\\Models\\Customer"} 
[2025-08-15 15:20:20] local.INFO: HyperPay makePayment called with data: {"amount":154.0,"currency":"SAR","order_id":[49],"payment_type":"visa","data_keys":["amount","payment_fee","shipping_amount","shipping_method","tax_amount","discount_amount","currency","order_id","description","customer_id","customer_type","return_url","cancel_url","callback_url","products","orders","address","checkout_token","payment_type"],"address_data":{"name":"Ishtiaq Ahmed","email":"<EMAIL>","phone":"+923147552550","country":"PK","state":"Test","city":"test","address":"test","zip_code":null}} 
[2025-08-15 15:20:20] local.INFO: HyperPay Checkout Request {"url":"https://eu-test.oppwa.com/v1/checkouts","entity_id":"8ac7a4c79483092601948366b9d1011b","amount":"154.00","currency":"SAR","payment_type":"visa","data":{"entityId":"8ac7a4c79483092601948366b9d1011b","amount":"154.00","currency":"SAR","paymentType":"DB","merchantTransactionId":"49","shopperResultUrl":"https://martfury.gc/payment/hyperpay/callback","customer.email":"<EMAIL>","customer.givenName":"Ishtiaq","customer.surname":"Ahmed","billing.street1":"test","billing.city":"test","billing.state":"test","billing.country":"PK","billing.postcode":"11111"}} 
[2025-08-15 15:20:21] local.INFO: HyperPay Checkout Response {"status_code":200,"response":{"result":{"code":"000.200.100","description":"successfully created checkout"},"buildNumber":"8aa3728a6ba24ed3c4a6c6e9064ffd726f1bee67@2025-08-11 05:22:09 +0000","timestamp":"2025-08-15 15:20:22+0000","ndc":"A756E034138B1072883E724DB84035E6.uat01-vm-tx02","id":"A756E034138B1072883E724DB84035E6.uat01-vm-tx02"}} 
[2025-08-15 15:20:50] local.INFO: HyperPay Callback Received {"resource_path":"/v1/checkouts/A756E034138B1072883E724DB84035E6.uat01-vm-tx02/payment","checkout_id":"A756E034138B1072883E724DB84035E6.uat01-vm-tx02","all_params":{"id":"A756E034138B1072883E724DB84035E6.uat01-vm-tx02","resourcePath":"/v1/checkouts/A756E034138B1072883E724DB84035E6.uat01-vm-tx02/payment"}} 
[2025-08-15 15:20:51] local.INFO: HyperPay Payment Status Check {"url":"https://eu-test.oppwa.com/v1/checkouts/A756E034138B1072883E724DB84035E6.uat01-vm-tx02/payment","query_params":{"entityId":"8ac7a4c79483092601948366b9d1011b"},"checkout_id":"A756E034138B1072883E724DB84035E6.uat01-vm-tx02","payment_type":"visa","entity_id":"8ac7a4c79483092601948366b9d1011b","status_code":200,"response":{"result":{"code":"200.300.404","description":"invalid or missing parameter","parameterErrors":[{"name":"shopperResultUrl","value":"https://martfury.gc/payment/hyperpay/callback?checkout_id=A756E034138B1072883E724DB84035E6.uat01-vm-tx02&order_id=","message":"was already set and cannot be overwritten"}]},"buildNumber":"8aa3728a6ba24ed3c4a6c6e9064ffd726f1bee67@2025-08-11 05:22:09 +0000","timestamp":"2025-08-15 15:20:49+0000","ndc":"A756E034138B1072883E724DB84035E6.uat01-vm-tx02"}} 
[2025-08-15 15:20:51] local.INFO: HyperPay Payment Result Analysis {"result_code":"200.300.404","result_description":"invalid or missing parameter","checkout_id":"A756E034138B1072883E724DB84035E6.uat01-vm-tx02","charge_id":"A756E034138B1072883E724DB84035E6.uat01-vm-tx02"} 
[2025-08-15 15:20:51] local.INFO: HyperPay Processing Payment Hook {"order_id":[49],"order_ids":[49],"amount":154.0,"currency":"SAR","status":"failed","charge_id":"A756E034138B1072883E724DB84035E6.uat01-vm-tx02","customer_id":null,"customer_type":"Botble\\Ecommerce\\Models\\Customer"} 
[2025-08-15 15:20:52] local.INFO: HyperPay Checkout Token Retrieved {"order_id":null,"checkout_token":null,"payment_result":"A756E034138B1072883E724DB84035E6.uat01-vm-tx02"} 
[2025-08-15 15:20:52] local.INFO: HyperPay Payment Success {"charge_id":"A756E034138B1072883E724DB84035E6.uat01-vm-tx02","order_id":null,"checkout_token":null} 
[2025-08-15 15:20:52] local.WARNING: HyperPay Success but no checkout token {"order_id":null,"charge_id":"A756E034138B1072883E724DB84035E6.uat01-vm-tx02"} 
