@extends('frontend.layouts.app_wide')

@section('content')
@push('scripts')
<script type="text/javascript" src="{{asset('front/js/checkout-js.js')}}"></script>
@endpush
@php

$locale = App::getLocale();
$isgifthidden = 'hidden';

$user_agent = $_SERVER['HTTP_USER_AGENT'];
    //mht test

    $isAdmin = Auth::user()->isAdmin == 1;

    if (stripos($user_agent, 'Chrome') == false) {
    $isSafari = true;
    } else {
    $isSafari = false;
    }

    /*
    echo $isSafari?"is it safari":"is it not safari";
    if ($isAdmin == 1) {
    echo "user is admin pay method =" .Auth::user()->payment;
    echo $user_agent."<br>";
    }
    */

    @endphp
    @section('pageTitle', __('messages.Checkout'))

    <style>
        .table td {
            padding-top: 1rem;
            padding-bottom: 1rem;
        }

        .product-col {
            display: flex;
            gap: 1rem;
        }

        h2,
        .h2 {
            background-color: #eee;
            padding: 0.5rem;
            font-size: 2rem;
        }

        .cart-discount1 .btn {
            padding: 0.8rem 0.3rem;
        }

        .cart-discount1 {
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .cart-discount1 {
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .price-col {
            width: 20% !important;
        }

        .coupon_bk {
            background-color: #f4f4f4;
            border-radius: 50%;
            padding: 5px;
            margin-inline-end: 5px;
            border: 1px solid #d8d3d3;
        }

        .total-text {
            text-align: end;
        }

        .breadcrumb-nav {
            margin-bottom: 1rem;
        }

        .input-group .flag-icon {
            width: 30px;
            height: 21px;
            object-fit: cover;
        }

        .product-image-container img {
            border: 1px solid #e3e3e3;
            border-radius: 8px;
        }

        .form-control {
            color: black;
        }

        .btn {
            min-width: 150px;
        }
    </style>

    <main class="main">
        <nav aria-label="breadcrumb" class="breadcrumb-nav">
            <div class="container">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="index.html"><i class="icon-home"></i></a></li>
                    <li class="breadcrumb-item active" aria-current="page">
                        {{ __('messages.Checkout') }}
                    </li>
                </ol>
            </div><!-- End .container -->
        </nav>

        <div class="container">

            <div class="row">
                <div id="dev_alert"></div>
                {{-- <h2 class="text-danger" style="text-align: center;"><span><?php echo __('messages.ShippingDelay')?></span></h2> --}}

                <div class="col-lg-6" id="inputFields">
                    <div class="cart-discount1 border">

                        @if ($errors->has('discountcod'))
                        <h2 class="text-danger" style="text-align: center;"><span>{{ $errors->first('discountcod') }}</span>
                        </h2>
                        @endif
                        <h2 class="d-flex" style="align-items: center"><span class="coupon_bk"><img
                                    src="{{ asset('images/coupon1.png') }}"
                                    style="width:35px;"></span>{{ __('messages.Apply Discount Code') }}</h2>

                        <form id="applyCouponCodeCheckout" action="javascript:;"  method="POST" enctype="multipart/form-data">
                            @csrf
                            <div class="input-group" style="justify-content: space-around">
                                <input type="text" value="" class="form-control form-control-sm me-1" style="min-width: 35%;"
                                    id="code" name="code" placeholder="{{ __('messages.Enter discount code') }}" required>
                                <div class="input-group-append">
                                    <button class="btn btn-sm btn-primary"
                                        type="submit">{{ __('messages.Apply Discount') }}</button>
                                </div>
                            </div><!-- End .input-group -->
                            <p id="apply-coupon-message" ></p>
                        </form>
                    </div>
                    <form action="{{ route('checkout.place_order') }}" method="POST" enctype="multipart/form-data">

                            @csrf
                                <div class="m-1 p-3 border">
                                    <h2>{{ __('messages.Billing & Shipping') }}</h2>
                                    <div class="form-group required-field">
                                        <label for="name">{{ __('messages.Full Name') }}</label>
                                        <input style="max-width:100%;" type="text" class="form-control" id="name"
                                            name="name" placeholder="{{ __('messages.Full Name') }}"
                                            value="{{ $user->name }}">
                                        @if ($errors->has('name'))
                                        <span class="text-danger">{{ $errors->first('name') }}</span>
                                        @endif
                                    </div>
                                    <div class="form-group required-field">
                                        <label for="city">{{ __('messages.City') }}</label>
                                        <div class="select-custom">
                                            <select  class="select-control"
                                                style="width:100%;padding: 5px;" type="text" id="city" name="city"
                                                placeholder="{{ __('messages.City') }}" value="{{ $user->city }}">

                                                <option value="non" selected>{{ __('messages.select-city') }}</option>

                                                @foreach ($cities as $city)
                                                    @if ($locale == 'ar')
                                                    <option  @if ($user->city == $city->value)  selected @endif value="{{ $city->value }}"> {{ $city->extra_value }}</option>
                                                    @else
                                                    <option  @if ($user->city == $city->value)  selected @endif value="{{ $city->value }}"> {{ $city->value }}</option>
                                                    @endif
                                                @endforeach
                                            </select>
                                            @if ($errors->has('city'))
                                            <span class="text-danger">{{ $errors->first('city') }}</span>
                                            @endif
                                        </div><!-- End .select-custom -->
                                    </div>
                                     <div class="form-group required-field">
                                        <label for="city_area">{{ __('messages.city_area') }}</label>
                                        <input style="max-width:100%;" type="text" class="form-control" id="city_area"
                                            name="city_area" placeholder="{{ __('messages.city_area') }}"
                                            value="{{ $user->city_area }}" maxlength="100">
                                        @if ($errors->has('city_area'))
                                        <span class="text-danger">{{ $errors->first('city_area') }}</span>
                                        @endif

                                    </div>

                                    <div class="form-group required-field">
                                        <label for="address">{{ __('messages.Address') }}</label>
                                        <input style="max-width:100%;" type="text" class="form-control" id="address"
                                            name="address" placeholder="{{ __('messages.Address') }}"
                                            value="{{ $user->address }}" maxlength="100">
                                        @if ($errors->has('address'))
                                        <span class="text-danger">{{ $errors->first('address') }}</span>
                                        @endif

                                    </div>
                                    <div class="form-group required-field">
                                        <label for="email">{{ __('messages.Email Address') }}</label>
                                        <input style="max-width:100%;" type="text" class="form-control" id="email"
                                            name="email" placeholder="{{ __('messages.Email Address') }}"
                                            value="{{ $user->email }}">
                                        @if ($errors->has('email'))
                                        <span class="text-danger">{{ $errors->first('email') }}</span>
                                        @endif

                                    </div>

                                    <div class="form-group required-field">
                                        <label for="gender">{{ __('messages.Gender') }}</label>
                                        <div class="select-custom">
                                            <select class="form-control" style="max-width:100%;padding: 5px;" type="text"
                                                class="form-control" id="gender" name="gender"
                                                placeholder="{{ __('messages.Male/Female') }}" value="{{ $user->gender }}">
                                                <option @if ($user->gender == 'Male') selected @endif value="Male">{{ __('messages.Male') }}</option>
                                                <option @if ($user->gender == 'Female') selected @endif value="Female">{{ __('messages.Female') }}</option>
                                            </select>
                                        </div><!-- End .select-custom -->
                                    </div>
                                    <div class="form-group">
                                        <label for="mobile">{{ __('messages.Mobile Number') }} (05XXXXXXXX)</label>
                                        <div class="input-group">
                                            <span class="input-group-text bg-light">
                                                <img src="https://flagcdn.com/w40/sa.png" alt="Saudi Flag" class="flag-icon">
                                            </span>
                                            <input type="text" id="mobile" name="mobile" class="form-control"
                                                placeholder="Enter phone number"
                                                title="Enter a valid Saudi phone number (e.g., 05XXXXXXX)" value="{{ $user->mobile }}">
                                        </div>
                                        @if ($errors->has('mobile'))
                                        <div class="text-danger">{{ $errors->first('mobile') }}</div>
                                        @endif
                                    </div>
                                    <br>
                                    <h2>{{ __('messages.Additional Information') }}</h2>
                                    <div class="form-group">
                                        <label for="notes">{{ __('messages.Order Notes (optional)') }}</label>
                                        <textarea style="max-width:100%;" rows="2" cols="5" class="form-control" id="notes" name="notes"
                                            placeholder="{{ __('messages.Notes About Your Order') }}"></textarea>
                                    </div>

                                    <div class="" id="giftDiv">
                                        <label class="form-check">
                                            <input  id="giftCheck" value="on" @if($gift==35) checked @endif name="giftCheck" type="checkbox" class="form-check-input isgift-checkout " />
                                            <span class="checkoutspan">{{ __('messages.Wrapgiftksa') }}</span>
                                        </label>
                                        @if($gift==35)
                                            <div class="row g-4" id="giftDetails">
                                        @else
                                        <div class="collapse row g-4" id="giftDetails">
                                        @endif
                                            <div class="col">
                                                <label for="giftcolor">{{ __('messages.giftcolor') }}</label><br>
                                        <input checked type="radio" name="giftcolor" value="Gold">
                                            {{ __('messages.Gold') }}<br>
                                        <input type="radio" name="giftcolor" value="Red">
                                            {{ __('messages.Red') }}<br>
                                            </div><br>
                                            <div class="col">
                                                <label for="giftmessage"
                                                    class="form-label">{{ __('messages.giftmess') }}</label>
                                                <textarea style="max-width:100%;" rows="2" cols="5" class="form-control" id="giftmessage"
                                                    name="giftmessage"></textarea>
                                            </div>
                                        </div>
                                    </div>

                                </div>

                </div><!-- End .col-lg-6 -->



                <div class="col-lg-6 " id="mycheckoutsummary">

                    <div class="m-1 p-3 border">

                            <div class="order-summary">
                                <h2>{{ __('messages.Summary') }}</h2>
                                @if ($errors->has('paymentmethod'))
                                <div class="error" style="color:red">{{ $errors->first('paymentmethod') }}</div>
                                @endif
                                <div id="mini-cart-checkout">
                                    @include('frontend.mini-cart')

                                </div><!-- End #order-cart-section -->

                                <div id="checkout-summary">
                                    @include('frontend.checkout-summary')
                                </div>

                                <h3 class="alert alert-warning text-center text-yellow-700 fw-bold">
                                    {{ __('messages.freeShipping') }}
                                </h3>
                                <h2>{{ __('messages.Shipping Methods') }}</h2>
                                <table class="table table-mini-cart" id="methodstable">
                                    <tbody>
                                        <tr>
                                            <td><input class="shipping-checkout" checked type="radio" name="shippingmethod" value="express"></td>
                                            <td><strong> {{ __('messages.Express Shipping') }}</strong></td>
                                            <td><?php if ($final_total >= 200) {
                                                    echo '<img style="max-height:40px;float:inline-end" src="https://adawliahshop.com/storage/graphics/free-delivery.jpg">';
                                                } ?></td>
                                        </tr>
                                        <tr {{ $nopickup }}>
                                            <td><input class="shipping-checkout" type="radio" name="shippingmethod" value="pickup"></td>
                                            <td><strong> {{ __('messages.Pickup From Store') }}</strong></td>
                                            <td></td>
                                        </tr>

                                    </tbody>
                                </table>
                                <div class="collapse" id="branches_select">


                                    @include('frontend.checkout-branches')

                                </div>
                                <h2>{{ __('messages.Payment Methods') }}</h2>

                                <table class="table table-mini-cart" id="methodstable">
                                    <tbody>
                                        <tr {{ $madahidden }}>
                                            <td><input checked class="paymentmethod-checkout" type="radio" name="paymentmethod" id="MadaNew" value="MadaNew"></td>
                                            <td><strong> {{ __('messages.Mada Debit Card') . '.' }}</strong></td>
                                            <td><img style="float: inline-end;max-width:60px; max-height:25px;"
                                                    src="{{ asset('assets/images/mada-logo.png') }}"></td>
                                        </tr>
                                        <tr >
                                            <td>
                                                <input class="paymentmethod-checkout" type="radio" name="paymentmethod" id="VisaMasterNew" value="VisaMasterNew">
                                            </td>
                                            <td colspan="2">
                                                <div style="display: flex;align-items: center;justify-content: space-between;">
                                                    <strong> {{ __('messages.Visa Master Cards') }}</strong>
                                                    <img style="float: inline-end;max-width:111px; max-height:41px;" src="{{ asset('public/storage/graphics/flex_new.jpg') }}">
                                                    <img style="float: inline-end;max-width:111px; max-height:41px;" src="{{ asset('public/storage/graphics/stc.svg') }}">
                                                    <img style="max-width:111px; max-height:41px;float:inline-end"   src="{{ asset('public/storage/graphics/visamaster.png') }}">
                                                </div>
                                            </td>
                                        </tr>
                                        @if ($isSafari)
                                        <tr {{ $appelhidden }} >
                                            <td><input  type="radio" name="paymentmethod" id="ApplePayNew"
                                                    value="ApplePayNew"></td>
                                            <td><strong>{{ __('messages.Apple Pay') }}</strong></td>
                                            <td><img style="float: inline-end;max-width:111px; max-height:41px;"
                                                    src="{{ asset('storage/graphics/appelpayicon.png')}}"></td>
                                        </tr>
                                        @endif
                                        <tr id="tr_amex"  {{ $amexhidden }} >
                                            <td><input class="paymentmethod-checkout" type="radio" name="paymentmethod" id="AMEX"
                                                    value="AMEX"></td>
                                            <td><strong> {{ __('messages.amex') }}</strong></td>
                                            <td><img style="float: inline-end;max-width:111px; max-height:41px;"
                                                    src="{{ asset('storage/graphics/amex.png') }}"></td>
                                        </tr>
                                        <tr id="tr_tabby" {{ $tabbyhidden }} >
                                            <td><input class="paymentmethod-checkout" type="radio" name="paymentmethod" id="Tabby" value="Tabby"></td>
                                            <td>
                                                <strong>{{ __('messages.Split in 4 payments with Tabby. No fees') }}</strong>
                                            </td>
                                            <td><img style="float: inline-end;max-width:85px; max-height:45px;"
                                                    src="{{ asset('public/storage/graphics/tabby-logo.jpg') }}"></td>
                                        </tr>
                                        <tr >
                                            <th colspan="3">
                                                <div class="collapse" id="tabbyCard"></div>
                                            </th>
                                        </tr>
                                        <tr id="tr_cod" {{ $codhidden }}>
                                            <td><input class="paymentmethod-checkout"  type="radio" name="paymentmethod" id="COD"
                                                    value="COD"></td>
                                            <td><strong> {{ __('messages.Cash On Delivery') }}</strong></td>
                                            {{-- <td><img width="111px" height="30px" src="{{asset('assets/images/tamara-logo.png')}}"></td> --}}
                                        </tr>
                                    </tbody>
                                </table>
                                <div class="checkout-methods">
                                    <button type="submit"
                                        class="btn btn-block btn-sm btn-primary  w-100 rounded-2 fs-3">{{ __('messages.Place Order') }}</button>
                                </div><!-- End .checkout-methods -->


                                <br>
                                @if( $tabbyhidden=='hidden')
                                    <div class="" id="tabby_help">
                                        <p>{{ __('messages.tabbynotav') }}<br><a
                                            href="https://web.whatsapp.com/send?phone=966112438957"
                                            class="text-danger">{{ __('messages.needhelp') }}</a></p>
                                    </div>
                                @else
                                <div class="collapse" id="tabby_help">
                                    <p>{{ __('messages.tabbynotav') }}<br><a
                                        href="https://web.whatsapp.com/send?phone=966112438957"
                                        class="text-danger">{{ __('messages.needhelp') }}</a></p>
                                </div>
                                @endif

                                </div>

                            </div><!-- End .order-summary -->
                        </form>

                    </div>

                </div>
            </div><!-- End .row -->
        </div>
    </main><!-- End .main -->
    <!-- Snap Pixel Code -->
    <script type='text/javascript'>
        (function(e, t, n) {
            if (e.snaptr) return;
            var a = e.snaptr = function() {
                a.handleRequest ? a.handleRequest.apply(a, arguments) : a.queue.push(arguments)
            };
            a.queue = [];
            var s = 'script';
            r = t.createElement(s);
            r.async = !0;
            r.src = n;
            var u = t.getElementsByTagName(s)[0];
            u.parentNode.insertBefore(r, u);
        })(window, document,
            'https://sc-static.net/scevent.min.js');
        snaptr('init', '523bad55-374f-496f-b32d-8f1974b0290e');
        snaptr('track', 'START_CHECKOUT');
    </script>
    <!-- End Snap Pixel Code -->
    <script src="https://checkout.tabby.ai/tabby-card.js"></script>
    <script>
        new TabbyCard({
            selector: '#tabbyCard', // empty div for TabbyCard
            currency: 'SAR', // or SAR, BHD, KWD
            lang: '{{ $locale }}', // or ar
            price: ' {{ $final_total }}',
            size: 'wide', // or wide, depending on the width
            theme: 'default', // or can be black
            header: false // if there is a Payment method name already
        });
    </script>
    @endsection
    @section('js')
    <script>
        $(document).ready(function() {
            $('#city').select2();
        });
    </script>

    @endsection
