<?php

namespace Bo<PERSON>ble\Tabby\Tests;

use <PERSON><PERSON>ble\Base\Facades\BaseHelper;
use <PERSON><PERSON>ble\Ecommerce\Models\Currency;
use Bo<PERSON>ble\Payment\Facades\PaymentMethods;
use Bo<PERSON>ble\Tabby\Services\Gateways\TabbyPaymentService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\View;
use Tests\TestCase;

class TabbyCardWidgetTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Mock currency
        $currency = new Currency();
        $currency->title = 'SAR';
        $currency->symbol = 'ر.س';
        $currency->is_default = true;
        
        // Mock the get_application_currency function
        $this->app->bind('get_application_currency', function () use ($currency) {
            return $currency;
        });
    }

    public function testTabbyCardWidgetRendersWithCorrectData()
    {
        // Set up test data
        $testData = [
            'amount' => '500.00',
            'currency' => 'SAR',
            'isEligible' => true,
            'errorMessage' => null,
            'rejectionReason' => null,
        ];

        // Set locale to Arabic
        App::setLocale('ar');

        // Render the view
        $view = View::make('plugins/tabby::methods', $testData);
        $content = $view->render();

        // Assert that the card container is present
        $this->assertStringContainsString('id="tabbyCard"', $content);
        
        // Assert that the Tabby card script is included
        $this->assertStringContainsString('https://checkout.tabby.ai/tabby-card.js', $content);
        $this->assertStringContainsString('vendor/core/plugins/tabby/js/tabby-card.js', $content);
        
        // Assert that the global data is set correctly
        $this->assertStringContainsString('window.TabbyCheckoutData', $content);
        $this->assertStringContainsString('"currency":"SAR"', $content);
        $this->assertStringContainsString('"locale":"ar"', $content);
        $this->assertStringContainsString('"amount":"500"', $content);
    }

    public function testTabbyCardWidgetNotRenderedWhenNotEligible()
    {
        $testData = [
            'amount' => '500.00',
            'currency' => 'SAR',
            'isEligible' => false,
            'errorMessage' => null,
            'rejectionReason' => 'amount_too_low',
            'rejectionMessage' => 'Amount is too low for Tabby installments',
        ];

        $view = View::make('plugins/tabby::methods', $testData);
        $content = $view->render();

        // Assert that the card container is NOT present
        $this->assertStringNotContainsString('id="tabbyCard"', $content);
        
        // Assert that the scripts are NOT included
        $this->assertStringNotContainsString('https://checkout.tabby.ai/tabby-card.js', $content);
        
        // Assert that rejection message is shown
        $this->assertStringContainsString('Amount is too low for Tabby installments', $content);
    }

    public function testTabbyCardWidgetWithEnglishLocale()
    {
        $testData = [
            'amount' => '1000.50',
            'currency' => 'SAR',
            'isEligible' => true,
            'errorMessage' => null,
            'rejectionReason' => null,
        ];

        // Set locale to English
        App::setLocale('en');

        $view = View::make('plugins/tabby::methods', $testData);
        $content = $view->render();

        // Assert English locale is set
        $this->assertStringContainsString('"locale":"en"', $content);
        
        // Assert amount is properly cleaned
        $this->assertStringContainsString('"amount":"1000.50"', $content);
    }

    public function testTabbyCardWidgetWithErrorMessage()
    {
        $testData = [
            'amount' => '500.00',
            'currency' => 'SAR',
            'isEligible' => false,
            'errorMessage' => 'API connection failed',
            'rejectionReason' => null,
        ];

        $view = View::make('plugins/tabby::methods', $testData);
        $content = $view->render();

        // Assert error message is displayed
        $this->assertStringContainsString('API connection failed', $content);
        $this->assertStringContainsString('text-danger', $content);
        
        // Assert card is not rendered
        $this->assertStringNotContainsString('id="tabbyCard"', $content);
    }

    public function testTabbyCardWidgetAmountCleaning()
    {
        $testData = [
            'amount' => 'ر.س 1,234.56', // Amount with currency symbol and formatting
            'currency' => 'SAR',
            'isEligible' => true,
            'errorMessage' => null,
            'rejectionReason' => null,
        ];

        $view = View::make('plugins/tabby::methods', $testData);
        $content = $view->render();

        // Assert amount is properly cleaned (only numbers and decimal point)
        $this->assertStringContainsString('"amount":"1234.56"', $content);
    }

    public function testTabbyCardWidgetPaymentMethodName()
    {
        $testData = [
            'amount' => '500.00',
            'currency' => 'SAR',
            'isEligible' => true,
            'errorMessage' => null,
            'rejectionReason' => null,
        ];

        $view = View::make('plugins/tabby::methods', $testData);
        $content = $view->render();

        // Assert payment method name is included
        $this->assertStringContainsString('"paymentMethodName":"tabby"', $content);
    }
}
