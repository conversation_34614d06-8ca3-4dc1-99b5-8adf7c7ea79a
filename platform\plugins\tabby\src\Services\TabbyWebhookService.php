<?php

namespace Botble\Tabby\Services;

use Botble\Setting\Facades\Setting;
use Exception;
use Illuminate\Support\Facades\Log;

class TabbyWebhookService
{
    protected TabbyApiService $tabbyApiService;

    public function __construct()
    {
        $this->tabbyApiService = new TabbyApiService();
    }

    /**
     * Setup webhook for production
     */
    public function setupWebhook(): array
    {
        try {
            $webhookUrl = route('payments.tabby.webhook');

            // Validate webhook URL
            $validation = $this->validateWebhookUrl($webhookUrl);
            if (!$validation['valid']) {
                return [
                    'success' => false,
                    'error' => $validation['error'],
                    'message' => 'Webhook setup failed: Invalid URL',
                ];
            }

            // Generate auth header for webhook security
            $authHeader = [
                'title' => 'X-Tabby-Signature',
                'value' => $this->generateWebhookSecret(),
            ];

            // Check if webhook already exists
            $existingWebhookId = get_payment_setting('webhook_id', TABBY_PAYMENT_METHOD_NAME);

            if ($existingWebhookId) {
                // Update existing webhook
                $response = $this->tabbyApiService->updateWebhook($existingWebhookId, $webhookUrl, $authHeader);
            } else {
                // Create new webhook
                $response = $this->tabbyApiService->registerWebhook($webhookUrl, $authHeader);

                // Store webhook ID for future reference
                Setting::set([
                    'payment_tabby_webhook_id' => $response['id'],
                    'payment_tabby_webhook_secret' => $authHeader['value'],
                ]);
                Setting::save();
            }

            Log::info('Tabby webhook setup successful', [
                'webhook_id' => $response['id'],
                'webhook_url' => $webhookUrl,
            ]);

            return [
                'success' => true,
                'webhook_id' => $response['id'],
                'webhook_url' => $webhookUrl,
                'message' => 'Webhook setup successful',
            ];
        } catch (Exception $exception) {
            Log::error('Tabby webhook setup failed', [
                'error' => $exception->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $exception->getMessage(),
                'message' => 'Webhook setup failed',
            ];
        }
    }

    /**
     * Remove webhook
     */
    public function removeWebhook(): array
    {
        try {
            $webhookId = get_payment_setting('webhook_id', TABBY_PAYMENT_METHOD_NAME);

            if (!$webhookId) {
                return [
                    'success' => true,
                    'message' => 'No webhook to remove',
                ];
            }

            $this->tabbyApiService->removeWebhook($webhookId);

            // Clear stored webhook data
            Setting::set([
                'payment_tabby_webhook_id' => '',
                'payment_tabby_webhook_secret' => '',
            ]);
            Setting::save();

            Log::info('Tabby webhook removed successfully', [
                'webhook_id' => $webhookId,
            ]);

            return [
                'success' => true,
                'message' => 'Webhook removed successfully',
            ];
        } catch (Exception $exception) {
            Log::error('Tabby webhook removal failed', [
                'error' => $exception->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $exception->getMessage(),
                'message' => 'Webhook removal failed',
            ];
        }
    }

    /**
     * Get webhook status
     */
    public function getWebhookStatus(): array
    {
        try {
            $webhookId = get_payment_setting('webhook_id', TABBY_PAYMENT_METHOD_NAME);

            if (!$webhookId) {
                return [
                    'exists' => false,
                    'message' => 'No webhook configured',
                ];
            }

            $webhook = $this->tabbyApiService->getWebhook($webhookId);

            return [
                'exists' => true,
                'webhook_id' => $webhook['id'],
                'webhook_url' => $webhook['url'],
                'is_test' => $webhook['is_test'],
                'message' => 'Webhook is active',
            ];
        } catch (Exception $exception) {
            return [
                'exists' => false,
                'error' => $exception->getMessage(),
                'message' => 'Webhook status check failed',
            ];
        }
    }

    /**
     * List all webhooks
     */
    public function getAllWebhooks(): array
    {
        try {
            return $this->tabbyApiService->getAllWebhooks();
        } catch (Exception $exception) {
            Log::error('Failed to retrieve webhooks', [
                'error' => $exception->getMessage(),
            ]);

            return [];
        }
    }

    /**
     * Auto setup webhook on environment change
     */
    public function autoSetupWebhook(): void
    {
        // Only setup webhook in production environment
        if (get_payment_setting('environment', TABBY_PAYMENT_METHOD_NAME) === 'live') {
            $this->setupWebhook();
        }
    }

    /**
     * Get development webhook instructions
     */
    public function getDevelopmentInstructions(): array
    {
        return [
            'message' => 'For development/testing, you can use tools like ngrok to expose your local server:',
            'steps' => [
                '1. Install ngrok: https://ngrok.com/',
                '2. Run: ngrok http 80 (or your local port)',
                '3. Copy the HTTPS URL (e.g., https://abc123.ngrok.io)',
                '4. Use this URL for webhook setup',
                '5. Your webhook URL will be: https://abc123.ngrok.io/payment/tabby/webhook',
            ],
            'alternatives' => [
                'Use a staging server with a real domain',
                'Use localhost tunneling services like ngrok, localtunnel, or serveo',
                'Deploy to a test environment with HTTPS',
            ],
        ];
    }

    /**
     * Generate secure webhook secret
     */
    protected function generateWebhookSecret(): string
    {
        return 'tabby_' . bin2hex(random_bytes(16)) . '_' . time();
    }

    /**
     * Validate webhook URL
     */
    protected function validateWebhookUrl(string $url): array
    {
        // Check if URL is HTTPS
        if (!str_starts_with($url, 'https://')) {
            return [
                'valid' => false,
                'error' => 'Webhook URL must use HTTPS protocol',
            ];
        }

        // Check if URL is publicly accessible (not localhost or local domains)
        $host = parse_url($url, PHP_URL_HOST);
        $localHosts = ['localhost', '127.0.0.1', '::1'];
        $localDomains = ['.local', '.test', '.dev', '.gc'];

        if (in_array($host, $localHosts)) {
            return [
                'valid' => false,
                'error' => 'Webhook URL cannot be localhost. Use a publicly accessible domain.',
            ];
        }

        foreach ($localDomains as $localDomain) {
            if (str_ends_with($host, $localDomain)) {
                return [
                    'valid' => false,
                    'error' => "Webhook URL cannot use local domain ({$localDomain}). Use a publicly accessible domain.",
                ];
            }
        }

        // Check if URL is reachable (optional - can be slow)
        // You can enable this for production
        /*
        $headers = @get_headers($url);
        if (!$headers) {
            return [
                'valid' => false,
                'error' => 'Webhook URL is not reachable',
            ];
        }
        */

        return [
            'valid' => true,
            'error' => null,
        ];
    }

    /**
     * Validate webhook signature
     */
    public function validateWebhookSignature(string $payload, string $signature): bool
    {
        $expectedSignature = get_payment_setting('webhook_secret', TABBY_PAYMENT_METHOD_NAME);

        if (!$expectedSignature) {
            return true; // No signature validation if not configured
        }

        return hash_equals($expectedSignature, $signature);
    }
}
