@if($payment)
    <div class="hyperpay-payment-details">
        <h6>{{ trans('plugins/hyperpay::hyperpay.payment_details') }}</h6>
        
        <div class="row">
            <div class="col-md-6">
                <table class="table table-striped">
                    <tr>
                        <td><strong>{{ trans('plugins/hyperpay::hyperpay.transaction_id') }}:</strong></td>
                        <td>{{ $payment['id'] ?? 'N/A' }}</td>
                    </tr>
                    <tr>
                        <td><strong>{{ trans('plugins/hyperpay::hyperpay.amount') }}:</strong></td>
                        <td>{{ $payment['amount'] ?? 'N/A' }} {{ $payment['currency'] ?? '' }}</td>
                    </tr>
                    <tr>
                        <td><strong>{{ trans('plugins/hyperpay::hyperpay.payment_type') }}:</strong></td>
                        <td>{{ $payment['paymentType'] ?? 'N/A' }}</td>
                    </tr>
                    <tr>
                        <td><strong>{{ trans('plugins/hyperpay::hyperpay.result_code') }}:</strong></td>
                        <td>
                            <span class="badge badge-{{ isset($payment['result']['code']) && str_starts_with($payment['result']['code'], '000.000') ? 'success' : 'warning' }}">
                                {{ $payment['result']['code'] ?? 'N/A' }}
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>{{ trans('plugins/hyperpay::hyperpay.result_description') }}:</strong></td>
                        <td>{{ $payment['result']['description'] ?? 'N/A' }}</td>
                    </tr>
                </table>
            </div>
            
            <div class="col-md-6">
                @if(isset($payment['card']))
                    <h6>{{ trans('plugins/hyperpay::hyperpay.card_details') }}</h6>
                    <table class="table table-striped">
                        <tr>
                            <td><strong>{{ trans('plugins/hyperpay::hyperpay.card_bin') }}:</strong></td>
                            <td>{{ $payment['card']['bin'] ?? 'N/A' }}</td>
                        </tr>
                        <tr>
                            <td><strong>{{ trans('plugins/hyperpay::hyperpay.card_last4') }}:</strong></td>
                            <td>**** **** **** {{ $payment['card']['last4Digits'] ?? 'N/A' }}</td>
                        </tr>
                        <tr>
                            <td><strong>{{ trans('plugins/hyperpay::hyperpay.card_holder') }}:</strong></td>
                            <td>{{ $payment['card']['holder'] ?? 'N/A' }}</td>
                        </tr>
                        <tr>
                            <td><strong>{{ trans('plugins/hyperpay::hyperpay.card_expiry') }}:</strong></td>
                            <td>{{ $payment['card']['expiryMonth'] ?? 'N/A' }}/{{ $payment['card']['expiryYear'] ?? 'N/A' }}</td>
                        </tr>
                    </table>
                @endif
            </div>
        </div>
        
        @if(isset($payment['timestamp']))
            <div class="mt-3">
                <small class="text-muted">
                    {{ trans('plugins/hyperpay::hyperpay.processed_at') }}: {{ $payment['timestamp'] }}
                </small>
            </div>
        @endif
        
        @if(isset($payment['merchantTransactionId']))
            <div class="mt-2">
                <small class="text-muted">
                    {{ trans('plugins/hyperpay::hyperpay.merchant_transaction_id') }}: {{ $payment['merchantTransactionId'] }}
                </small>
            </div>
        @endif
    </div>
@else
    <div class="alert alert-warning">
        {{ trans('plugins/hyperpay::hyperpay.no_payment_details') }}
    </div>
@endif
