@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">ERPNext Product Import Setup</h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-{{ $type }}">
                        <h5><i class="ti ti-alert-triangle"></i> Setup Required</h5>
                        <p>{{ $message }}</p>
                    </div>
                    
                    <div class="alert alert-info">
                        <h6>Setup Steps:</h6>
                        <ol>
                            <li>Go to ERPNext Settings</li>
                            <li>Enable ERPNext integration</li>
                            <li>Configure API URL, Key, and Secret</li>
                            <li>Test the connection</li>
                            <li>Return to this page to import products</li>
                        </ol>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <a href="{{ $settingsUrl }}" class="btn btn-primary">
                                <i class="ti ti-settings"></i> Go to ERPNext Settings
                            </a>
                        </div>
                        <div class="col-md-6">
                            <a href="{{ route('products.index') }}" class="btn btn-secondary">
                                <i class="ti ti-package"></i> View Products
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
