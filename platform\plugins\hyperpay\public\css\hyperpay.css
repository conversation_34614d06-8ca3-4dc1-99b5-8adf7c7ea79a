/* HyperPay Payment Gateway Styles */

.hyperpay-payment-form {
    background: #fff;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    padding: 20px;
    margin: 15px 0;
}

.payment-type-selection {
    margin: 15px 0;
}

.payment-type-selection .form-check {
    margin-bottom: 10px;
    padding: 12px;
    border: 1px solid #e1e5e9;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.payment-type-selection .form-check:hover {
    border-color: #007bff;
    background-color: #f8f9fa;
}

.payment-type-selection .form-check-input:checked + .form-check-label {
    color: #007bff;
    font-weight: 500;
}

.payment-type-selection .form-check-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    margin-bottom: 0;
    width: 100%;
}

.payment-type-selection .form-check-label img {
    margin-right: 10px;
    height: 24px;
    width: auto;
}

.payment-type-display {
    display: flex;
    align-items: center;
    padding: 15px;
    background: #f8f9fa;
    border: 1px solid #e1e5e9;
    border-radius: 6px;
    margin-bottom: 15px;
}

.payment-type-display img {
    margin-right: 10px;
    height: 32px;
    width: auto;
}

.hyperpay-checkout-container {
    margin: 20px 0;
    padding: 20px;
    border: 1px solid #e1e5e9;
    border-radius: 6px;
    background: #fff;
}

.hyperpay-loading {
    text-align: center;
    padding: 40px 20px;
}

.hyperpay-loading .spinner-border {
    width: 3rem;
    height: 3rem;
}

.hyperpay-error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    padding: 15px;
    border-radius: 6px;
    margin: 15px 0;
}

.hyperpay-success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
    padding: 15px;
    border-radius: 6px;
    margin: 15px 0;
}

/* Payment Widget Styles */
.paymentWidgets {
    margin: 20px 0;
}

.paymentWidgets .form-group {
    margin-bottom: 20px;
}

.paymentWidgets .form-control {
    border: 1px solid #ced4da;
    border-radius: 4px;
    padding: 10px 12px;
    font-size: 14px;
}

.paymentWidgets .form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.paymentWidgets .btn-primary {
    background-color: #007bff;
    border-color: #007bff;
    padding: 12px 30px;
    font-size: 16px;
    font-weight: 500;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.paymentWidgets .btn-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
    transform: translateY(-1px);
}

.paymentWidgets .btn-primary:disabled {
    background-color: #6c757d;
    border-color: #6c757d;
    cursor: not-allowed;
    transform: none;
}

/* Checkout Page Styles */
.checkout-container {
    max-width: 600px;
    margin: 50px auto;
    padding: 30px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    background: #fff;
}

.checkout-container h2 {
    color: #333;
    margin-bottom: 10px;
}

.checkout-container .text-muted {
    color: #6c757d !important;
    margin-bottom: 30px;
}

.payment-widget {
    margin: 20px 0;
    min-height: 300px;
}

.loading-spinner {
    text-align: center;
    padding: 40px;
}

.error-message {
    color: #dc3545;
    margin: 20px 0;
    text-align: center;
}

.error-message h4 {
    color: #dc3545;
    margin-bottom: 15px;
}

/* Payment Details Styles */
.hyperpay-payment-details {
    background: #fff;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    padding: 20px;
    margin: 15px 0;
}

.hyperpay-payment-details h6 {
    color: #333;
    margin-bottom: 20px;
    font-weight: 600;
}

.hyperpay-payment-details .table {
    margin-bottom: 0;
}

.hyperpay-payment-details .table td {
    border-top: 1px solid #e1e5e9;
    padding: 8px 12px;
    vertical-align: middle;
}

.hyperpay-payment-details .table td:first-child {
    font-weight: 500;
    color: #495057;
    width: 40%;
}

.hyperpay-payment-details .badge {
    font-size: 12px;
    padding: 4px 8px;
}

.hyperpay-payment-details .badge-success {
    background-color: #28a745;
}

.hyperpay-payment-details .badge-warning {
    background-color: #ffc107;
    color: #212529;
}

.hyperpay-payment-details .badge-danger {
    background-color: #dc3545;
}

/* Instructions Styles */
.hyperpay-instructions {
    background: #f8f9fa;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    padding: 25px;
    margin: 20px 0;
}

.hyperpay-instructions h5 {
    color: #333;
    margin-bottom: 20px;
    font-weight: 600;
}

.hyperpay-instructions h6 {
    color: #495057;
    margin-bottom: 15px;
    font-weight: 500;
}

.hyperpay-instructions ol,
.hyperpay-instructions ul {
    margin-bottom: 20px;
}

.hyperpay-instructions li {
    margin-bottom: 8px;
    line-height: 1.5;
}

.hyperpay-instructions .fas {
    margin-right: 8px;
}

.hyperpay-instructions .alert {
    margin-bottom: 15px;
}

.hyperpay-instructions .alert:last-child {
    margin-bottom: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .checkout-container {
        margin: 20px;
        padding: 20px;
    }
    
    .hyperpay-payment-form {
        padding: 15px;
    }
    
    .payment-type-selection .form-check-label img {
        height: 20px;
    }
    
    .payment-type-display img {
        height: 24px;
    }
    
    .hyperpay-instructions {
        padding: 20px;
    }
}

/* Animation for loading states */
@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}

.hyperpay-loading {
    animation: pulse 2s infinite;
}

/* Security indicators */
.security-indicator {
    display: flex;
    align-items: center;
    color: #28a745;
    font-size: 12px;
    margin-top: 10px;
}

.security-indicator .fas {
    margin-right: 5px;
}

/* Form validation styles */
.hyperpay-payment-form .is-invalid {
    border-color: #dc3545;
}

.hyperpay-payment-form .invalid-feedback {
    display: block;
    color: #dc3545;
    font-size: 12px;
    margin-top: 5px;
}
