<?php

return [
    // Customer New Order Email
    'customer_new_order_title' => 'Order successfully!',
    'customer_new_order_greeting' => 'Dear :customer_name,',
    'customer_new_order_message' => 'Thank you for purchasing our products, we will contact you via phone <strong>:customer_phone</strong> to confirm order!',
    'customer_new_order_customer_info' => 'Customer Information',
    'customer_new_order_name' => 'Name',
    'customer_new_order_phone' => 'Phone',
    'customer_new_order_email' => 'Email',
    'customer_new_order_address' => 'Address',
    'customer_new_order_products' => 'Here\'s what you ordered:',
    'customer_new_order_note' => 'Note',
    'customer_new_order_shipping_method' => 'Shipping Method',
    'customer_new_order_order_number' => 'Order number',
    'customer_new_order_payment_method' => 'Payment Method',

    // Admin New Order Email
    'admin_new_order_title' => 'You received a new order from :site_title',
    'admin_new_order_greeting' => 'Hi Admin,',
    'admin_new_order_message' => 'A new order was successfully placed by :customer_name',
    'admin_new_order_customer_info' => 'Customer Information',
    'admin_new_order_name' => 'Name',
    'admin_new_order_phone' => 'Phone',
    'admin_new_order_email' => 'Email',
    'admin_new_order_address' => 'Address',
    'admin_new_order_products' => 'Order detail:',
    'admin_new_order_note' => 'Note',
    'admin_new_order_total' => 'Total',
    'admin_new_order_sub_total' => 'Sub total',
    'admin_new_order_shipping_fee' => 'Shipping fee',
    'admin_new_order_discount' => 'Discount',
    'admin_new_order_coupon_code' => 'Coupon code',
    'admin_new_order_tax' => 'Tax',
    'admin_new_order_shipping_method' => 'Shipping method',
    'admin_new_order_payment_method' => 'Payment method',
    'admin_new_order_view_order' => 'View order detail',

    // Order Confirmation Email
    'order_confirm_title' => 'Order confirmed!',
    'order_confirm_greeting' => 'Hi :customer_name,',
    'order_confirm_message' => 'Your order has been confirmed. Thank you for purchasing our products!',
    'order_confirm_order_number' => 'Order number',
    'order_confirm_order_confirmed' => 'Order has been confirmed!',
    'order_confirm_customer_info' => 'Customer information',
    'order_confirm_name' => 'Name',
    'order_confirm_phone' => 'Phone',
    'order_confirm_email' => 'Email',
    'order_confirm_address' => 'Address',
    'order_confirm_products' => 'Order detail:',
    'order_confirm_note' => 'Note',
    'order_confirm_shipping_method' => 'Shipping method',
    'order_confirm_payment_method' => 'Payment method',

    // Order Cancel (Customer) Email
    'customer_cancel_order_title' => 'Your order has been cancelled',
    'customer_cancel_order_greeting' => 'Dear :customer_name,',
    'customer_cancel_order_message' => 'Your order <strong>:order_id</strong> has been canceled as you requested due to reason :cancellation_reason and your payment was cancelled too.',
    'customer_cancel_order_apology' => 'We\'re sorry to hear that you\'ve decided to cancel your order. If you have any questions or concerns, please don\'t hesitate to contact us.',
    'customer_cancel_order_order_cancelled' => 'Order has been cancelled!',
    'customer_cancel_order_reason' => 'Reason for cancellation: :cancellation_reason',
    'customer_cancel_order_customer_info' => 'Customer information',
    'customer_cancel_order_name' => 'Name',
    'customer_cancel_order_phone' => 'Phone',
    'customer_cancel_order_email' => 'Email',
    'customer_cancel_order_address' => 'Address',
    'customer_cancel_order_products' => 'Here\'s what you ordered:',
    'customer_cancel_order_note' => 'Note',

    // Order Cancel (Admin) Email
    'admin_cancel_order_title' => 'Order cancelled',
    'admin_cancel_order_greeting' => 'Hi :customer_name,',
    'admin_cancel_order_message' => 'Your order #:order_id has been cancelled by our staff.',
    'admin_cancel_order_order_cancelled' => 'Order has been cancelled!',
    'admin_cancel_order_reason' => 'Reason for cancellation: :cancellation_reason',
    'admin_cancel_order_customer_info' => 'Customer information',
    'admin_cancel_order_name' => 'Name',
    'admin_cancel_order_phone' => 'Phone',
    'admin_cancel_order_email' => 'Email',
    'admin_cancel_order_address' => 'Address',
    'admin_cancel_order_products' => 'Order detail:',
    'admin_cancel_order_note' => 'Note',
    'admin_cancel_order_order_summary' => 'Order Summary',

    // Order Cancellation to Admin Email
    'order_cancellation_to_admin_title' => 'Order #:order_id cancelled by customer',
    'order_cancellation_to_admin_greeting' => 'Hi Admin,',
    'order_cancellation_to_admin_message' => ':customer_name has cancelled order <strong>#:order_id</strong>. Reason: <strong>:cancellation_reason</strong>',
    'order_cancellation_to_admin_customer_info' => 'Customer information',
    'order_cancellation_to_admin_name' => 'Name',
    'order_cancellation_to_admin_phone' => 'Phone',
    'order_cancellation_to_admin_email' => 'Email',
    'order_cancellation_to_admin_address' => 'Address',
    'order_cancellation_to_admin_products' => 'Order detail:',
    'order_cancellation_to_admin_note' => 'Note',
    'order_cancellation_to_admin_view_order' => 'View order detail',
    'order_cancellation_to_admin_order_summary' => 'Order Summary',

    // Order Delivery Email
    'customer_delivery_order_title' => 'Your order is on the way',
    'customer_delivery_order_greeting' => 'Hi :customer_name,',
    'customer_delivery_order_message' => 'Your order is on the way. Thank you for purchasing our products!',
    'customer_delivery_order_order_number' => 'Order number',
    'customer_delivery_order_tracking' => 'Your order is being delivered!',
    'customer_delivery_order_customer_info' => 'Customer information',
    'customer_delivery_order_name' => 'Name',
    'customer_delivery_order_phone' => 'Phone',
    'customer_delivery_order_email' => 'Email',
    'customer_delivery_order_address' => 'Address',
    'customer_delivery_order_products' => 'Order detail:',
    'customer_delivery_order_note' => 'Note',
    'customer_delivery_order_delivery_notes' => 'Delivery notes',
    'customer_delivery_order_order_summary' => 'Order Summary',

    // Order Delivered Email
    'customer_order_delivered_title' => 'Your order has been delivered',
    'customer_order_delivered_greeting' => 'Hi :customer_name,',
    'customer_order_delivered_message' => 'Your order has been delivered. Thank you for purchasing our products!',
    'customer_order_delivered_order_number' => 'Order number',
    'customer_order_delivered_success' => 'Order delivered successfully!',
    'customer_order_delivered_customer_info' => 'Customer information',
    'customer_order_delivered_name' => 'Name',
    'customer_order_delivered_phone' => 'Phone',
    'customer_order_delivered_email' => 'Email',
    'customer_order_delivered_address' => 'Address',
    'customer_order_delivered_products' => 'Order detail:',
    'customer_order_delivered_note' => 'Note',
    'customer_order_delivered_order_summary' => 'Order Summary',

    // Payment Confirmation Email
    'order_confirm_payment_title' => 'Payment confirmed for order #:order_id',
    'order_confirm_payment_greeting' => 'Hi :customer_name,',
    'order_confirm_payment_message' => 'Your payment has been confirmed. Thank you for purchasing our products!',
    'order_confirm_payment_order_number' => 'Order number',
    'order_confirm_payment_success' => 'Payment has been confirmed!',
    'order_confirm_payment_customer_info' => 'Customer information',
    'order_confirm_payment_name' => 'Name',
    'order_confirm_payment_phone' => 'Phone',
    'order_confirm_payment_email' => 'Email',
    'order_confirm_payment_address' => 'Address',
    'order_confirm_payment_products' => 'Order detail:',
    'order_confirm_payment_note' => 'Note',
    'order_confirm_payment_thanks' => 'Thank you for your payment. Your order will be processed soon.',
    'order_confirm_payment_order_summary' => 'Order Summary',
    'order_confirm_payment_shipping_method' => 'Shipping method',

    // Order Recovery Email
    'order_recover_title' => 'You have an incomplete order',
    'order_recover_greeting' => 'Hi :customer_name,',
    'order_recover_message' => 'We noticed you have an incomplete order. Would you like to complete your purchase?',
    'order_recover_items_waiting' => 'These items are waiting for you:',
    'order_recover_complete_order' => 'Complete your order',
    'order_recover_or_visit' => 'or visit our shop',
    'order_recover_button' => 'Complete Your Order',
    'order_recover_order_summary' => 'Order Summary',
    'order_recover_note' => 'Note',

    // Welcome Email
    'welcome_greeting' => 'We\'re glad to have you here, :customer_name!',
    'welcome_message' => 'Welcome to :site_title!',
    'welcome_register_success' => 'You have successfully registered an account at <strong>:site_title</strong>.',
    'welcome_explore' => 'If you need any help, feel free to response to this email!',
    'welcome_visit_shop' => 'Start buying!',

    // Confirm Email
    'confirm_email_title' => 'Verify Email',
    'confirm_email_greeting' => 'We\'re glad to have you here, :customer_name!',
    'confirm_email_message' => 'Please confirm your email address by clicking the button below.',
    'confirm_email_instruction' => 'Please verify your email address in order to access this website. Click on the button below to verify your email..',
    'confirm_email_button' => 'Confirm your email address',
    'confirm_email_trouble' => 'If you\'re having trouble clicking the "Confirm your email address" button, copy and paste the URL below into your web browser: <a href=":verify_link">:verify_link</a> and paste it into your browser.',

    // Password Reminder Email
    'password_reminder_title' => 'Reset your password',
    'password_reminder_greeting' => 'Hi :customer_name,',
    'password_reminder_message' => 'You have requested to reset your password.',
    'password_reminder_instruction' => 'Click the button below to reset your password:',
    'password_reminder_button' => 'Reset Password',
    'password_reminder_ignore' => 'If you did not request a password reset, please ignore this email.',
    'password_reminder_trouble_clicking' => 'If you\'re having trouble clicking the "Reset Password" button, copy and paste the URL below into your web browser: <a href=":reset_link">:reset_link</a>',

    // Order Return Request Email
    'order_return_request_title' => 'Return request received',
    'order_return_request_greeting' => 'Hi :customer_name,',
    'order_return_request_message' => 'We have received your return request for order #:order_id.',
    'order_return_request_reason' => 'Reason for return',
    'order_return_request_items' => 'Items to be returned',
    'order_return_request_process' => 'We will process your request and contact you soon.',
    'order_return_request_customer_info' => 'Customer information',
    'order_return_request_name' => 'Name',
    'order_return_request_phone' => 'Phone',
    'order_return_request_email' => 'Email',
    'order_return_request_address' => 'Address',
    'order_return_request_return_reason' => 'Return Reason',
    'order_return_request_order_summary' => 'Order Summary',
    'order_return_request_note' => 'Note',

    // Order Return Status Updated Email
    'order_return_status_updated_title' => 'Order return status update',
    'order_return_status_updated_greeting' => 'Dear :customer_name,',
    'order_return_status_updated_message' => 'We wanted to inform you that the status of your return request for order :order_id has been updated.',
    'order_return_status_updated_new_status' => 'The new status of your return request is: :status.',
    'order_return_status_updated_description' => 'Moderator\'s note: " :description ".',
    'order_return_status_updated_questions' => 'If you have any questions or concerns regarding this update, please don\'t hesitate to contact our customer support team.',
    'order_return_status_updated_view_details' => 'View return details',

    // Review Products Email
    'review_products_title' => 'Review your recent purchase',
    'review_products_greeting' => 'Hi :customer_name,',
    'review_products_message' => 'Your order #:order_id has been completed. We would love to hear your feedback!',
    'review_products_instruction' => 'Please take a moment to review the products you purchased:',
    'review_products_button' => 'Review Products',
    'review_products_thank_you' => 'Thank you for your purchase!',
    'review_products_products' => 'Products to Review',

    // Digital Products Download Email
    'download_digital_products_title' => 'Your digital products are ready',
    'download_digital_products_greeting' => 'Hi :customer_name,',
    'download_digital_products_message' => 'Thank you for your purchase! Your digital products are ready for download.',
    'download_digital_products_thanks' => 'Thank you for your purchase!',
    'download_digital_products_instruction' => 'Click the links below to download your products:',
    'download_digital_products_order_number' => 'Order number',
    'download_digital_products_order_summary' => 'Download Your Products',
    'download_digital_products_download' => 'Download',
    'download_digital_products_all_files' => 'Download All Files',
    'download_digital_products_external_link_downloads' => 'Download External Files',
    'download_digital_products_payment_method' => 'Payment Method',

    // Digital Product License Codes Email
    'digital_product_license_codes_title' => 'Your License Codes',
    'digital_product_license_codes_greeting' => 'Dear :customer_name,',
    'digital_product_license_codes_message' => 'Thank you for your purchase! Here are your license codes:',
    'digital_product_license_codes_thanks' => 'Thank you for purchasing our digital products.',
    'digital_product_license_codes_below' => 'Below are your license codes for the products you have purchased:',
    'digital_product_license_codes_product' => 'Product',
    'digital_product_license_codes_license' => 'License Code',
    'digital_product_license_codes_order_summary' => 'Here\'s what you ordered:',
    'digital_product_license_codes_product_image' => 'Product Image',
    'digital_product_license_codes_product_name' => 'Product Name',
    'digital_product_license_codes_na' => 'N/A',
    'digital_product_license_codes_payment_method' => 'Payment Method',
    'digital_product_license_codes_important_notes' => 'Important Notes:',
    'digital_product_license_codes_note_1' => 'Please keep your license codes safe and secure',
    'digital_product_license_codes_note_2' => 'Each license code is unique and can only be used once',
    'digital_product_license_codes_note_3' => 'Do not share your license codes with others',
    'digital_product_license_codes_note_4' => 'If you have any issues with your license codes, please contact our support team',

    // Product File Updated Email
    'product_file_updated_title' => 'Product Files Updated',
    'product_file_updated_greeting' => 'Hello, :customer_name!',
    'product_file_updated_message' => 'The files for the product <a href=":product_link"><strong>:product_name</strong></a> have been updated.',
    'product_file_updated_update_time' => 'Update time: :update_time',
    'product_file_updated_updated_files' => 'Updated files:',
    'product_file_updated_download_message' => 'You can download the updated files from the following link:',
    'product_file_updated_thank_you' => 'Thank you for your attention.',
    'product_file_updated_questions' => 'If you have any questions, feel free to contact us.',
    'product_file_updated_instruction' => 'You can download the updated files from your account.',
    'product_file_updated_order_number' => 'Order number',
    'product_file_updated_download' => 'Download Updated Files',

    // Invoice Payment Created Email
    'invoice_payment_created_title' => 'Payment received',
    'invoice_payment_created_greeting' => 'Hi :customer_name,',
    'invoice_payment_created_message' => 'We have successfully received your payment for :site_title. Thank you!',
    'invoice_payment_created_invoice_info' => 'Invoice Information',
    'invoice_payment_created_invoice_code' => 'Invoice Code',
    'invoice_payment_created_amount' => 'Amount',
    'invoice_payment_created_payment_method' => 'Payment Method',
    'invoice_payment_created_view_invoice' => 'View Invoice',
    'invoice_payment_created_invoice_link_message' => 'You can view your invoice online at <a href=":invoice_link">:invoice_link</a> with invoice code #:invoice_code',
    'invoice_payment_created_invoice_message' => 'Your invoice code is #:invoice_code',
    'invoice_payment_created_view_online' => 'View Invoice Online',

    // Customer Deletion Request Confirmation Email
    'customer_deletion_request_confirmation_greeting' => 'Hi :customer_name,',
    'customer_deletion_request_confirmation_message' => 'We have received your request to delete your account.',
    'customer_deletion_request_confirmation_warning' => 'Please note that this action cannot be undone. All your data will be permanently deleted.',
    'customer_deletion_request_confirmation_button' => 'Confirm Deletion',
    'customer_deletion_request_confirmation_cancel' => 'If you did not request this, please ignore this email.',

    // Customer Deletion Request Completed Email
    'customer_deletion_request_completed_greeting' => 'Hi :customer_name,',
    'customer_deletion_request_completed_message' => 'Your account has been successfully deleted as requested.',
    'customer_deletion_request_completed_thank_you' => 'Thank you for being with us.',
    'customer_deletion_request_completed_goodbye' => 'We hope to see you again in the future.',

    // Payment Proof Upload Notification Email
    'payment_proof_upload_notification_greeting' => 'Hello Admin,',
    'payment_proof_upload_notification_message' => 'The customer :customer_name (Email: <a href="mailto::customer_email">:customer_email</a>) has uploaded a payment proof for their order with ID :order_id.',
    'payment_proof_upload_notification_view_details' => 'You can view the payment details <a href=":payment_link">here</a> and the order details <a href=":order_link">here</a>.',
    'payment_proof_upload_notification_customer_info' => 'Customer Information',
    'payment_proof_upload_notification_name' => 'Name',
    'payment_proof_upload_notification_email' => 'Email',
    'payment_proof_upload_notification_order_info' => 'Order Information',
    'payment_proof_upload_notification_order_number' => 'Order Number',
    'payment_proof_upload_notification_total' => 'Total Amount',
    'payment_proof_upload_notification_view_order' => 'View Order',
    'payment_proof_upload_notification_view_proof' => 'View Payment Proof',
];
