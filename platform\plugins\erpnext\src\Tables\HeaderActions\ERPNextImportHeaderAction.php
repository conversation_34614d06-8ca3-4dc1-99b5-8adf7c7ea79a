<?php

namespace Shaqi\ERPNext\Tables\HeaderActions;

use Botble\Table\HeaderActions\HeaderAction;

class ERPNextImportHeaderAction extends HeaderAction
{
    public static function make(string $name = 'erpnext-import'): static
    {
        return parent::make($name)
            ->icon('ti ti-cloud-download')
            ->label('Import from ERPNext')
            ->color('info')
            ->route('erpnext.product-import.index')
            ->permission('products.index');
    }
}
