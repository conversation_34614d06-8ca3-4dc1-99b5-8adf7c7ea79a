@if (setting('payment_hyperpay_status') == 1)
    @php
        $hyperPayService = new \Botble\HyperPay\Services\Gateways\HyperPayPaymentService();
        $availablePaymentTypes = $hyperPayService->getAvailablePaymentTypes();
    @endphp

    <x-plugins-payment::payment-method
        :name="HYPERPAY_PAYMENT_METHOD_NAME"
        paymentName="HyperPay"
        :supportedCurrencies="$hyperPayService->supportedCurrencyCodes()"
    >
        <div class="hyperpay-payment-form" style="max-width: 500px">
            @if(count($availablePaymentTypes) > 1)
                <div class="form-group mb-3">
                    <label class="form-label">{{ trans('plugins/hyperpay::hyperpay.select_payment_type') }}</label>
                    <div class="payment-type-selection">
                        @foreach($availablePaymentTypes as $type => $label)
                            <div class="form-check">
                                <input 
                                    class="form-check-input" 
                                    type="radio" 
                                    name="hyperpay_payment_type" 
                                    id="hyperpay_{{ $type }}" 
                                    value="{{ $type }}"
                                    {{ $loop->first ? 'checked' : '' }}
                                >
                                <label class="form-check-label" for="hyperpay_{{ $type }}">
                                    <img src="{{ url('vendor/core/plugins/hyperpay/images/' . $type . '.png') }}" 
                                         alt="{{ $label }}" 
                                         style="height: 24px; margin-right: 8px;">
                                    {{ $label }}
                                </label>
                            </div>
                        @endforeach
                    </div>
                </div>
            @else
                @foreach($availablePaymentTypes as $type => $label)
                    <input type="hidden" name="hyperpay_payment_type" value="{{ $type }}">
                    <div class="payment-type-display mb-3">
                        <img src="{{ url('vendor/core/plugins/hyperpay/images/' . $type . '.png') }}" 
                             alt="{{ $label }}" 
                             style="height: 32px; margin-right: 8px;">
                        <span>{{ $label }}</span>
                    </div>
                @endforeach
            @endif

            <div class="hyperpay-checkout-container" id="hyperpay-checkout-container" style="display: none;">
                <!-- HyperPay widget will be loaded here -->
            </div>

            <div class="alert alert-info mt-3">
                <i class="fas fa-info-circle"></i>
                {{ trans('plugins/hyperpay::hyperpay.payment_info') }}
            </div>
        </div>

        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const paymentTypeInputs = document.querySelectorAll('input[name="hyperpay_payment_type"]');
                
                paymentTypeInputs.forEach(function(input) {
                    input.addEventListener('change', function() {
                        // Update the selected payment type
                        console.log('Selected payment type:', this.value);
                    });
                });
            });
        </script>
    </x-plugins-payment::payment-method>
@endif
