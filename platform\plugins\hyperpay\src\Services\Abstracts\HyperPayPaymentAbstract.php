<?php

namespace Botble\HyperPay\Services\Abstracts;

use Botble\Payment\Services\Traits\PaymentErrorTrait;
use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

abstract class HyperPayPaymentAbstract
{
    use PaymentErrorTrait;

    protected float $amount;
    protected string $currency;
    protected string $checkoutId;
    protected bool $supportRefundOnline = false;
    protected array $supportedCurrencies = ['SAR', 'AED', 'USD', 'EUR'];

    public function getSupportRefundOnline(): bool
    {
        return $this->supportRefundOnline;
    }

    public function supportedCurrencyCodes(): array
    {
        return $this->supportedCurrencies;
    }

    public function execute(array $data): ?string
    {
        try {
            return $this->makePayment($data);
        } catch (Exception $exception) {
            $this->setErrorMessageAndLogging($exception, 1);
            return null;
        }
    }

    abstract public function makePayment(array $data): ?string;

    abstract public function afterMakePayment(array $data): ?string;

    public function getApiUrl(): string
    {
        $isSandbox = get_payment_setting('sandbox_mode', HYPERPAY_PAYMENT_METHOD_NAME, false);

        return $isSandbox
            ? 'https://eu-test.oppwa.com/v1'
            : 'https://eu-prod.oppwa.com/v1';
    }

    public function getAccessToken(): ?string
    {
        return get_payment_setting('access_token', HYPERPAY_PAYMENT_METHOD_NAME);
    }

    public function getEntityId(string $paymentType = 'visa'): ?string
    {
        $entityIdKey = match($paymentType) {
            'visa', 'master' => 'visa_entity_id',
            'mada' => 'mada_entity_id',
            'amex' => 'amex_entity_id',
            'applepay' => 'applepay_entity_id',
            default => 'visa_entity_id'
        };

        return get_payment_setting($entityIdKey, HYPERPAY_PAYMENT_METHOD_NAME);
    }

    public function createCheckout(array $data): array
    {
        $url = $this->getApiUrl() . '/checkouts';
        $accessToken = $this->getAccessToken();
        $entityId = $this->getEntityId($data['payment_type'] ?? 'visa');

        if (!$accessToken || !$entityId) {
            throw new Exception('HyperPay configuration is incomplete. Please check access token and entity IDs.');
        }

        // Validate amount - must be greater than 0
        $amount = (float) $data['amount'];
        if ($amount <= 0) {
            throw new Exception('Invalid amount: Amount must be greater than 0');
        }

        $formattedAmount = number_format($amount, 2, '.', '');
        $currency = $data['currency'] ?? get_payment_setting('currency', HYPERPAY_PAYMENT_METHOD_NAME, 'SAR');

        // Ensure merchantTransactionId is a string, not an array
        $merchantTransactionId = $data['order_id'] ?? uniqid('order_');

        // Handle array case safely
        if (is_array($merchantTransactionId)) {
            if (!empty($merchantTransactionId) && count($merchantTransactionId) > 0) {
                $firstElement = reset($merchantTransactionId); // Get first element safely
                $merchantTransactionId = is_numeric($firstElement) ? (string) $firstElement : uniqid('order_');
            } else {
                $merchantTransactionId = uniqid('order_');
            }
        } else {
            $merchantTransactionId = (string) $merchantTransactionId;
        }

        // Basic required parameters for HyperPay
        $postData = [
            'entityId' => trim($entityId),
            'amount' => $formattedAmount,
            'currency' => strtoupper($currency),
            'paymentType' => 'DB',
            'merchantTransactionId' => $merchantTransactionId,
        ];

        // Add shopperResultUrl - this is often required
        $shopperResultUrl = route('payments.hyperpay.callback');
        $postData['shopperResultUrl'] = $shopperResultUrl;

        // Add customer information if available (but make them optional)
        if (!empty($data['customer_email']) && filter_var($data['customer_email'], FILTER_VALIDATE_EMAIL)) {
            $postData['customer.email'] = $data['customer_email'];
        }

        if (!empty($data['customer_name'])) {
            $customerName = $data['customer_name'];

            // Handle array case safely
            if (is_array($customerName)) {
                if (!empty($customerName) && count($customerName) > 0) {
                    $customerName = reset($customerName); // Get first element safely
                } else {
                    $customerName = null;
                }
            }

            if (!empty($customerName)) {
                $name = trim((string) $customerName);
                if (!empty($name)) {
                    $nameParts = explode(' ', $name, 2);
                    $postData['customer.givenName'] = $nameParts[0];
                    $postData['customer.surname'] = isset($nameParts[1]) ? $nameParts[1] : $nameParts[0];
                }
            }
        }

        // Add billing information if available (optional)
        if (!empty($data['billing_address'])) {
            $billingAddress = $data['billing_address'];
            if (is_array($billingAddress)) {
                $billingAddress = !empty($billingAddress) && count($billingAddress) > 0 ? reset($billingAddress) : null;
            }
            if (!empty($billingAddress)) {
                $postData['billing.street1'] = substr(trim((string) $billingAddress), 0, 50);
            }
        }

        if (!empty($data['billing_city'])) {
            $billingCity = $data['billing_city'];
            if (is_array($billingCity)) {
                $billingCity = !empty($billingCity) && count($billingCity) > 0 ? reset($billingCity) : null;
            }
            if (!empty($billingCity)) {
                $postData['billing.city'] = substr(trim((string) $billingCity), 0, 32);
                $postData['billing.state'] = substr(trim((string) $billingCity), 0, 32);
            }
        }

        if (!empty($data['billing_country'])) {
            $billingCountry = $data['billing_country'];
            if (is_array($billingCountry)) {
                $billingCountry = !empty($billingCountry) && count($billingCountry) > 0 ? reset($billingCountry) : null;
            }
            if (!empty($billingCountry)) {
                $country = strtoupper(trim((string) $billingCountry));
                if (strlen($country) === 2) {
                    $postData['billing.country'] = $country;
                }
            }
        }

        if (!empty($data['billing_postcode'])) {
            $billingPostcode = $data['billing_postcode'];
            if (is_array($billingPostcode)) {
                $billingPostcode = !empty($billingPostcode) && count($billingPostcode) > 0 ? reset($billingPostcode) : null;
            }
            if (!empty($billingPostcode)) {
                $postData['billing.postcode'] = substr(trim((string) $billingPostcode), 0, 10);
            }
        }

        // Log the request for debugging
        Log::info('HyperPay Checkout Request', [
            'url' => $url,
            'entity_id' => $entityId,
            'amount' => $formattedAmount,
            'currency' => $currency,
            'payment_type' => $data['payment_type'] ?? 'visa',
            'data' => $postData
        ]);

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $accessToken,
                'Content-Type' => 'application/x-www-form-urlencoded',
            ])->asForm()->post($url, $postData);

            $responseData = $response->json();

            Log::info('HyperPay Checkout Response', [
                'status_code' => $response->status(),
                'response' => $responseData
            ]);

            // Check if response is successful
            if ($response->successful() && isset($responseData['result']['code'])) {
                $resultCode = $responseData['result']['code'];

                // Success code for checkout creation
                if ($resultCode === '000.200.100') {
                    return $responseData;
                }

                // Log the specific error
                $errorMessage = $responseData['result']['description'] ?? 'Unknown error';
                Log::error('HyperPay API Error', [
                    'result_code' => $resultCode,
                    'description' => $errorMessage,
                    'request_data' => $postData
                ]);

                throw new Exception("HyperPay checkout creation failed: {$errorMessage} (Code: {$resultCode})");
            } else {
                $statusCode = $response->status();
                $errorMessage = $responseData['result']['description'] ?? $response->body();

                Log::error('HyperPay HTTP Error', [
                    'status_code' => $statusCode,
                    'response' => $responseData,
                    'request_data' => $postData
                ]);

                throw new Exception("HyperPay API request failed with status {$statusCode}: {$errorMessage}");
            }
        } catch (Exception $e) {
            Log::error('HyperPay Checkout Exception', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $postData
            ]);
            throw $e;
        }
    }

    public function getPaymentStatus(string $resourcePath, string $checkoutId): array
    {
        // Based on reference implementation - use the resourcePath directly
        // The resourcePath already contains all necessary information
        $baseUrl = rtrim($this->getApiUrl(), '/');
        if (str_starts_with($resourcePath, '/v1/')) {
            // Remove /v1 from base URL to avoid duplication
            $baseUrl = str_replace('/v1', '', $baseUrl);
        }
        $url = $baseUrl . $resourcePath;

        $accessToken = $this->getAccessToken();

        if (!$accessToken) {
            return [
                'error' => true,
                'message' => 'HyperPay access token is not configured.'
            ];
        }

        try {
            // Based on reference implementation - get entityId from session payment type
            // Reference: HttpParameters::getParams() method
            $paymentType = session('hyperpay_payment_type', 'visa');
            $entityId = $this->getEntityId($paymentType);

            if (!$entityId) {
                return [
                    'error' => true,
                    'message' => 'HyperPay entityId is not configured for payment type: ' . $paymentType
                ];
            }

            // Reference implementation uses getParams() which returns ['entityId' => $entityId]
            $queryParams = ['entityId' => $entityId];

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $accessToken,
            ])->get($url, $queryParams);

            $responseData = $response->json();

            Log::info('HyperPay Payment Status Check', [
                'url' => $url,
                'checkout_id' => $checkoutId,
                'resource_path' => $resourcePath,
                'payment_type' => $paymentType,
                'entity_id' => $entityId,
                'query_params' => $queryParams,
                'status_code' => $response->status(),
                'response' => $responseData
            ]);

            // Ensure we always return an array
            if (!is_array($responseData)) {
                return [
                    'error' => true,
                    'message' => 'Invalid response from HyperPay API',
                    'response' => $responseData
                ];
            }

            return $responseData;
        } catch (Exception $e) {
            Log::error('HyperPay Payment Status Error', [
                'error' => $e->getMessage(),
                'checkout_id' => $checkoutId,
                'url' => $url,
                'resource_path' => $resourcePath,
                'payment_type' => session('hyperpay_payment_type', 'visa')
            ]);

            return [
                'error' => true,
                'message' => $e->getMessage()
            ];
        }
    }

    public function isSuccessfulPayment(string $resultCode): bool
    {
        // Success patterns from HyperPay documentation
        $successPatterns = [
            '/^(000\.000\.|000\.100\.1|000\.[36])/',  // Successful payments
        ];

        foreach ($successPatterns as $pattern) {
            if (preg_match($pattern, $resultCode)) {
                return true;
            }
        }

        return false;
    }

    public function isPendingPayment(string $resultCode): bool
    {
        // Pending patterns from HyperPay documentation
        $pendingPatterns = [
            '/^(000\.200)/',                          // Pending payments
            '/^(800\.400\.5|100\.400\.500)/',        // Pending v2 payments
        ];

        foreach ($pendingPatterns as $pattern) {
            if (preg_match($pattern, $resultCode)) {
                return true;
            }
        }

        return false;
    }

    public function setCurrency(string $currency): static
    {
        $this->currency = $currency;
        return $this;
    }

    public function setAmount(float $amount): static
    {
        $this->amount = $amount;
        return $this;
    }
}
