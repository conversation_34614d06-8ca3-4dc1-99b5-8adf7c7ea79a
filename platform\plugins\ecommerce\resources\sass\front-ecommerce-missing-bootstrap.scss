@use 'ultils' as *;

.#{$prefix}customer-page {
    /* Base styling for Bootstrap-like tables in Tailwind */
    .table {
        width: 100%;
        margin-bottom: 1rem;
        color: #212529;
    }

    .table th,
    .table td {
        padding: 0.75rem;
        vertical-align: top;
        border-top: 1px solid #dee2e6;
    }

    .table thead th {
        vertical-align: bottom;
        border-bottom: 2px solid #dee2e6;
    }

    .table tbody + tbody {
        border-top: 2px solid #dee2e6;
    }

    .table-striped tbody tr:nth-of-type(odd) {
        background-color: rgba(0, 0, 0, 0.05);
    }

    .table-hover tbody tr:hover {
        background-color: rgba(0, 0, 0, 0.075);
    }

    .table-bordered {
        border: 1px solid #dee2e6;
    }

    .table-bordered th,
    .table-bordered td {
        border: 1px solid #dee2e6;
    }

    .table-borderless th,
    .table-borderless td,
    .table-borderless thead th,
    .table-borderless tbody + tbody {
        border: 0;
    }

    /* Base badge styling */
    .badge {
        display: inline-block;
        padding: 0.25em 0.4em;
        font-size: 75%;
        font-weight: 700;
        line-height: 1;
        text-align: center;
        white-space: nowrap;
        vertical-align: baseline;
        border-radius: 0.25rem;
    }

    /* Color variations */
    .badge-primary {
        color: #fff;
        background-color: #007bff;
    }

    .badge-secondary {
        color: #fff;
        background-color: #6c757d;
    }

    .badge-success {
        color: #fff;
        background-color: #28a745;
    }

    .badge-danger {
        color: #fff;
        background-color: #dc3545;
    }

    .badge-warning {
        color: #212529;
        background-color: #ffc107;
    }

    .badge-info {
        color: #fff;
        background-color: #17a2b8;
    }

    .badge-light {
        color: #212529;
        background-color: #f8f9fa;
    }

    .badge-dark {
        color: #fff;
        background-color: #343a40;
    }

    /* Pill badges */
    .badge-pill {
        padding-right: 0.6em;
        padding-left: 0.6em;
        border-radius: 10rem;
    }
    /* Background color utility classes */
    .bg-primary {
        background-color: #007bff !important;
        color: #fff;
    }

    .bg-secondary {
        background-color: #6c757d !important;
        color: #fff;
    }

    .bg-success {
        background-color: #28a745 !important;
        color: #fff;
    }

    .bg-danger {
        background-color: #dc3545 !important;
        color: #fff;
    }

    .bg-warning {
        background-color: #ffc107 !important;
        color: #212529;
    }

    .bg-info {
        background-color: #17a2b8 !important;
        color: #fff;
    }

    .bg-light {
        background-color: #f8f9fa !important;
        color: #212529;
    }

    .bg-dark {
        background-color: #343a40 !important;
        color: #fff;
    }

    /* Optionally, add text color classes to prevent adding extra color styles */
    .text-white {
        color: #fff !important;
    }

    .text-dark {
        color: #212529 !important;
    }
}
