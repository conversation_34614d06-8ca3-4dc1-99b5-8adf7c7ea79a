name: tests

on:
    push:
    pull_request:
    schedule:
        - cron: "0 0 * * *"

jobs:
    tests:
        runs-on: ubuntu-20.04

        strategy:
            fail-fast: true
            matrix:
                php: [7.3, 7.4]
                laravel: [^7.0]

        name: PHP ${{ matrix.php }} - <PERSON><PERSON> ${{ matrix.laravel }}

        steps:
            - name: Checkout code
              uses: actions/checkout@v2

            - name: Setup PHP
              uses: shivammathur/setup-php@v2
              with:
                  php-version: ${{ matrix.php }}
                  extensions: dom, curl, libxml, mbstring, zip
                  tools: composer:v1
                  coverage: none

            - name: Install dependencies
              run: |
                  composer update --prefer-dist --no-interaction --no-progress

            - name: Execute tests
              run: vendor/bin/phpunit --verbose
              env:
                  STRIPE_SECRET: ${{ secrets.STRIPE_SECRET }}
