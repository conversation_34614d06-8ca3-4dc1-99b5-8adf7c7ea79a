<?php

namespace Shaqi\ERPNext\Http\Requests;

use Botble\Support\Http\Requests\Request;

class ProductImportRequest extends Request
{
    public function rules(): array
    {
        return [
            'item_groups' => 'required|array|min:1',
            'item_groups.*' => 'required|string|max:255',
        ];
    }

    public function messages(): array
    {
        return [
            'item_groups.required' => 'Please select at least one Item Group to import products from.',
            'item_groups.array' => 'Item Groups must be an array.',
            'item_groups.min' => 'Please select at least one Item Group.',
            'item_groups.*.required' => 'Each Item Group name is required.',
            'item_groups.*.string' => 'Each Item Group name must be a string.',
            'item_groups.*.max' => 'Each Item Group name must not exceed 255 characters.',
        ];
    }

    public function attributes(): array
    {
        return [
            'item_groups' => 'Item Groups',
            'item_groups.*' => 'Item Group',
        ];
    }
}
