<?php

namespace Shaqi\ERPNext\Http\Controllers;

use Botble\Base\Http\Controllers\BaseController;
use Botble\Base\Http\Responses\BaseHttpResponse;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Shaqi\ERPNext\Services\ERPNextService;
use Shaqi\ERPNext\Services\ERPNextProductImportService;
use Shaqi\ERPNext\Services\ERPNextLogger;

use Shaqi\ERPNext\Http\Requests\ProductImportRequest;

class ProductImportController extends BaseController
{
    public function index()
    {
        $this->pageTitle('Import Products from ERPNext');

        // Check if ERPNext is enabled and configured
        if (!setting('enable_erpnext')) {
            return view('plugins/erpnext::product-import.setup', [
                'message' => 'ERPNext integration is not enabled. Please enable and configure it first.',
                'type' => 'warning',
                'settingsUrl' => route('erpnext.settings')
            ]);
        }

        if (!setting('erpnext_api_url') || !setting('erpnext_api_key') || !setting('erpnext_api_secret')) {
            return view('plugins/erpnext::product-import.setup', [
                'message' => 'ERPNext API credentials are not configured. Please configure them first.',
                'type' => 'warning',
                'settingsUrl' => route('erpnext.settings')
            ]);
        }

        // Show the actual import form
        return view('plugins/erpnext::product-import.index');
    }

    /**
     * Get Item Groups from ERPNext via AJAX
     */
    public function getItemGroups(): JsonResponse
    {
        try {
            ERPNextLogger::info("Fetching Item Groups for import form");

            $itemGroups = ERPNextService::getItemGroups();

            // Format for select dropdown
            $formattedGroups = [];
            foreach ($itemGroups as $group) {
                $formattedGroups[] = [
                    'value' => $group['name'],
                    'label' => $group['item_group_name'] ?? $group['name'],
                    'is_group' => $group['is_group'] ?? 0
                ];
            }

            return response()->json([
                'error' => false,
                'data' => $formattedGroups,
                'message' => 'Item Groups fetched successfully'
            ]);

        } catch (\Exception $e) {
            ERPNextLogger::error("Failed to fetch Item Groups", [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'error' => true,
                'message' => 'Failed to fetch Item Groups: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Preview products from selected Item Groups
     */
    public function previewProducts(Request $request): JsonResponse
    {
        try {
            $selectedGroups = $request->input('item_groups', []);

            if (empty($selectedGroups)) {
                return response()->json([
                    'error' => true,
                    'message' => 'Please select at least one Item Group'
                ], 400);
            }

            ERPNextLogger::info("Previewing products for selected groups", [
                'groups' => $selectedGroups
            ]);

            $products = ERPNextService::getProductsByItemGroups($selectedGroups);

            // Format products for preview
            $formattedProducts = [];
            foreach ($products as $product) {
                $formattedProducts[] = [
                    'item_code' => $product['item_code'],
                    'item_name' => $product['item_name'] ?? $product['name'],
                    'item_group' => $product['item_group'],
                    'standard_rate' => $product['standard_rate'] ?? 0,
                    'description' => $product['description'] ?? '',
                    'disabled' => $product['disabled'] ?? 0
                ];
            }

            return response()->json([
                'error' => false,
                'data' => $formattedProducts,
                'total' => count($formattedProducts),
                'message' => 'Products preview loaded successfully'
            ]);

        } catch (\Exception $e) {
            ERPNextLogger::error("Failed to preview products", [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'error' => true,
                'message' => 'Failed to preview products: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Import products from selected Item Groups
     */
    public function importProducts(ProductImportRequest $request): BaseHttpResponse
    {
        try {
            $selectedGroups = $request->input('item_groups', []);

            ERPNextLogger::info("Starting product import process", [
                'selected_groups' => $selectedGroups,
                'user_id' => Auth::check() ? Auth::id() : null
            ]);

            $importService = new ERPNextProductImportService();
            $results = $importService->importProducts($selectedGroups);

            // Prepare response message
            $message = sprintf(
                'Import completed! Total: %d, Imported: %d, Skipped: %d, Errors: %d',
                $results['total'],
                $results['imported'],
                $results['skipped'],
                $results['errors']
            );

            $responseType = $results['errors'] > 0 ? 'warning' : 'success';

            return $this
                ->httpResponse()
                ->setData(array_merge($results, [
                    'type' => $responseType,
                    'details' => $results['details']
                ]))
                ->setMessage($message);

        } catch (\Exception $e) {
            ERPNextLogger::error("Product import process failed", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this
                ->httpResponse()
                ->setError()
                ->setMessage('Import failed: ' . $e->getMessage());
        }
    }

    /**
     * Get import status/progress (for future AJAX polling)
     */
    public function getImportStatus(): JsonResponse
    {
        // This can be implemented later for real-time progress tracking
        // For now, return a simple response
        return response()->json([
            'error' => false,
            'status' => 'completed',
            'message' => 'Import status check not implemented yet'
        ]);
    }
}
